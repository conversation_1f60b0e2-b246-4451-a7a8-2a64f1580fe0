// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"cml_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addMaterial(RequestHeader header, Material material)")
	fmt.Fprintln(os.Stderr, "  bool updateMaterial(RequestHeader header, Material material)")
	fmt.Fprintln(os.<PERSON>r, "   getMaterialByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchMaterialByParams(RequestHeader header, MaterialParams params)")
	fmt.Fprintln(os.Stderr, "  bool checkMaterialNameIsExist(RequestHeader header, string name)")
	fmt.Fprintln(os.Stderr, "   getUploaders(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "   getAuthors(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "  i32 addCreative(RequestHeader header, Creative creative)")
	fmt.Fprintln(os.Stderr, "  bool updateCreative(RequestHeader header, Creative creative)")
	fmt.Fprintln(os.Stderr, "   categorizeSimilarCreative(RequestHeader header, string group_id,  creatives)")
	fmt.Fprintln(os.Stderr, "   categorizeSameCreative(RequestHeader header, string group_id,  creatives, CategorizeCreativeFiled field)")
	fmt.Fprintln(os.Stderr, "  void SetSimilarCreativeCache(RequestHeader header, string group_id,  creatives)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := cml_server.NewCmlServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addMaterial":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddMaterial requires 2 args")
			flag.Usage()
		}
		arg64 := flag.Arg(1)
		mbTrans65 := thrift.NewTMemoryBufferLen(len(arg64))
		defer mbTrans65.Close()
		_, err66 := mbTrans65.WriteString(arg64)
		if err66 != nil {
			Usage()
			return
		}
		factory67 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt68 := factory67.GetProtocol(mbTrans65)
		argvalue0 := cml_server.NewRequestHeader()
		err69 := argvalue0.Read(jsProt68)
		if err69 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg70 := flag.Arg(2)
		mbTrans71 := thrift.NewTMemoryBufferLen(len(arg70))
		defer mbTrans71.Close()
		_, err72 := mbTrans71.WriteString(arg70)
		if err72 != nil {
			Usage()
			return
		}
		factory73 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt74 := factory73.GetProtocol(mbTrans71)
		argvalue1 := cml_server.NewMaterial()
		err75 := argvalue1.Read(jsProt74)
		if err75 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddMaterial(value0, value1))
		fmt.Print("\n")
		break
	case "updateMaterial":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateMaterial requires 2 args")
			flag.Usage()
		}
		arg76 := flag.Arg(1)
		mbTrans77 := thrift.NewTMemoryBufferLen(len(arg76))
		defer mbTrans77.Close()
		_, err78 := mbTrans77.WriteString(arg76)
		if err78 != nil {
			Usage()
			return
		}
		factory79 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt80 := factory79.GetProtocol(mbTrans77)
		argvalue0 := cml_server.NewRequestHeader()
		err81 := argvalue0.Read(jsProt80)
		if err81 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg82 := flag.Arg(2)
		mbTrans83 := thrift.NewTMemoryBufferLen(len(arg82))
		defer mbTrans83.Close()
		_, err84 := mbTrans83.WriteString(arg82)
		if err84 != nil {
			Usage()
			return
		}
		factory85 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt86 := factory85.GetProtocol(mbTrans83)
		argvalue1 := cml_server.NewMaterial()
		err87 := argvalue1.Read(jsProt86)
		if err87 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateMaterial(value0, value1))
		fmt.Print("\n")
		break
	case "getMaterialByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMaterialByIds requires 2 args")
			flag.Usage()
		}
		arg88 := flag.Arg(1)
		mbTrans89 := thrift.NewTMemoryBufferLen(len(arg88))
		defer mbTrans89.Close()
		_, err90 := mbTrans89.WriteString(arg88)
		if err90 != nil {
			Usage()
			return
		}
		factory91 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt92 := factory91.GetProtocol(mbTrans89)
		argvalue0 := cml_server.NewRequestHeader()
		err93 := argvalue0.Read(jsProt92)
		if err93 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg94 := flag.Arg(2)
		mbTrans95 := thrift.NewTMemoryBufferLen(len(arg94))
		defer mbTrans95.Close()
		_, err96 := mbTrans95.WriteString(arg94)
		if err96 != nil {
			Usage()
			return
		}
		factory97 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt98 := factory97.GetProtocol(mbTrans95)
		containerStruct1 := cml_server.NewGetMaterialByIdsArgs()
		err99 := containerStruct1.ReadField2(jsProt98)
		if err99 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetMaterialByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchMaterialByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchMaterialByParams requires 2 args")
			flag.Usage()
		}
		arg100 := flag.Arg(1)
		mbTrans101 := thrift.NewTMemoryBufferLen(len(arg100))
		defer mbTrans101.Close()
		_, err102 := mbTrans101.WriteString(arg100)
		if err102 != nil {
			Usage()
			return
		}
		factory103 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt104 := factory103.GetProtocol(mbTrans101)
		argvalue0 := cml_server.NewRequestHeader()
		err105 := argvalue0.Read(jsProt104)
		if err105 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg106 := flag.Arg(2)
		mbTrans107 := thrift.NewTMemoryBufferLen(len(arg106))
		defer mbTrans107.Close()
		_, err108 := mbTrans107.WriteString(arg106)
		if err108 != nil {
			Usage()
			return
		}
		factory109 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt110 := factory109.GetProtocol(mbTrans107)
		argvalue1 := cml_server.NewMaterialParams()
		err111 := argvalue1.Read(jsProt110)
		if err111 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchMaterialByParams(value0, value1))
		fmt.Print("\n")
		break
	case "checkMaterialNameIsExist":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "CheckMaterialNameIsExist requires 2 args")
			flag.Usage()
		}
		arg112 := flag.Arg(1)
		mbTrans113 := thrift.NewTMemoryBufferLen(len(arg112))
		defer mbTrans113.Close()
		_, err114 := mbTrans113.WriteString(arg112)
		if err114 != nil {
			Usage()
			return
		}
		factory115 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt116 := factory115.GetProtocol(mbTrans113)
		argvalue0 := cml_server.NewRequestHeader()
		err117 := argvalue0.Read(jsProt116)
		if err117 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.CheckMaterialNameIsExist(value0, value1))
		fmt.Print("\n")
		break
	case "getUploaders":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetUploaders requires 1 args")
			flag.Usage()
		}
		arg119 := flag.Arg(1)
		mbTrans120 := thrift.NewTMemoryBufferLen(len(arg119))
		defer mbTrans120.Close()
		_, err121 := mbTrans120.WriteString(arg119)
		if err121 != nil {
			Usage()
			return
		}
		factory122 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt123 := factory122.GetProtocol(mbTrans120)
		argvalue0 := cml_server.NewRequestHeader()
		err124 := argvalue0.Read(jsProt123)
		if err124 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetUploaders(value0))
		fmt.Print("\n")
		break
	case "getAuthors":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetAuthors requires 1 args")
			flag.Usage()
		}
		arg125 := flag.Arg(1)
		mbTrans126 := thrift.NewTMemoryBufferLen(len(arg125))
		defer mbTrans126.Close()
		_, err127 := mbTrans126.WriteString(arg125)
		if err127 != nil {
			Usage()
			return
		}
		factory128 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt129 := factory128.GetProtocol(mbTrans126)
		argvalue0 := cml_server.NewRequestHeader()
		err130 := argvalue0.Read(jsProt129)
		if err130 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetAuthors(value0))
		fmt.Print("\n")
		break
	case "addCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCreative requires 2 args")
			flag.Usage()
		}
		arg131 := flag.Arg(1)
		mbTrans132 := thrift.NewTMemoryBufferLen(len(arg131))
		defer mbTrans132.Close()
		_, err133 := mbTrans132.WriteString(arg131)
		if err133 != nil {
			Usage()
			return
		}
		factory134 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt135 := factory134.GetProtocol(mbTrans132)
		argvalue0 := cml_server.NewRequestHeader()
		err136 := argvalue0.Read(jsProt135)
		if err136 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg137 := flag.Arg(2)
		mbTrans138 := thrift.NewTMemoryBufferLen(len(arg137))
		defer mbTrans138.Close()
		_, err139 := mbTrans138.WriteString(arg137)
		if err139 != nil {
			Usage()
			return
		}
		factory140 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt141 := factory140.GetProtocol(mbTrans138)
		argvalue1 := cml_server.NewCreative()
		err142 := argvalue1.Read(jsProt141)
		if err142 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCreative(value0, value1))
		fmt.Print("\n")
		break
	case "updateCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateCreative requires 2 args")
			flag.Usage()
		}
		arg143 := flag.Arg(1)
		mbTrans144 := thrift.NewTMemoryBufferLen(len(arg143))
		defer mbTrans144.Close()
		_, err145 := mbTrans144.WriteString(arg143)
		if err145 != nil {
			Usage()
			return
		}
		factory146 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt147 := factory146.GetProtocol(mbTrans144)
		argvalue0 := cml_server.NewRequestHeader()
		err148 := argvalue0.Read(jsProt147)
		if err148 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg149 := flag.Arg(2)
		mbTrans150 := thrift.NewTMemoryBufferLen(len(arg149))
		defer mbTrans150.Close()
		_, err151 := mbTrans150.WriteString(arg149)
		if err151 != nil {
			Usage()
			return
		}
		factory152 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt153 := factory152.GetProtocol(mbTrans150)
		argvalue1 := cml_server.NewCreative()
		err154 := argvalue1.Read(jsProt153)
		if err154 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateCreative(value0, value1))
		fmt.Print("\n")
		break
	case "categorizeSimilarCreative":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "CategorizeSimilarCreative requires 3 args")
			flag.Usage()
		}
		arg155 := flag.Arg(1)
		mbTrans156 := thrift.NewTMemoryBufferLen(len(arg155))
		defer mbTrans156.Close()
		_, err157 := mbTrans156.WriteString(arg155)
		if err157 != nil {
			Usage()
			return
		}
		factory158 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt159 := factory158.GetProtocol(mbTrans156)
		argvalue0 := cml_server.NewRequestHeader()
		err160 := argvalue0.Read(jsProt159)
		if err160 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg162 := flag.Arg(3)
		mbTrans163 := thrift.NewTMemoryBufferLen(len(arg162))
		defer mbTrans163.Close()
		_, err164 := mbTrans163.WriteString(arg162)
		if err164 != nil {
			Usage()
			return
		}
		factory165 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt166 := factory165.GetProtocol(mbTrans163)
		containerStruct2 := cml_server.NewCategorizeSimilarCreativeArgs()
		err167 := containerStruct2.ReadField3(jsProt166)
		if err167 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Creatives
		value2 := argvalue2
		fmt.Print(client.CategorizeSimilarCreative(value0, value1, value2))
		fmt.Print("\n")
		break
	case "categorizeSameCreative":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "CategorizeSameCreative requires 4 args")
			flag.Usage()
		}
		arg168 := flag.Arg(1)
		mbTrans169 := thrift.NewTMemoryBufferLen(len(arg168))
		defer mbTrans169.Close()
		_, err170 := mbTrans169.WriteString(arg168)
		if err170 != nil {
			Usage()
			return
		}
		factory171 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt172 := factory171.GetProtocol(mbTrans169)
		argvalue0 := cml_server.NewRequestHeader()
		err173 := argvalue0.Read(jsProt172)
		if err173 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg175 := flag.Arg(3)
		mbTrans176 := thrift.NewTMemoryBufferLen(len(arg175))
		defer mbTrans176.Close()
		_, err177 := mbTrans176.WriteString(arg175)
		if err177 != nil {
			Usage()
			return
		}
		factory178 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt179 := factory178.GetProtocol(mbTrans176)
		containerStruct2 := cml_server.NewCategorizeSameCreativeArgs()
		err180 := containerStruct2.ReadField3(jsProt179)
		if err180 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Creatives
		value2 := argvalue2
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := cml_server.CategorizeCreativeFiled(tmp3)
		value3 := argvalue3
		fmt.Print(client.CategorizeSameCreative(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "SetSimilarCreativeCache":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SetSimilarCreativeCache requires 3 args")
			flag.Usage()
		}
		arg181 := flag.Arg(1)
		mbTrans182 := thrift.NewTMemoryBufferLen(len(arg181))
		defer mbTrans182.Close()
		_, err183 := mbTrans182.WriteString(arg181)
		if err183 != nil {
			Usage()
			return
		}
		factory184 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt185 := factory184.GetProtocol(mbTrans182)
		argvalue0 := cml_server.NewRequestHeader()
		err186 := argvalue0.Read(jsProt185)
		if err186 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg188 := flag.Arg(3)
		mbTrans189 := thrift.NewTMemoryBufferLen(len(arg188))
		defer mbTrans189.Close()
		_, err190 := mbTrans189.WriteString(arg188)
		if err190 != nil {
			Usage()
			return
		}
		factory191 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt192 := factory191.GetProtocol(mbTrans189)
		containerStruct2 := cml_server.NewSetSimilarCreativeCacheArgs()
		err193 := containerStruct2.ReadField3(jsProt192)
		if err193 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Creatives
		value2 := argvalue2
		fmt.Print(client.SetSimilarCreativeCache(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
