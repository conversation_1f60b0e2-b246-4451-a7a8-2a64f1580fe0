// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package data_match_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/data_match_types"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/rtb_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = rtb_types.GoUnusedProtection__
var _ = data_match_types.GoUnusedProtection__

type DataMatcher interface {
	dm303.DomobService

	// 提交任务
	//
	// Parameters:
	//  - Task
	CommitTask(task *data_match_types.Task) (exp *data_match_types.DataMatchException, err error)
	// 获取任务的执行状态
	//
	// Parameters:
	//  - Task
	GetTaskStatus(task *data_match_types.Task) (r data_match_types.TaskStatus, exp *data_match_types.DataMatchException, err error)
	// 获取请求交易所量级匹配返回结果
	//
	// Parameters:
	//  - Task
	GetRtbReqExchange(task *data_match_types.Task) (r *data_match_types.RetExchange, exp *data_match_types.DataMatchException, err error)
	// 获取请求媒体量级匹配返回结果
	//
	// Parameters:
	//  - Task
	GetRtbReqMedia(task *data_match_types.Task) (r *data_match_types.RetMedia, exp *data_match_types.DataMatchException, err error)
	// 获取RTB活动激活匹配返回结果
	//
	// Parameters:
	//  - Task
	GetRtbActCampaign(task *data_match_types.Task) (r []*data_match_types.RetCampaign, exp *data_match_types.DataMatchException, err error)
	// 获取RTB交易所激活匹配返回结果
	//
	// Parameters:
	//  - Task
	GetRtbActExchange(task *data_match_types.Task) (r *data_match_types.RetExchange, exp *data_match_types.DataMatchException, err error)
	// 获取RTB媒体激活匹配返回结果
	//
	// Parameters:
	//  - Task
	GetRtbActMedia(task *data_match_types.Task) (r *data_match_types.RetMedia, exp *data_match_types.DataMatchException, err error)
	// 获取外采激活匹配返回结果
	//
	// Parameters:
	//  - Task
	GetDosAct(task *data_match_types.Task) (r *data_match_types.RetDos, exp *data_match_types.DataMatchException, err error)
	// 获取点击匹配活动返回结果
	//
	// Parameters:
	//  - Task
	GetRtbClkCampaign(task *data_match_types.Task) (r []*data_match_types.RetCampaign, exp *data_match_types.DataMatchException, err error)
	// 获取点击匹配交易所返回结果
	//
	// Parameters:
	//  - Task
	GetRtbClkExchange(task *data_match_types.Task) (r *data_match_types.RetExchange, exp *data_match_types.DataMatchException, err error)
	// 获取点击匹配媒体返回结果
	//
	// Parameters:
	//  - Task
	GetRtbClkMedia(task *data_match_types.Task) (r *data_match_types.RetMedia, exp *data_match_types.DataMatchException, err error)
	// 获取预估任务返回结果
	//
	// Parameters:
	//  - Task
	GetRetEstimation(task *data_match_types.Task) (r *data_match_types.RetEstimation, exp *data_match_types.DataMatchException, err error)
	// 设置任务状态
	//
	// Parameters:
	//  - Task
	//  - TaskStatus
	SetTaskStatus(task *data_match_types.Task, task_status data_match_types.TaskStatus) (r data_match_types.StatusCode, exp *data_match_types.DataMatchException, err error)
	// 获取任务
	//
	// Parameters:
	//  - MatchType
	GetTask(match_type data_match_types.MatchType) (r *data_match_types.Task, exp *data_match_types.DataMatchException, err error)
	// 批量获取任务
	//
	// Parameters:
	//  - MatchType
	GetTasks(match_type data_match_types.MatchType) (r *data_match_types.Tasks, exp *data_match_types.DataMatchException, err error)
}

type DataMatcherClient struct {
	*dm303.DomobServiceClient
}

func NewDataMatcherClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DataMatcherClient {
	return &DataMatcherClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewDataMatcherClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DataMatcherClient {
	return &DataMatcherClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// 提交任务
//
// Parameters:
//  - Task
func (p *DataMatcherClient) CommitTask(task *data_match_types.Task) (exp *data_match_types.DataMatchException, err error) {
	if err = p.sendCommitTask(task); err != nil {
		return
	}
	return p.recvCommitTask()
}

func (p *DataMatcherClient) sendCommitTask(task *data_match_types.Task) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("commit_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewCommitTaskArgs()
	args0.Task = task
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvCommitTask() (exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewCommitTaskResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result1.Exp != nil {
		exp = result1.Exp
	}
	return
}

// 获取任务的执行状态
//
// Parameters:
//  - Task
func (p *DataMatcherClient) GetTaskStatus(task *data_match_types.Task) (r data_match_types.TaskStatus, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetTaskStatus(task); err != nil {
		return
	}
	return p.recvGetTaskStatus()
}

func (p *DataMatcherClient) sendGetTaskStatus(task *data_match_types.Task) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_task_status", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetTaskStatusArgs()
	args4.Task = task
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetTaskStatus() (value data_match_types.TaskStatus, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetTaskStatusResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Exp != nil {
		exp = result5.Exp
	}
	return
}

// 获取请求交易所量级匹配返回结果
//
// Parameters:
//  - Task
func (p *DataMatcherClient) GetRtbReqExchange(task *data_match_types.Task) (r *data_match_types.RetExchange, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetRtbReqExchange(task); err != nil {
		return
	}
	return p.recvGetRtbReqExchange()
}

func (p *DataMatcherClient) sendGetRtbReqExchange(task *data_match_types.Task) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_rtb_req_exchange", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetRtbReqExchangeArgs()
	args8.Task = task
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetRtbReqExchange() (value *data_match_types.RetExchange, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetRtbReqExchangeResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Exp != nil {
		exp = result9.Exp
	}
	return
}

// 获取请求媒体量级匹配返回结果
//
// Parameters:
//  - Task
func (p *DataMatcherClient) GetRtbReqMedia(task *data_match_types.Task) (r *data_match_types.RetMedia, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetRtbReqMedia(task); err != nil {
		return
	}
	return p.recvGetRtbReqMedia()
}

func (p *DataMatcherClient) sendGetRtbReqMedia(task *data_match_types.Task) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_rtb_req_media", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetRtbReqMediaArgs()
	args12.Task = task
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetRtbReqMedia() (value *data_match_types.RetMedia, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetRtbReqMediaResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.Exp != nil {
		exp = result13.Exp
	}
	return
}

// 获取RTB活动激活匹配返回结果
//
// Parameters:
//  - Task
func (p *DataMatcherClient) GetRtbActCampaign(task *data_match_types.Task) (r []*data_match_types.RetCampaign, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetRtbActCampaign(task); err != nil {
		return
	}
	return p.recvGetRtbActCampaign()
}

func (p *DataMatcherClient) sendGetRtbActCampaign(task *data_match_types.Task) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_rtb_act_campaign", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewGetRtbActCampaignArgs()
	args16.Task = task
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetRtbActCampaign() (value []*data_match_types.RetCampaign, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewGetRtbActCampaignResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.Exp != nil {
		exp = result17.Exp
	}
	return
}

// 获取RTB交易所激活匹配返回结果
//
// Parameters:
//  - Task
func (p *DataMatcherClient) GetRtbActExchange(task *data_match_types.Task) (r *data_match_types.RetExchange, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetRtbActExchange(task); err != nil {
		return
	}
	return p.recvGetRtbActExchange()
}

func (p *DataMatcherClient) sendGetRtbActExchange(task *data_match_types.Task) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_rtb_act_exchange", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewGetRtbActExchangeArgs()
	args20.Task = task
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetRtbActExchange() (value *data_match_types.RetExchange, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewGetRtbActExchangeResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.Exp != nil {
		exp = result21.Exp
	}
	return
}

// 获取RTB媒体激活匹配返回结果
//
// Parameters:
//  - Task
func (p *DataMatcherClient) GetRtbActMedia(task *data_match_types.Task) (r *data_match_types.RetMedia, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetRtbActMedia(task); err != nil {
		return
	}
	return p.recvGetRtbActMedia()
}

func (p *DataMatcherClient) sendGetRtbActMedia(task *data_match_types.Task) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_rtb_act_media", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewGetRtbActMediaArgs()
	args24.Task = task
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetRtbActMedia() (value *data_match_types.RetMedia, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewGetRtbActMediaResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.Exp != nil {
		exp = result25.Exp
	}
	return
}

// 获取外采激活匹配返回结果
//
// Parameters:
//  - Task
func (p *DataMatcherClient) GetDosAct(task *data_match_types.Task) (r *data_match_types.RetDos, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetDosAct(task); err != nil {
		return
	}
	return p.recvGetDosAct()
}

func (p *DataMatcherClient) sendGetDosAct(task *data_match_types.Task) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_dos_act", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewGetDosActArgs()
	args28.Task = task
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetDosAct() (value *data_match_types.RetDos, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewGetDosActResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.Exp != nil {
		exp = result29.Exp
	}
	return
}

// 获取点击匹配活动返回结果
//
// Parameters:
//  - Task
func (p *DataMatcherClient) GetRtbClkCampaign(task *data_match_types.Task) (r []*data_match_types.RetCampaign, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetRtbClkCampaign(task); err != nil {
		return
	}
	return p.recvGetRtbClkCampaign()
}

func (p *DataMatcherClient) sendGetRtbClkCampaign(task *data_match_types.Task) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_rtb_clk_campaign", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewGetRtbClkCampaignArgs()
	args32.Task = task
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetRtbClkCampaign() (value []*data_match_types.RetCampaign, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewGetRtbClkCampaignResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.Exp != nil {
		exp = result33.Exp
	}
	return
}

// 获取点击匹配交易所返回结果
//
// Parameters:
//  - Task
func (p *DataMatcherClient) GetRtbClkExchange(task *data_match_types.Task) (r *data_match_types.RetExchange, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetRtbClkExchange(task); err != nil {
		return
	}
	return p.recvGetRtbClkExchange()
}

func (p *DataMatcherClient) sendGetRtbClkExchange(task *data_match_types.Task) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_rtb_clk_exchange", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewGetRtbClkExchangeArgs()
	args36.Task = task
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetRtbClkExchange() (value *data_match_types.RetExchange, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewGetRtbClkExchangeResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.Exp != nil {
		exp = result37.Exp
	}
	return
}

// 获取点击匹配媒体返回结果
//
// Parameters:
//  - Task
func (p *DataMatcherClient) GetRtbClkMedia(task *data_match_types.Task) (r *data_match_types.RetMedia, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetRtbClkMedia(task); err != nil {
		return
	}
	return p.recvGetRtbClkMedia()
}

func (p *DataMatcherClient) sendGetRtbClkMedia(task *data_match_types.Task) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_rtb_clk_media", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewGetRtbClkMediaArgs()
	args40.Task = task
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetRtbClkMedia() (value *data_match_types.RetMedia, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewGetRtbClkMediaResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	if result41.Exp != nil {
		exp = result41.Exp
	}
	return
}

// 获取预估任务返回结果
//
// Parameters:
//  - Task
func (p *DataMatcherClient) GetRetEstimation(task *data_match_types.Task) (r *data_match_types.RetEstimation, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetRetEstimation(task); err != nil {
		return
	}
	return p.recvGetRetEstimation()
}

func (p *DataMatcherClient) sendGetRetEstimation(task *data_match_types.Task) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_ret_estimation", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewGetRetEstimationArgs()
	args44.Task = task
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetRetEstimation() (value *data_match_types.RetEstimation, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewGetRetEstimationResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	if result45.Exp != nil {
		exp = result45.Exp
	}
	return
}

// 设置任务状态
//
// Parameters:
//  - Task
//  - TaskStatus
func (p *DataMatcherClient) SetTaskStatus(task *data_match_types.Task, task_status data_match_types.TaskStatus) (r data_match_types.StatusCode, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendSetTaskStatus(task, task_status); err != nil {
		return
	}
	return p.recvSetTaskStatus()
}

func (p *DataMatcherClient) sendSetTaskStatus(task *data_match_types.Task, task_status data_match_types.TaskStatus) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("set_task_status", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewSetTaskStatusArgs()
	args48.Task = task
	args48.TaskStatus = task_status
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvSetTaskStatus() (value data_match_types.StatusCode, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewSetTaskStatusResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result49.Success
	if result49.Exp != nil {
		exp = result49.Exp
	}
	return
}

// 获取任务
//
// Parameters:
//  - MatchType
func (p *DataMatcherClient) GetTask(match_type data_match_types.MatchType) (r *data_match_types.Task, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetTask(match_type); err != nil {
		return
	}
	return p.recvGetTask()
}

func (p *DataMatcherClient) sendGetTask(match_type data_match_types.MatchType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewGetTaskArgs()
	args52.MatchType = match_type
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetTask() (value *data_match_types.Task, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewGetTaskResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result53.Success
	if result53.Exp != nil {
		exp = result53.Exp
	}
	return
}

// 批量获取任务
//
// Parameters:
//  - MatchType
func (p *DataMatcherClient) GetTasks(match_type data_match_types.MatchType) (r *data_match_types.Tasks, exp *data_match_types.DataMatchException, err error) {
	if err = p.sendGetTasks(match_type); err != nil {
		return
	}
	return p.recvGetTasks()
}

func (p *DataMatcherClient) sendGetTasks(match_type data_match_types.MatchType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_tasks", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args56 := NewGetTasksArgs()
	args56.MatchType = match_type
	if err = args56.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataMatcherClient) recvGetTasks() (value *data_match_types.Tasks, exp *data_match_types.DataMatchException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error58 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error59 error
		error59, err = error58.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error59
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result57 := NewGetTasksResult()
	if err = result57.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result57.Success
	if result57.Exp != nil {
		exp = result57.Exp
	}
	return
}

type DataMatcherProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewDataMatcherProcessor(handler DataMatcher) *DataMatcherProcessor {
	self60 := &DataMatcherProcessor{dm303.NewDomobServiceProcessor(handler)}
	self60.AddToProcessorMap("commit_task", &dataMatcherProcessorCommitTask{handler: handler})
	self60.AddToProcessorMap("get_task_status", &dataMatcherProcessorGetTaskStatus{handler: handler})
	self60.AddToProcessorMap("get_rtb_req_exchange", &dataMatcherProcessorGetRtbReqExchange{handler: handler})
	self60.AddToProcessorMap("get_rtb_req_media", &dataMatcherProcessorGetRtbReqMedia{handler: handler})
	self60.AddToProcessorMap("get_rtb_act_campaign", &dataMatcherProcessorGetRtbActCampaign{handler: handler})
	self60.AddToProcessorMap("get_rtb_act_exchange", &dataMatcherProcessorGetRtbActExchange{handler: handler})
	self60.AddToProcessorMap("get_rtb_act_media", &dataMatcherProcessorGetRtbActMedia{handler: handler})
	self60.AddToProcessorMap("get_dos_act", &dataMatcherProcessorGetDosAct{handler: handler})
	self60.AddToProcessorMap("get_rtb_clk_campaign", &dataMatcherProcessorGetRtbClkCampaign{handler: handler})
	self60.AddToProcessorMap("get_rtb_clk_exchange", &dataMatcherProcessorGetRtbClkExchange{handler: handler})
	self60.AddToProcessorMap("get_rtb_clk_media", &dataMatcherProcessorGetRtbClkMedia{handler: handler})
	self60.AddToProcessorMap("get_ret_estimation", &dataMatcherProcessorGetRetEstimation{handler: handler})
	self60.AddToProcessorMap("set_task_status", &dataMatcherProcessorSetTaskStatus{handler: handler})
	self60.AddToProcessorMap("get_task", &dataMatcherProcessorGetTask{handler: handler})
	self60.AddToProcessorMap("get_tasks", &dataMatcherProcessorGetTasks{handler: handler})
	return self60
}

type dataMatcherProcessorCommitTask struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorCommitTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCommitTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("commit_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCommitTaskResult()
	if result.Exp, err = p.handler.CommitTask(args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing commit_task: "+err.Error())
		oprot.WriteMessageBegin("commit_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("commit_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetTaskStatus struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetTaskStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTaskStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_task_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTaskStatusResult()
	if result.Success, result.Exp, err = p.handler.GetTaskStatus(args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_task_status: "+err.Error())
		oprot.WriteMessageBegin("get_task_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_task_status", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetRtbReqExchange struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetRtbReqExchange) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRtbReqExchangeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_rtb_req_exchange", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRtbReqExchangeResult()
	if result.Success, result.Exp, err = p.handler.GetRtbReqExchange(args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_rtb_req_exchange: "+err.Error())
		oprot.WriteMessageBegin("get_rtb_req_exchange", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_rtb_req_exchange", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetRtbReqMedia struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetRtbReqMedia) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRtbReqMediaArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_rtb_req_media", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRtbReqMediaResult()
	if result.Success, result.Exp, err = p.handler.GetRtbReqMedia(args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_rtb_req_media: "+err.Error())
		oprot.WriteMessageBegin("get_rtb_req_media", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_rtb_req_media", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetRtbActCampaign struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetRtbActCampaign) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRtbActCampaignArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_rtb_act_campaign", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRtbActCampaignResult()
	if result.Success, result.Exp, err = p.handler.GetRtbActCampaign(args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_rtb_act_campaign: "+err.Error())
		oprot.WriteMessageBegin("get_rtb_act_campaign", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_rtb_act_campaign", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetRtbActExchange struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetRtbActExchange) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRtbActExchangeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_rtb_act_exchange", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRtbActExchangeResult()
	if result.Success, result.Exp, err = p.handler.GetRtbActExchange(args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_rtb_act_exchange: "+err.Error())
		oprot.WriteMessageBegin("get_rtb_act_exchange", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_rtb_act_exchange", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetRtbActMedia struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetRtbActMedia) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRtbActMediaArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_rtb_act_media", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRtbActMediaResult()
	if result.Success, result.Exp, err = p.handler.GetRtbActMedia(args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_rtb_act_media: "+err.Error())
		oprot.WriteMessageBegin("get_rtb_act_media", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_rtb_act_media", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetDosAct struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetDosAct) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetDosActArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_dos_act", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetDosActResult()
	if result.Success, result.Exp, err = p.handler.GetDosAct(args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_dos_act: "+err.Error())
		oprot.WriteMessageBegin("get_dos_act", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_dos_act", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetRtbClkCampaign struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetRtbClkCampaign) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRtbClkCampaignArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_rtb_clk_campaign", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRtbClkCampaignResult()
	if result.Success, result.Exp, err = p.handler.GetRtbClkCampaign(args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_rtb_clk_campaign: "+err.Error())
		oprot.WriteMessageBegin("get_rtb_clk_campaign", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_rtb_clk_campaign", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetRtbClkExchange struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetRtbClkExchange) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRtbClkExchangeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_rtb_clk_exchange", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRtbClkExchangeResult()
	if result.Success, result.Exp, err = p.handler.GetRtbClkExchange(args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_rtb_clk_exchange: "+err.Error())
		oprot.WriteMessageBegin("get_rtb_clk_exchange", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_rtb_clk_exchange", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetRtbClkMedia struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetRtbClkMedia) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRtbClkMediaArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_rtb_clk_media", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRtbClkMediaResult()
	if result.Success, result.Exp, err = p.handler.GetRtbClkMedia(args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_rtb_clk_media: "+err.Error())
		oprot.WriteMessageBegin("get_rtb_clk_media", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_rtb_clk_media", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetRetEstimation struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetRetEstimation) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRetEstimationArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_ret_estimation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRetEstimationResult()
	if result.Success, result.Exp, err = p.handler.GetRetEstimation(args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_ret_estimation: "+err.Error())
		oprot.WriteMessageBegin("get_ret_estimation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_ret_estimation", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorSetTaskStatus struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorSetTaskStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSetTaskStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("set_task_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSetTaskStatusResult()
	if result.Success, result.Exp, err = p.handler.SetTaskStatus(args.Task, args.TaskStatus); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing set_task_status: "+err.Error())
		oprot.WriteMessageBegin("set_task_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("set_task_status", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetTask struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTaskResult()
	if result.Success, result.Exp, err = p.handler.GetTask(args.MatchType); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_task: "+err.Error())
		oprot.WriteMessageBegin("get_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataMatcherProcessorGetTasks struct {
	handler DataMatcher
}

func (p *dataMatcherProcessorGetTasks) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTasksArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_tasks", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTasksResult()
	if result.Success, result.Exp, err = p.handler.GetTasks(args.MatchType); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_tasks: "+err.Error())
		oprot.WriteMessageBegin("get_tasks", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_tasks", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type CommitTaskArgs struct {
	Task *data_match_types.Task `thrift:"task,1" json:"task"`
}

func NewCommitTaskArgs() *CommitTaskArgs {
	return &CommitTaskArgs{}
}

func (p *CommitTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CommitTaskArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *CommitTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("commit_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CommitTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *CommitTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommitTaskArgs(%+v)", *p)
}

type CommitTaskResult struct {
	Exp *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewCommitTaskResult() *CommitTaskResult {
	return &CommitTaskResult{}
}

func (p *CommitTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CommitTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *CommitTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("commit_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CommitTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *CommitTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommitTaskResult(%+v)", *p)
}

type GetTaskStatusArgs struct {
	Task *data_match_types.Task `thrift:"task,1" json:"task"`
}

func NewGetTaskStatusArgs() *GetTaskStatusArgs {
	return &GetTaskStatusArgs{}
}

func (p *GetTaskStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTaskStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *GetTaskStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_task_status_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTaskStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTaskStatusArgs(%+v)", *p)
}

type GetTaskStatusResult struct {
	Success data_match_types.TaskStatus          `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetTaskStatusResult() *GetTaskStatusResult {
	return &GetTaskStatusResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetTaskStatusResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *GetTaskStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTaskStatusResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = data_match_types.TaskStatus(v)
	}
	return nil
}

func (p *GetTaskStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetTaskStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_task_status_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTaskStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTaskStatusResult(%+v)", *p)
}

type GetRtbReqExchangeArgs struct {
	Task *data_match_types.Task `thrift:"task,1" json:"task"`
}

func NewGetRtbReqExchangeArgs() *GetRtbReqExchangeArgs {
	return &GetRtbReqExchangeArgs{}
}

func (p *GetRtbReqExchangeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbReqExchangeArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *GetRtbReqExchangeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_req_exchange_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbReqExchangeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbReqExchangeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbReqExchangeArgs(%+v)", *p)
}

type GetRtbReqExchangeResult struct {
	Success *data_match_types.RetExchange        `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetRtbReqExchangeResult() *GetRtbReqExchangeResult {
	return &GetRtbReqExchangeResult{}
}

func (p *GetRtbReqExchangeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbReqExchangeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = data_match_types.NewRetExchange()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetRtbReqExchangeResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetRtbReqExchangeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_req_exchange_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbReqExchangeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbReqExchangeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbReqExchangeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbReqExchangeResult(%+v)", *p)
}

type GetRtbReqMediaArgs struct {
	Task *data_match_types.Task `thrift:"task,1" json:"task"`
}

func NewGetRtbReqMediaArgs() *GetRtbReqMediaArgs {
	return &GetRtbReqMediaArgs{}
}

func (p *GetRtbReqMediaArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbReqMediaArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *GetRtbReqMediaArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_req_media_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbReqMediaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbReqMediaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbReqMediaArgs(%+v)", *p)
}

type GetRtbReqMediaResult struct {
	Success *data_match_types.RetMedia           `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetRtbReqMediaResult() *GetRtbReqMediaResult {
	return &GetRtbReqMediaResult{}
}

func (p *GetRtbReqMediaResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbReqMediaResult) readField0(iprot thrift.TProtocol) error {
	p.Success = data_match_types.NewRetMedia()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetRtbReqMediaResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetRtbReqMediaResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_req_media_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbReqMediaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbReqMediaResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbReqMediaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbReqMediaResult(%+v)", *p)
}

type GetRtbActCampaignArgs struct {
	Task *data_match_types.Task `thrift:"task,1" json:"task"`
}

func NewGetRtbActCampaignArgs() *GetRtbActCampaignArgs {
	return &GetRtbActCampaignArgs{}
}

func (p *GetRtbActCampaignArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbActCampaignArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *GetRtbActCampaignArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_act_campaign_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbActCampaignArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbActCampaignArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbActCampaignArgs(%+v)", *p)
}

type GetRtbActCampaignResult struct {
	Success []*data_match_types.RetCampaign      `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetRtbActCampaignResult() *GetRtbActCampaignResult {
	return &GetRtbActCampaignResult{}
}

func (p *GetRtbActCampaignResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbActCampaignResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*data_match_types.RetCampaign, 0, size)
	for i := 0; i < size; i++ {
		_elem61 := data_match_types.NewRetCampaign()
		if err := _elem61.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem61)
		}
		p.Success = append(p.Success, _elem61)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetRtbActCampaignResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetRtbActCampaignResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_act_campaign_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbActCampaignResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbActCampaignResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbActCampaignResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbActCampaignResult(%+v)", *p)
}

type GetRtbActExchangeArgs struct {
	Task *data_match_types.Task `thrift:"task,1" json:"task"`
}

func NewGetRtbActExchangeArgs() *GetRtbActExchangeArgs {
	return &GetRtbActExchangeArgs{}
}

func (p *GetRtbActExchangeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbActExchangeArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *GetRtbActExchangeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_act_exchange_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbActExchangeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbActExchangeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbActExchangeArgs(%+v)", *p)
}

type GetRtbActExchangeResult struct {
	Success *data_match_types.RetExchange        `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetRtbActExchangeResult() *GetRtbActExchangeResult {
	return &GetRtbActExchangeResult{}
}

func (p *GetRtbActExchangeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbActExchangeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = data_match_types.NewRetExchange()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetRtbActExchangeResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetRtbActExchangeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_act_exchange_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbActExchangeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbActExchangeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbActExchangeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbActExchangeResult(%+v)", *p)
}

type GetRtbActMediaArgs struct {
	Task *data_match_types.Task `thrift:"task,1" json:"task"`
}

func NewGetRtbActMediaArgs() *GetRtbActMediaArgs {
	return &GetRtbActMediaArgs{}
}

func (p *GetRtbActMediaArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbActMediaArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *GetRtbActMediaArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_act_media_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbActMediaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbActMediaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbActMediaArgs(%+v)", *p)
}

type GetRtbActMediaResult struct {
	Success *data_match_types.RetMedia           `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetRtbActMediaResult() *GetRtbActMediaResult {
	return &GetRtbActMediaResult{}
}

func (p *GetRtbActMediaResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbActMediaResult) readField0(iprot thrift.TProtocol) error {
	p.Success = data_match_types.NewRetMedia()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetRtbActMediaResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetRtbActMediaResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_act_media_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbActMediaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbActMediaResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbActMediaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbActMediaResult(%+v)", *p)
}

type GetDosActArgs struct {
	Task *data_match_types.Task `thrift:"task,1" json:"task"`
}

func NewGetDosActArgs() *GetDosActArgs {
	return &GetDosActArgs{}
}

func (p *GetDosActArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDosActArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *GetDosActArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_dos_act_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDosActArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *GetDosActArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDosActArgs(%+v)", *p)
}

type GetDosActResult struct {
	Success *data_match_types.RetDos             `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetDosActResult() *GetDosActResult {
	return &GetDosActResult{}
}

func (p *GetDosActResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDosActResult) readField0(iprot thrift.TProtocol) error {
	p.Success = data_match_types.NewRetDos()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetDosActResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetDosActResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_dos_act_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDosActResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetDosActResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetDosActResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDosActResult(%+v)", *p)
}

type GetRtbClkCampaignArgs struct {
	Task *data_match_types.Task `thrift:"task,1" json:"task"`
}

func NewGetRtbClkCampaignArgs() *GetRtbClkCampaignArgs {
	return &GetRtbClkCampaignArgs{}
}

func (p *GetRtbClkCampaignArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbClkCampaignArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *GetRtbClkCampaignArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_clk_campaign_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbClkCampaignArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbClkCampaignArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbClkCampaignArgs(%+v)", *p)
}

type GetRtbClkCampaignResult struct {
	Success []*data_match_types.RetCampaign      `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetRtbClkCampaignResult() *GetRtbClkCampaignResult {
	return &GetRtbClkCampaignResult{}
}

func (p *GetRtbClkCampaignResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbClkCampaignResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*data_match_types.RetCampaign, 0, size)
	for i := 0; i < size; i++ {
		_elem62 := data_match_types.NewRetCampaign()
		if err := _elem62.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem62)
		}
		p.Success = append(p.Success, _elem62)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetRtbClkCampaignResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetRtbClkCampaignResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_clk_campaign_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbClkCampaignResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbClkCampaignResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbClkCampaignResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbClkCampaignResult(%+v)", *p)
}

type GetRtbClkExchangeArgs struct {
	Task *data_match_types.Task `thrift:"task,1" json:"task"`
}

func NewGetRtbClkExchangeArgs() *GetRtbClkExchangeArgs {
	return &GetRtbClkExchangeArgs{}
}

func (p *GetRtbClkExchangeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbClkExchangeArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *GetRtbClkExchangeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_clk_exchange_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbClkExchangeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbClkExchangeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbClkExchangeArgs(%+v)", *p)
}

type GetRtbClkExchangeResult struct {
	Success *data_match_types.RetExchange        `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetRtbClkExchangeResult() *GetRtbClkExchangeResult {
	return &GetRtbClkExchangeResult{}
}

func (p *GetRtbClkExchangeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbClkExchangeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = data_match_types.NewRetExchange()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetRtbClkExchangeResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetRtbClkExchangeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_clk_exchange_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbClkExchangeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbClkExchangeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbClkExchangeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbClkExchangeResult(%+v)", *p)
}

type GetRtbClkMediaArgs struct {
	Task *data_match_types.Task `thrift:"task,1" json:"task"`
}

func NewGetRtbClkMediaArgs() *GetRtbClkMediaArgs {
	return &GetRtbClkMediaArgs{}
}

func (p *GetRtbClkMediaArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbClkMediaArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *GetRtbClkMediaArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_clk_media_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbClkMediaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbClkMediaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbClkMediaArgs(%+v)", *p)
}

type GetRtbClkMediaResult struct {
	Success *data_match_types.RetMedia           `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetRtbClkMediaResult() *GetRtbClkMediaResult {
	return &GetRtbClkMediaResult{}
}

func (p *GetRtbClkMediaResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRtbClkMediaResult) readField0(iprot thrift.TProtocol) error {
	p.Success = data_match_types.NewRetMedia()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetRtbClkMediaResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetRtbClkMediaResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_rtb_clk_media_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRtbClkMediaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbClkMediaResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetRtbClkMediaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRtbClkMediaResult(%+v)", *p)
}

type GetRetEstimationArgs struct {
	Task *data_match_types.Task `thrift:"task,1" json:"task"`
}

func NewGetRetEstimationArgs() *GetRetEstimationArgs {
	return &GetRetEstimationArgs{}
}

func (p *GetRetEstimationArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRetEstimationArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *GetRetEstimationArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_ret_estimation_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRetEstimationArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *GetRetEstimationArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRetEstimationArgs(%+v)", *p)
}

type GetRetEstimationResult struct {
	Success *data_match_types.RetEstimation      `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetRetEstimationResult() *GetRetEstimationResult {
	return &GetRetEstimationResult{}
}

func (p *GetRetEstimationResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRetEstimationResult) readField0(iprot thrift.TProtocol) error {
	p.Success = data_match_types.NewRetEstimation()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetRetEstimationResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetRetEstimationResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_ret_estimation_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRetEstimationResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRetEstimationResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetRetEstimationResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRetEstimationResult(%+v)", *p)
}

type SetTaskStatusArgs struct {
	Task       *data_match_types.Task      `thrift:"task,1" json:"task"`
	TaskStatus data_match_types.TaskStatus `thrift:"task_status,2" json:"task_status"`
}

func NewSetTaskStatusArgs() *SetTaskStatusArgs {
	return &SetTaskStatusArgs{
		TaskStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SetTaskStatusArgs) IsSetTaskStatus() bool {
	return int64(p.TaskStatus) != math.MinInt32-1
}

func (p *SetTaskStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SetTaskStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Task = data_match_types.NewTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *SetTaskStatusArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskStatus = data_match_types.TaskStatus(v)
	}
	return nil
}

func (p *SetTaskStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("set_task_status_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SetTaskStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task: %s", p, err)
		}
	}
	return err
}

func (p *SetTaskStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskStatus() {
		if err := oprot.WriteFieldBegin("task_status", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:task_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TaskStatus)); err != nil {
			return fmt.Errorf("%T.task_status (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:task_status: %s", p, err)
		}
	}
	return err
}

func (p *SetTaskStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetTaskStatusArgs(%+v)", *p)
}

type SetTaskStatusResult struct {
	Success data_match_types.StatusCode          `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewSetTaskStatusResult() *SetTaskStatusResult {
	return &SetTaskStatusResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SetTaskStatusResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *SetTaskStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SetTaskStatusResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = data_match_types.StatusCode(v)
	}
	return nil
}

func (p *SetTaskStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *SetTaskStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("set_task_status_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SetTaskStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SetTaskStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *SetTaskStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetTaskStatusResult(%+v)", *p)
}

type GetTaskArgs struct {
	MatchType data_match_types.MatchType `thrift:"match_type,1" json:"match_type"`
}

func NewGetTaskArgs() *GetTaskArgs {
	return &GetTaskArgs{
		MatchType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetTaskArgs) IsSetMatchType() bool {
	return int64(p.MatchType) != math.MinInt32-1
}

func (p *GetTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTaskArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MatchType = data_match_types.MatchType(v)
	}
	return nil
}

func (p *GetTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMatchType() {
		if err := oprot.WriteFieldBegin("match_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:match_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.MatchType)); err != nil {
			return fmt.Errorf("%T.match_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:match_type: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTaskArgs(%+v)", *p)
}

type GetTaskResult struct {
	Success *data_match_types.Task               `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetTaskResult() *GetTaskResult {
	return &GetTaskResult{}
}

func (p *GetTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTaskResult) readField0(iprot thrift.TProtocol) error {
	p.Success = data_match_types.NewTask()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTaskResult(%+v)", *p)
}

type GetTasksArgs struct {
	MatchType data_match_types.MatchType `thrift:"match_type,1" json:"match_type"`
}

func NewGetTasksArgs() *GetTasksArgs {
	return &GetTasksArgs{
		MatchType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetTasksArgs) IsSetMatchType() bool {
	return int64(p.MatchType) != math.MinInt32-1
}

func (p *GetTasksArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTasksArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MatchType = data_match_types.MatchType(v)
	}
	return nil
}

func (p *GetTasksArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_tasks_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTasksArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMatchType() {
		if err := oprot.WriteFieldBegin("match_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:match_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.MatchType)); err != nil {
			return fmt.Errorf("%T.match_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:match_type: %s", p, err)
		}
	}
	return err
}

func (p *GetTasksArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTasksArgs(%+v)", *p)
}

type GetTasksResult struct {
	Success *data_match_types.Tasks              `thrift:"success,0" json:"success"`
	Exp     *data_match_types.DataMatchException `thrift:"exp,1" json:"exp"`
}

func NewGetTasksResult() *GetTasksResult {
	return &GetTasksResult{}
}

func (p *GetTasksResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTasksResult) readField0(iprot thrift.TProtocol) error {
	p.Success = data_match_types.NewTasks()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetTasksResult) readField1(iprot thrift.TProtocol) error {
	p.Exp = data_match_types.NewDataMatchException()
	if err := p.Exp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Exp)
	}
	return nil
}

func (p *GetTasksResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_tasks_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Exp != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTasksResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTasksResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Exp != nil {
		if err := oprot.WriteFieldBegin("exp", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:exp: %s", p, err)
		}
		if err := p.Exp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Exp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:exp: %s", p, err)
		}
	}
	return err
}

func (p *GetTasksResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTasksResult(%+v)", *p)
}
