// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"data_match_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  void commit_task(Task task)")
	fmt.Fprintln(os.Stderr, "  TaskStatus get_task_status(Task task)")
	fmt.Fprintln(os.Stderr, "  RetExchange get_rtb_req_exchange(Task task)")
	fmt.Fprintln(os.<PERSON>r, "  RetMedia get_rtb_req_media(Task task)")
	fmt.Fprintln(os.Stderr, "   get_rtb_act_campaign(Task task)")
	fmt.Fprintln(os.Stderr, "  RetExchange get_rtb_act_exchange(Task task)")
	fmt.Fprintln(os.Stderr, "  RetMedia get_rtb_act_media(Task task)")
	fmt.Fprintln(os.Stderr, "  RetDos get_dos_act(Task task)")
	fmt.Fprintln(os.Stderr, "   get_rtb_clk_campaign(Task task)")
	fmt.Fprintln(os.Stderr, "  RetExchange get_rtb_clk_exchange(Task task)")
	fmt.Fprintln(os.Stderr, "  RetMedia get_rtb_clk_media(Task task)")
	fmt.Fprintln(os.Stderr, "  RetEstimation get_ret_estimation(Task task)")
	fmt.Fprintln(os.Stderr, "  StatusCode set_task_status(Task task, TaskStatus task_status)")
	fmt.Fprintln(os.Stderr, "  Task get_task(MatchType match_type)")
	fmt.Fprintln(os.Stderr, "  Tasks get_tasks(MatchType match_type)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := data_match_server.NewDataMatcherClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "commit_task":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "CommitTask requires 1 args")
			flag.Usage()
		}
		arg63 := flag.Arg(1)
		mbTrans64 := thrift.NewTMemoryBufferLen(len(arg63))
		defer mbTrans64.Close()
		_, err65 := mbTrans64.WriteString(arg63)
		if err65 != nil {
			Usage()
			return
		}
		factory66 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt67 := factory66.GetProtocol(mbTrans64)
		argvalue0 := data_match_server.NewTask()
		err68 := argvalue0.Read(jsProt67)
		if err68 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.CommitTask(value0))
		fmt.Print("\n")
		break
	case "get_task_status":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetTaskStatus requires 1 args")
			flag.Usage()
		}
		arg69 := flag.Arg(1)
		mbTrans70 := thrift.NewTMemoryBufferLen(len(arg69))
		defer mbTrans70.Close()
		_, err71 := mbTrans70.WriteString(arg69)
		if err71 != nil {
			Usage()
			return
		}
		factory72 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt73 := factory72.GetProtocol(mbTrans70)
		argvalue0 := data_match_server.NewTask()
		err74 := argvalue0.Read(jsProt73)
		if err74 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetTaskStatus(value0))
		fmt.Print("\n")
		break
	case "get_rtb_req_exchange":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetRtbReqExchange requires 1 args")
			flag.Usage()
		}
		arg75 := flag.Arg(1)
		mbTrans76 := thrift.NewTMemoryBufferLen(len(arg75))
		defer mbTrans76.Close()
		_, err77 := mbTrans76.WriteString(arg75)
		if err77 != nil {
			Usage()
			return
		}
		factory78 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt79 := factory78.GetProtocol(mbTrans76)
		argvalue0 := data_match_server.NewTask()
		err80 := argvalue0.Read(jsProt79)
		if err80 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetRtbReqExchange(value0))
		fmt.Print("\n")
		break
	case "get_rtb_req_media":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetRtbReqMedia requires 1 args")
			flag.Usage()
		}
		arg81 := flag.Arg(1)
		mbTrans82 := thrift.NewTMemoryBufferLen(len(arg81))
		defer mbTrans82.Close()
		_, err83 := mbTrans82.WriteString(arg81)
		if err83 != nil {
			Usage()
			return
		}
		factory84 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt85 := factory84.GetProtocol(mbTrans82)
		argvalue0 := data_match_server.NewTask()
		err86 := argvalue0.Read(jsProt85)
		if err86 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetRtbReqMedia(value0))
		fmt.Print("\n")
		break
	case "get_rtb_act_campaign":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetRtbActCampaign requires 1 args")
			flag.Usage()
		}
		arg87 := flag.Arg(1)
		mbTrans88 := thrift.NewTMemoryBufferLen(len(arg87))
		defer mbTrans88.Close()
		_, err89 := mbTrans88.WriteString(arg87)
		if err89 != nil {
			Usage()
			return
		}
		factory90 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt91 := factory90.GetProtocol(mbTrans88)
		argvalue0 := data_match_server.NewTask()
		err92 := argvalue0.Read(jsProt91)
		if err92 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetRtbActCampaign(value0))
		fmt.Print("\n")
		break
	case "get_rtb_act_exchange":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetRtbActExchange requires 1 args")
			flag.Usage()
		}
		arg93 := flag.Arg(1)
		mbTrans94 := thrift.NewTMemoryBufferLen(len(arg93))
		defer mbTrans94.Close()
		_, err95 := mbTrans94.WriteString(arg93)
		if err95 != nil {
			Usage()
			return
		}
		factory96 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt97 := factory96.GetProtocol(mbTrans94)
		argvalue0 := data_match_server.NewTask()
		err98 := argvalue0.Read(jsProt97)
		if err98 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetRtbActExchange(value0))
		fmt.Print("\n")
		break
	case "get_rtb_act_media":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetRtbActMedia requires 1 args")
			flag.Usage()
		}
		arg99 := flag.Arg(1)
		mbTrans100 := thrift.NewTMemoryBufferLen(len(arg99))
		defer mbTrans100.Close()
		_, err101 := mbTrans100.WriteString(arg99)
		if err101 != nil {
			Usage()
			return
		}
		factory102 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt103 := factory102.GetProtocol(mbTrans100)
		argvalue0 := data_match_server.NewTask()
		err104 := argvalue0.Read(jsProt103)
		if err104 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetRtbActMedia(value0))
		fmt.Print("\n")
		break
	case "get_dos_act":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetDosAct requires 1 args")
			flag.Usage()
		}
		arg105 := flag.Arg(1)
		mbTrans106 := thrift.NewTMemoryBufferLen(len(arg105))
		defer mbTrans106.Close()
		_, err107 := mbTrans106.WriteString(arg105)
		if err107 != nil {
			Usage()
			return
		}
		factory108 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt109 := factory108.GetProtocol(mbTrans106)
		argvalue0 := data_match_server.NewTask()
		err110 := argvalue0.Read(jsProt109)
		if err110 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetDosAct(value0))
		fmt.Print("\n")
		break
	case "get_rtb_clk_campaign":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetRtbClkCampaign requires 1 args")
			flag.Usage()
		}
		arg111 := flag.Arg(1)
		mbTrans112 := thrift.NewTMemoryBufferLen(len(arg111))
		defer mbTrans112.Close()
		_, err113 := mbTrans112.WriteString(arg111)
		if err113 != nil {
			Usage()
			return
		}
		factory114 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt115 := factory114.GetProtocol(mbTrans112)
		argvalue0 := data_match_server.NewTask()
		err116 := argvalue0.Read(jsProt115)
		if err116 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetRtbClkCampaign(value0))
		fmt.Print("\n")
		break
	case "get_rtb_clk_exchange":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetRtbClkExchange requires 1 args")
			flag.Usage()
		}
		arg117 := flag.Arg(1)
		mbTrans118 := thrift.NewTMemoryBufferLen(len(arg117))
		defer mbTrans118.Close()
		_, err119 := mbTrans118.WriteString(arg117)
		if err119 != nil {
			Usage()
			return
		}
		factory120 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt121 := factory120.GetProtocol(mbTrans118)
		argvalue0 := data_match_server.NewTask()
		err122 := argvalue0.Read(jsProt121)
		if err122 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetRtbClkExchange(value0))
		fmt.Print("\n")
		break
	case "get_rtb_clk_media":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetRtbClkMedia requires 1 args")
			flag.Usage()
		}
		arg123 := flag.Arg(1)
		mbTrans124 := thrift.NewTMemoryBufferLen(len(arg123))
		defer mbTrans124.Close()
		_, err125 := mbTrans124.WriteString(arg123)
		if err125 != nil {
			Usage()
			return
		}
		factory126 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt127 := factory126.GetProtocol(mbTrans124)
		argvalue0 := data_match_server.NewTask()
		err128 := argvalue0.Read(jsProt127)
		if err128 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetRtbClkMedia(value0))
		fmt.Print("\n")
		break
	case "get_ret_estimation":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetRetEstimation requires 1 args")
			flag.Usage()
		}
		arg129 := flag.Arg(1)
		mbTrans130 := thrift.NewTMemoryBufferLen(len(arg129))
		defer mbTrans130.Close()
		_, err131 := mbTrans130.WriteString(arg129)
		if err131 != nil {
			Usage()
			return
		}
		factory132 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt133 := factory132.GetProtocol(mbTrans130)
		argvalue0 := data_match_server.NewTask()
		err134 := argvalue0.Read(jsProt133)
		if err134 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetRetEstimation(value0))
		fmt.Print("\n")
		break
	case "set_task_status":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetTaskStatus requires 2 args")
			flag.Usage()
		}
		arg135 := flag.Arg(1)
		mbTrans136 := thrift.NewTMemoryBufferLen(len(arg135))
		defer mbTrans136.Close()
		_, err137 := mbTrans136.WriteString(arg135)
		if err137 != nil {
			Usage()
			return
		}
		factory138 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt139 := factory138.GetProtocol(mbTrans136)
		argvalue0 := data_match_server.NewTask()
		err140 := argvalue0.Read(jsProt139)
		if err140 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := data_match_server.TaskStatus(tmp1)
		value1 := argvalue1
		fmt.Print(client.SetTaskStatus(value0, value1))
		fmt.Print("\n")
		break
	case "get_task":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetTask requires 1 args")
			flag.Usage()
		}
		tmp0, err := (strconv.Atoi(flag.Arg(1)))
		if err != nil {
			Usage()
			return
		}
		argvalue0 := data_match_server.MatchType(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetTask(value0))
		fmt.Print("\n")
		break
	case "get_tasks":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetTasks requires 1 args")
			flag.Usage()
		}
		tmp0, err := (strconv.Atoi(flag.Arg(1)))
		if err != nil {
			Usage()
			return
		}
		argvalue0 := data_match_server.MatchType(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetTasks(value0))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err145 := (strconv.Atoi(flag.Arg(1)))
		if err145 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
