// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package workflow_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//查询条件，实例完成情况
type QueryConditionInstanceStatus int64

const (
	QueryConditionInstanceStatus_QCIS_UNFINISHED QueryConditionInstanceStatus = 0
	QueryConditionInstanceStatus_QCIS_ALL        QueryConditionInstanceStatus = 1
	QueryConditionInstanceStatus_QCIS_FINISHED   QueryConditionInstanceStatus = 2
)

func (p QueryConditionInstanceStatus) String() string {
	switch p {
	case QueryConditionInstanceStatus_QCIS_UNFINISHED:
		return "QueryConditionInstanceStatus_QCIS_UNFINISHED"
	case QueryConditionInstanceStatus_QCIS_ALL:
		return "QueryConditionInstanceStatus_QCIS_ALL"
	case QueryConditionInstanceStatus_QCIS_FINISHED:
		return "QueryConditionInstanceStatus_QCIS_FINISHED"
	}
	return "<UNSET>"
}

func QueryConditionInstanceStatusFromString(s string) (QueryConditionInstanceStatus, error) {
	switch s {
	case "QueryConditionInstanceStatus_QCIS_UNFINISHED":
		return QueryConditionInstanceStatus_QCIS_UNFINISHED, nil
	case "QueryConditionInstanceStatus_QCIS_ALL":
		return QueryConditionInstanceStatus_QCIS_ALL, nil
	case "QueryConditionInstanceStatus_QCIS_FINISHED":
		return QueryConditionInstanceStatus_QCIS_FINISHED, nil
	}
	return QueryConditionInstanceStatus(math.MinInt32 - 1), fmt.Errorf("not a valid QueryConditionInstanceStatus string")
}

//查询条件，用户任务的筛选条件
type QueryConditionRoleType int64

const (
	QueryConditionRoleType_QCRT_ASSIGNEE        QueryConditionRoleType = 1
	QueryConditionRoleType_QCRT_CANDIDATE_USER  QueryConditionRoleType = 2
	QueryConditionRoleType_QCRT_CANDIDATE_GROUP QueryConditionRoleType = 3
)

func (p QueryConditionRoleType) String() string {
	switch p {
	case QueryConditionRoleType_QCRT_ASSIGNEE:
		return "QueryConditionRoleType_QCRT_ASSIGNEE"
	case QueryConditionRoleType_QCRT_CANDIDATE_USER:
		return "QueryConditionRoleType_QCRT_CANDIDATE_USER"
	case QueryConditionRoleType_QCRT_CANDIDATE_GROUP:
		return "QueryConditionRoleType_QCRT_CANDIDATE_GROUP"
	}
	return "<UNSET>"
}

func QueryConditionRoleTypeFromString(s string) (QueryConditionRoleType, error) {
	switch s {
	case "QueryConditionRoleType_QCRT_ASSIGNEE":
		return QueryConditionRoleType_QCRT_ASSIGNEE, nil
	case "QueryConditionRoleType_QCRT_CANDIDATE_USER":
		return QueryConditionRoleType_QCRT_CANDIDATE_USER, nil
	case "QueryConditionRoleType_QCRT_CANDIDATE_GROUP":
		return QueryConditionRoleType_QCRT_CANDIDATE_GROUP, nil
	}
	return QueryConditionRoleType(math.MinInt32 - 1), fmt.Errorf("not a valid QueryConditionRoleType string")
}

type TimeInt common.TimeInt

type AdQueryResult *common.QueryResult

type AdQueryInt common.QueryInt

type RequestHeader *common.RequestHeader

type VersionInt int32

type NumberInt int32

type ProcessDefinitionThrift struct {
	Id                   string     `thrift:"id,1" json:"id"`
	Key                  string     `thrift:"key,2" json:"key"`
	Version              VersionInt `thrift:"version,3" json:"version"`
	Name                 string     `thrift:"name,4" json:"name"`
	ResourceName         string     `thrift:"resourceName,5" json:"resourceName"`
	DeploymentId         string     `thrift:"deploymentId,6" json:"deploymentId"`
	StartFormResourceKey string     `thrift:"startFormResourceKey,7" json:"startFormResourceKey"`
}

func NewProcessDefinitionThrift() *ProcessDefinitionThrift {
	return &ProcessDefinitionThrift{}
}

func (p *ProcessDefinitionThrift) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessDefinitionThrift) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *ProcessDefinitionThrift) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Key = v
	}
	return nil
}

func (p *ProcessDefinitionThrift) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Version = VersionInt(v)
	}
	return nil
}

func (p *ProcessDefinitionThrift) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *ProcessDefinitionThrift) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ResourceName = v
	}
	return nil
}

func (p *ProcessDefinitionThrift) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.DeploymentId = v
	}
	return nil
}

func (p *ProcessDefinitionThrift) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.StartFormResourceKey = v
	}
	return nil
}

func (p *ProcessDefinitionThrift) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ProcessDefinitionThrift"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessDefinitionThrift) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionThrift) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("key", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:key: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Key)); err != nil {
		return fmt.Errorf("%T.key (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:key: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionThrift) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:version: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Version)); err != nil {
		return fmt.Errorf("%T.version (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:version: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionThrift) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionThrift) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("resourceName", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:resourceName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ResourceName)); err != nil {
		return fmt.Errorf("%T.resourceName (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:resourceName: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionThrift) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deploymentId", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:deploymentId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DeploymentId)); err != nil {
		return fmt.Errorf("%T.deploymentId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:deploymentId: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionThrift) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startFormResourceKey", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:startFormResourceKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StartFormResourceKey)); err != nil {
		return fmt.Errorf("%T.startFormResourceKey (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:startFormResourceKey: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionThrift) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessDefinitionThrift(%+v)", *p)
}

type ProcessInstanceThrift struct {
	Id                  string  `thrift:"id,1" json:"id"`
	ProcessDefinitionId string  `thrift:"processDefinitionId,2" json:"processDefinitionId"`
	BusinessKey         string  `thrift:"businessKey,3" json:"businessKey"`
	StartTime           TimeInt `thrift:"startTime,4" json:"startTime"`
	StartUserId         string  `thrift:"startUserId,5" json:"startUserId"`
}

func NewProcessInstanceThrift() *ProcessInstanceThrift {
	return &ProcessInstanceThrift{}
}

func (p *ProcessInstanceThrift) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessInstanceThrift) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *ProcessInstanceThrift) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ProcessDefinitionId = v
	}
	return nil
}

func (p *ProcessInstanceThrift) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.BusinessKey = v
	}
	return nil
}

func (p *ProcessInstanceThrift) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StartTime = TimeInt(v)
	}
	return nil
}

func (p *ProcessInstanceThrift) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StartUserId = v
	}
	return nil
}

func (p *ProcessInstanceThrift) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ProcessInstanceThrift"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessInstanceThrift) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *ProcessInstanceThrift) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("processDefinitionId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:processDefinitionId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ProcessDefinitionId)); err != nil {
		return fmt.Errorf("%T.processDefinitionId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:processDefinitionId: %s", p, err)
	}
	return err
}

func (p *ProcessInstanceThrift) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("businessKey", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:businessKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BusinessKey)); err != nil {
		return fmt.Errorf("%T.businessKey (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:businessKey: %s", p, err)
	}
	return err
}

func (p *ProcessInstanceThrift) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:startTime: %s", p, err)
	}
	return err
}

func (p *ProcessInstanceThrift) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startUserId", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:startUserId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StartUserId)); err != nil {
		return fmt.Errorf("%T.startUserId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:startUserId: %s", p, err)
	}
	return err
}

func (p *ProcessInstanceThrift) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessInstanceThrift(%+v)", *p)
}

type TaskThrift struct {
	Id              string `thrift:"id,1" json:"id"`
	Name            string `thrift:"name,2" json:"name"`
	Description     string `thrift:"description,3" json:"description"`
	Assignee        string `thrift:"assignee,4" json:"assignee"`
	ExecutionId     string `thrift:"executionId,5" json:"executionId"`
	FormResourceKey string `thrift:"formResourceKey,6" json:"formResourceKey"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	CreateTime          TimeInt `thrift:"createTime,10" json:"createTime"`
	ProcessDefinitionId string  `thrift:"processDefinitionId,11" json:"processDefinitionId"`
	ProcessInstanceId   string  `thrift:"processInstanceId,12" json:"processInstanceId"`
	TaskDefinitionKey   string  `thrift:"taskDefinitionKey,13" json:"taskDefinitionKey"`
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	StartTime TimeInt `thrift:"startTime,21" json:"startTime"`
	EndTime   TimeInt `thrift:"endTime,22" json:"endTime"`
	Duration  TimeInt `thrift:"duration,23" json:"duration"`
}

func NewTaskThrift() *TaskThrift {
	return &TaskThrift{}
}

func (p *TaskThrift) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TaskThrift) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *TaskThrift) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *TaskThrift) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *TaskThrift) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Assignee = v
	}
	return nil
}

func (p *TaskThrift) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ExecutionId = v
	}
	return nil
}

func (p *TaskThrift) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.FormResourceKey = v
	}
	return nil
}

func (p *TaskThrift) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *TaskThrift) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ProcessDefinitionId = v
	}
	return nil
}

func (p *TaskThrift) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ProcessInstanceId = v
	}
	return nil
}

func (p *TaskThrift) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.TaskDefinitionKey = v
	}
	return nil
}

func (p *TaskThrift) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.StartTime = TimeInt(v)
	}
	return nil
}

func (p *TaskThrift) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.EndTime = TimeInt(v)
	}
	return nil
}

func (p *TaskThrift) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Duration = TimeInt(v)
	}
	return nil
}

func (p *TaskThrift) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TaskThrift"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TaskThrift) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *TaskThrift) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *TaskThrift) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:description: %s", p, err)
	}
	return err
}

func (p *TaskThrift) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("assignee", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:assignee: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Assignee)); err != nil {
		return fmt.Errorf("%T.assignee (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:assignee: %s", p, err)
	}
	return err
}

func (p *TaskThrift) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("executionId", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:executionId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExecutionId)); err != nil {
		return fmt.Errorf("%T.executionId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:executionId: %s", p, err)
	}
	return err
}

func (p *TaskThrift) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("formResourceKey", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:formResourceKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FormResourceKey)); err != nil {
		return fmt.Errorf("%T.formResourceKey (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:formResourceKey: %s", p, err)
	}
	return err
}

func (p *TaskThrift) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:createTime: %s", p, err)
	}
	return err
}

func (p *TaskThrift) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("processDefinitionId", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:processDefinitionId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ProcessDefinitionId)); err != nil {
		return fmt.Errorf("%T.processDefinitionId (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:processDefinitionId: %s", p, err)
	}
	return err
}

func (p *TaskThrift) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("processInstanceId", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:processInstanceId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ProcessInstanceId)); err != nil {
		return fmt.Errorf("%T.processInstanceId (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:processInstanceId: %s", p, err)
	}
	return err
}

func (p *TaskThrift) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("taskDefinitionKey", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:taskDefinitionKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TaskDefinitionKey)); err != nil {
		return fmt.Errorf("%T.taskDefinitionKey (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:taskDefinitionKey: %s", p, err)
	}
	return err
}

func (p *TaskThrift) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:startTime: %s", p, err)
	}
	return err
}

func (p *TaskThrift) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:endTime: %s", p, err)
	}
	return err
}

func (p *TaskThrift) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:duration: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:duration: %s", p, err)
	}
	return err
}

func (p *TaskThrift) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskThrift(%+v)", *p)
}

type TaskSummaryThrift struct {
	AssignedTotal                        NumberInt            `thrift:"assignedTotal,1" json:"assignedTotal"`
	UnassignedTotal                      NumberInt            `thrift:"unassignedTotal,2" json:"unassignedTotal"`
	UnassignedGroupTotals                map[string]NumberInt `thrift:"unassignedGroupTotals,3" json:"unassignedGroupTotals"`
	FinishedTotal                        NumberInt            `thrift:"finishedTotal,4" json:"finishedTotal"`
	UnFinishedTotalsByTaskDefinitionKeys map[string]NumberInt `thrift:"unFinishedTotalsByTaskDefinitionKeys,5" json:"unFinishedTotalsByTaskDefinitionKeys"`
}

func NewTaskSummaryThrift() *TaskSummaryThrift {
	return &TaskSummaryThrift{}
}

func (p *TaskSummaryThrift) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.MAP {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TaskSummaryThrift) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AssignedTotal = NumberInt(v)
	}
	return nil
}

func (p *TaskSummaryThrift) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.UnassignedTotal = NumberInt(v)
	}
	return nil
}

func (p *TaskSummaryThrift) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.UnassignedGroupTotals = make(map[string]NumberInt, size)
	for i := 0; i < size; i++ {
		var _key0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 NumberInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = NumberInt(v)
		}
		p.UnassignedGroupTotals[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *TaskSummaryThrift) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FinishedTotal = NumberInt(v)
	}
	return nil
}

func (p *TaskSummaryThrift) readField5(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.UnFinishedTotalsByTaskDefinitionKeys = make(map[string]NumberInt, size)
	for i := 0; i < size; i++ {
		var _key2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key2 = v
		}
		var _val3 NumberInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val3 = NumberInt(v)
		}
		p.UnFinishedTotalsByTaskDefinitionKeys[_key2] = _val3
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *TaskSummaryThrift) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TaskSummaryThrift"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TaskSummaryThrift) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("assignedTotal", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:assignedTotal: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AssignedTotal)); err != nil {
		return fmt.Errorf("%T.assignedTotal (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:assignedTotal: %s", p, err)
	}
	return err
}

func (p *TaskSummaryThrift) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unassignedTotal", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:unassignedTotal: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UnassignedTotal)); err != nil {
		return fmt.Errorf("%T.unassignedTotal (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:unassignedTotal: %s", p, err)
	}
	return err
}

func (p *TaskSummaryThrift) writeField3(oprot thrift.TProtocol) (err error) {
	if p.UnassignedGroupTotals != nil {
		if err := oprot.WriteFieldBegin("unassignedGroupTotals", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:unassignedGroupTotals: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.I32, len(p.UnassignedGroupTotals)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.UnassignedGroupTotals {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:unassignedGroupTotals: %s", p, err)
		}
	}
	return err
}

func (p *TaskSummaryThrift) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("finishedTotal", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:finishedTotal: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FinishedTotal)); err != nil {
		return fmt.Errorf("%T.finishedTotal (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:finishedTotal: %s", p, err)
	}
	return err
}

func (p *TaskSummaryThrift) writeField5(oprot thrift.TProtocol) (err error) {
	if p.UnFinishedTotalsByTaskDefinitionKeys != nil {
		if err := oprot.WriteFieldBegin("unFinishedTotalsByTaskDefinitionKeys", thrift.MAP, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:unFinishedTotalsByTaskDefinitionKeys: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.I32, len(p.UnFinishedTotalsByTaskDefinitionKeys)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.UnFinishedTotalsByTaskDefinitionKeys {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:unFinishedTotalsByTaskDefinitionKeys: %s", p, err)
		}
	}
	return err
}

func (p *TaskSummaryThrift) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskSummaryThrift(%+v)", *p)
}

type FormPropertyThrift struct {
	Id        string `thrift:"id,1" json:"id"`
	Name      string `thrift:"name,2" json:"name"`
	Value     string `thrift:"value,3" json:"value"`
	TypeA1    string `thrift:"type,4" json:"type"`
	Mandatory bool   `thrift:"mandatory,5" json:"mandatory"`
	Readable  bool   `thrift:"readable,6" json:"readable"`
	Writable  bool   `thrift:"writable,7" json:"writable"`
}

func NewFormPropertyThrift() *FormPropertyThrift {
	return &FormPropertyThrift{}
}

func (p *FormPropertyThrift) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FormPropertyThrift) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *FormPropertyThrift) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *FormPropertyThrift) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Value = v
	}
	return nil
}

func (p *FormPropertyThrift) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = v
	}
	return nil
}

func (p *FormPropertyThrift) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Mandatory = v
	}
	return nil
}

func (p *FormPropertyThrift) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Readable = v
	}
	return nil
}

func (p *FormPropertyThrift) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Writable = v
	}
	return nil
}

func (p *FormPropertyThrift) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FormPropertyThrift"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FormPropertyThrift) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *FormPropertyThrift) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *FormPropertyThrift) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("value", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:value: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Value)); err != nil {
		return fmt.Errorf("%T.value (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:value: %s", p, err)
	}
	return err
}

func (p *FormPropertyThrift) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:type: %s", p, err)
	}
	return err
}

func (p *FormPropertyThrift) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mandatory", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:mandatory: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Mandatory)); err != nil {
		return fmt.Errorf("%T.mandatory (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:mandatory: %s", p, err)
	}
	return err
}

func (p *FormPropertyThrift) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("readable", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:readable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Readable)); err != nil {
		return fmt.Errorf("%T.readable (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:readable: %s", p, err)
	}
	return err
}

func (p *FormPropertyThrift) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("writable", thrift.BOOL, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:writable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Writable)); err != nil {
		return fmt.Errorf("%T.writable (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:writable: %s", p, err)
	}
	return err
}

func (p *FormPropertyThrift) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FormPropertyThrift(%+v)", *p)
}

type ProcessDefinitionQueryResult struct {
	Data  []*ProcessDefinitionThrift `thrift:"data,1" json:"data"`
	Total NumberInt                  `thrift:"total,2" json:"total"`
	Start NumberInt                  `thrift:"start,3" json:"start"`
	Sort  string                     `thrift:"sort,4" json:"sort"`
	Isasc bool                       `thrift:"isasc,5" json:"isasc"`
	Size  NumberInt                  `thrift:"size,6" json:"size"`
}

func NewProcessDefinitionQueryResult() *ProcessDefinitionQueryResult {
	return &ProcessDefinitionQueryResult{}
}

func (p *ProcessDefinitionQueryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessDefinitionQueryResult) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Data = make([]*ProcessDefinitionThrift, 0, size)
	for i := 0; i < size; i++ {
		_elem4 := NewProcessDefinitionThrift()
		if err := _elem4.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem4)
		}
		p.Data = append(p.Data, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ProcessDefinitionQueryResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Total = NumberInt(v)
	}
	return nil
}

func (p *ProcessDefinitionQueryResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *ProcessDefinitionQueryResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *ProcessDefinitionQueryResult) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *ProcessDefinitionQueryResult) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *ProcessDefinitionQueryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ProcessDefinitionQueryResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessDefinitionQueryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Data != nil {
		if err := oprot.WriteFieldBegin("data", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:data: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Data)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Data {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:data: %s", p, err)
		}
	}
	return err
}

func (p *ProcessDefinitionQueryResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:total: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Total)); err != nil {
		return fmt.Errorf("%T.total (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:total: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionQueryResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:start: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionQueryResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:sort: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionQueryResult) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:isasc: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionQueryResult) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:size: %s", p, err)
	}
	return err
}

func (p *ProcessDefinitionQueryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessDefinitionQueryResult(%+v)", *p)
}

type ProcessInstanceQueryResult struct {
	Data  []*ProcessInstanceThrift `thrift:"data,1" json:"data"`
	Total NumberInt                `thrift:"total,2" json:"total"`
	Start NumberInt                `thrift:"start,3" json:"start"`
	Sort  string                   `thrift:"sort,4" json:"sort"`
	Isasc bool                     `thrift:"isasc,5" json:"isasc"`
	Size  NumberInt                `thrift:"size,6" json:"size"`
}

func NewProcessInstanceQueryResult() *ProcessInstanceQueryResult {
	return &ProcessInstanceQueryResult{}
}

func (p *ProcessInstanceQueryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessInstanceQueryResult) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Data = make([]*ProcessInstanceThrift, 0, size)
	for i := 0; i < size; i++ {
		_elem5 := NewProcessInstanceThrift()
		if err := _elem5.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem5)
		}
		p.Data = append(p.Data, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ProcessInstanceQueryResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Total = NumberInt(v)
	}
	return nil
}

func (p *ProcessInstanceQueryResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *ProcessInstanceQueryResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *ProcessInstanceQueryResult) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *ProcessInstanceQueryResult) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *ProcessInstanceQueryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ProcessInstanceQueryResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessInstanceQueryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Data != nil {
		if err := oprot.WriteFieldBegin("data", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:data: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Data)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Data {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:data: %s", p, err)
		}
	}
	return err
}

func (p *ProcessInstanceQueryResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:total: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Total)); err != nil {
		return fmt.Errorf("%T.total (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:total: %s", p, err)
	}
	return err
}

func (p *ProcessInstanceQueryResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:start: %s", p, err)
	}
	return err
}

func (p *ProcessInstanceQueryResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:sort: %s", p, err)
	}
	return err
}

func (p *ProcessInstanceQueryResult) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:isasc: %s", p, err)
	}
	return err
}

func (p *ProcessInstanceQueryResult) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:size: %s", p, err)
	}
	return err
}

func (p *ProcessInstanceQueryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessInstanceQueryResult(%+v)", *p)
}

type TaskQueryResult struct {
	Data  []*TaskThrift `thrift:"data,1" json:"data"`
	Total NumberInt     `thrift:"total,2" json:"total"`
	Start NumberInt     `thrift:"start,3" json:"start"`
	Sort  string        `thrift:"sort,4" json:"sort"`
	Isasc bool          `thrift:"isasc,5" json:"isasc"`
	Size  NumberInt     `thrift:"size,6" json:"size"`
}

func NewTaskQueryResult() *TaskQueryResult {
	return &TaskQueryResult{}
}

func (p *TaskQueryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TaskQueryResult) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Data = make([]*TaskThrift, 0, size)
	for i := 0; i < size; i++ {
		_elem6 := NewTaskThrift()
		if err := _elem6.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem6)
		}
		p.Data = append(p.Data, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TaskQueryResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Total = NumberInt(v)
	}
	return nil
}

func (p *TaskQueryResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Start = NumberInt(v)
	}
	return nil
}

func (p *TaskQueryResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Sort = v
	}
	return nil
}

func (p *TaskQueryResult) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Isasc = v
	}
	return nil
}

func (p *TaskQueryResult) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Size = NumberInt(v)
	}
	return nil
}

func (p *TaskQueryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TaskQueryResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TaskQueryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Data != nil {
		if err := oprot.WriteFieldBegin("data", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:data: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Data)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Data {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:data: %s", p, err)
		}
	}
	return err
}

func (p *TaskQueryResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:total: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Total)); err != nil {
		return fmt.Errorf("%T.total (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:total: %s", p, err)
	}
	return err
}

func (p *TaskQueryResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Start)); err != nil {
		return fmt.Errorf("%T.start (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:start: %s", p, err)
	}
	return err
}

func (p *TaskQueryResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sort", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:sort: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sort)); err != nil {
		return fmt.Errorf("%T.sort (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:sort: %s", p, err)
	}
	return err
}

func (p *TaskQueryResult) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isasc", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:isasc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isasc)); err != nil {
		return fmt.Errorf("%T.isasc (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:isasc: %s", p, err)
	}
	return err
}

func (p *TaskQueryResult) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:size: %s", p, err)
	}
	return err
}

func (p *TaskQueryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskQueryResult(%+v)", *p)
}
