// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package bi_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/adinfo_types"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/searchui_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = adinfo_types.GoUnusedProtection__
var _ = searchui_types.GoUnusedProtection__
var GoUnusedProtection__ int

type MediaType enums.MediaType

type UidInt common.UidInt

type IdInt common.IdInt

type LargeIdInt common.LargeIdInt

type TimeInt common.TimeInt

type IpInt common.IpInt

type Amount common.Amount

type AgeCode common.AgeCode

type GenderCode common.GenderCode

type RegionCode common.RegionCode

type CarrierCode common.CarrierCode

type DeviceCode common.DeviceCode

type OSCode common.OSCode

type SDKTypeCode common.SDKTypeCode

type EncodingCode common.EncodingCode

type AccessTypeCode common.AccessTypeCode

type CostType common.CostType

type AdActionType common.AdActionType

type AdCreativeType adinfo_types.AdCreativeType

type RedirectType searchui_types.RedirectType

type AdImpClick struct {
	SearchId        LargeIdInt     `thrift:"searchId,1" json:"searchId"`
	DeverId         UidInt         `thrift:"deverId,2" json:"deverId"`
	MediaId         IdInt          `thrift:"mediaId,3" json:"mediaId"`
	ImpressionTime  TimeInt        `thrift:"impressionTime,4" json:"impressionTime"`
	SponsorId       UidInt         `thrift:"sponsorId,5" json:"sponsorId"`
	AdPlanId        IdInt          `thrift:"adPlanId,6" json:"adPlanId"`
	AdStrategyId    IdInt          `thrift:"adStrategyId,7" json:"adStrategyId"`
	AdCreativeId    IdInt          `thrift:"adCreativeId,8" json:"adCreativeId"`
	AdProperty      int32          `thrift:"adProperty,9" json:"adProperty"`
	Density         float64        `thrift:"density,10" json:"density"`
	Bid             Amount         `thrift:"bid,11" json:"bid"`
	ClickPrice      Amount         `thrift:"clickPrice,12" json:"clickPrice"`
	ImpressionPrice Amount         `thrift:"impressionPrice,13" json:"impressionPrice"`
	AdExpCategory   int32          `thrift:"adExpCategory,14" json:"adExpCategory"`
	AdCtr           float64        `thrift:"adCtr,15" json:"adCtr"`
	CostType        CostType       `thrift:"costType,16" json:"costType"`
	AdKeyword       string         `thrift:"adKeyword,17" json:"adKeyword"`
	AdType          AdCreativeType `thrift:"adType,18" json:"adType"`
	IsCharge        bool           `thrift:"isCharge,19" json:"isCharge"`
	AdPrint         string         `thrift:"adPrint,20" json:"adPrint"`
	LandingUrl      string         `thrift:"landingUrl,21" json:"landingUrl"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	ExpId             int32          `thrift:"expId,35" json:"expId"`
	Ip                IpInt          `thrift:"ip,36" json:"ip"`
	Carrier           CarrierCode    `thrift:"carrier,37" json:"carrier"`
	UserAgent         string         `thrift:"userAgent,38" json:"userAgent"`
	Refer             string         `thrift:"refer,39" json:"refer"`
	Device            DeviceCode     `thrift:"device,40" json:"device"`
	Os                OSCode         `thrift:"os,41" json:"os"`
	Imei              LargeIdInt     `thrift:"imei,42" json:"imei"`
	Imsi              LargeIdInt     `thrift:"imsi,43" json:"imsi"`
	CookieId          string         `thrift:"cookieId,44" json:"cookieId"`
	PublishId         string         `thrift:"publishId,45" json:"publishId"`
	Uuid              string         `thrift:"uuid,46" json:"uuid"`
	MediaType         MediaType      `thrift:"mediaType,47" json:"mediaType"`
	Region            RegionCode     `thrift:"region,48" json:"region"`
	Spot              string         `thrift:"spot,49" json:"spot"`
	SdkType           SDKTypeCode    `thrift:"sdkType,50" json:"sdkType"`
	SdkVersion        string         `thrift:"sdkVersion,51" json:"sdkVersion"`
	ClientEncoding    EncodingCode   `thrift:"clientEncoding,52" json:"clientEncoding"`
	ScreenIsVertical  bool           `thrift:"screenIsVertical,53" json:"screenIsVertical"`
	ScreenWidth       int32          `thrift:"screenWidth,54" json:"screenWidth"`
	ScreenHeight      int32          `thrift:"screenHeight,55" json:"screenHeight"`
	ScreenDensity     float64        `thrift:"screenDensity,56" json:"screenDensity"`
	Capability        []AdActionType `thrift:"capability,57" json:"capability"`
	MediaKeyword      string         `thrift:"mediaKeyword,58" json:"mediaKeyword"`
	MediaSearchQuery  string         `thrift:"mediaSearchQuery,59" json:"mediaSearchQuery"`
	AppName           string         `thrift:"appName,60" json:"appName"`
	AppVersion        string         `thrift:"appVersion,61" json:"appVersion"`
	CoordTime         TimeInt        `thrift:"coordTime,62" json:"coordTime"`
	Latitude          float64        `thrift:"latitude,63" json:"latitude"`
	Longitude         float64        `thrift:"longitude,64" json:"longitude"`
	PostalCode        int32          `thrift:"postalCode,65" json:"postalCode"`
	Birthday          int32          `thrift:"birthday,66" json:"birthday"`
	Gender            GenderCode     `thrift:"gender,67" json:"gender"`
	AccessType        AccessTypeCode `thrift:"accessType,68" json:"accessType"`
	ClickTime         TimeInt        `thrift:"clickTime,69" json:"clickTime"`
	Price             Amount         `thrift:"price,70" json:"price"`
	UserAgentClk      string         `thrift:"userAgentClk,71" json:"userAgentClk"`
	RemoteAddrClk     IpInt          `thrift:"remoteAddrClk,72" json:"remoteAddrClk"`
	RefererClk        string         `thrift:"refererClk,73" json:"refererClk"`
	CookieClk         string         `thrift:"cookieClk,74" json:"cookieClk"`
	RedirectType      RedirectType   `thrift:"redirectType,75" json:"redirectType"`
	ClickSeq          int32          `thrift:"clickSeq,76" json:"clickSeq"`
	SettledPrice      Amount         `thrift:"settledPrice,77" json:"settledPrice"`
	SettledMediaShare Amount         `thrift:"settledMediaShare,78" json:"settledMediaShare"`
	ClkIsCharge       bool           `thrift:"clkIsCharge,79" json:"clkIsCharge"`
	ImpIsFilter       bool           `thrift:"impIsFilter,80" json:"impIsFilter"`
	ClkIsFilter       bool           `thrift:"clkIsFilter,81" json:"clkIsFilter"`
	SpamType          string         `thrift:"spamType,82" json:"spamType"`
	ClkSpamType       string         `thrift:"clkSpamType,83" json:"clkSpamType"`
	Determine         string         `thrift:"determine,84" json:"determine"`
	ExtInfo           string         `thrift:"extInfo,85" json:"extInfo"`
	RegionCode        IdInt          `thrift:"region_code,86" json:"region_code"`
}

func NewAdImpClick() *AdImpClick {
	return &AdImpClick{
		CostType: math.MinInt32 - 1, // unset sentinal value

		AdType: math.MinInt32 - 1, // unset sentinal value

		Carrier: math.MinInt32 - 1, // unset sentinal value

		MediaType: math.MinInt32 - 1, // unset sentinal value

		Region: math.MinInt32 - 1, // unset sentinal value

		SdkType: math.MinInt32 - 1, // unset sentinal value

		ClientEncoding: math.MinInt32 - 1, // unset sentinal value

		Gender: math.MinInt32 - 1, // unset sentinal value

		AccessType: math.MinInt32 - 1, // unset sentinal value

		RedirectType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdImpClick) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *AdImpClick) IsSetAdType() bool {
	return int64(p.AdType) != math.MinInt32-1
}

func (p *AdImpClick) IsSetCarrier() bool {
	return int64(p.Carrier) != math.MinInt32-1
}

func (p *AdImpClick) IsSetMediaType() bool {
	return int64(p.MediaType) != math.MinInt32-1
}

func (p *AdImpClick) IsSetRegion() bool {
	return int64(p.Region) != math.MinInt32-1
}

func (p *AdImpClick) IsSetSdkType() bool {
	return int64(p.SdkType) != math.MinInt32-1
}

func (p *AdImpClick) IsSetClientEncoding() bool {
	return int64(p.ClientEncoding) != math.MinInt32-1
}

func (p *AdImpClick) IsSetGender() bool {
	return int64(p.Gender) != math.MinInt32-1
}

func (p *AdImpClick) IsSetAccessType() bool {
	return int64(p.AccessType) != math.MinInt32-1
}

func (p *AdImpClick) IsSetRedirectType() bool {
	return int64(p.RedirectType) != math.MinInt32-1
}

func (p *AdImpClick) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I64 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.STRING {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.STRING {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I64 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.I64 {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.STRING {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.STRING {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.STRING {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.I32 {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 48:
			if fieldTypeId == thrift.I32 {
				if err := p.readField48(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 49:
			if fieldTypeId == thrift.STRING {
				if err := p.readField49(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.I32 {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.STRING {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I32 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I32 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.I32 {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 56:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField56(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 57:
			if fieldTypeId == thrift.LIST {
				if err := p.readField57(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 58:
			if fieldTypeId == thrift.STRING {
				if err := p.readField58(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 59:
			if fieldTypeId == thrift.STRING {
				if err := p.readField59(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.STRING {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.STRING {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 62:
			if fieldTypeId == thrift.I64 {
				if err := p.readField62(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 63:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField63(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 64:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField64(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 65:
			if fieldTypeId == thrift.I32 {
				if err := p.readField65(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 66:
			if fieldTypeId == thrift.I32 {
				if err := p.readField66(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 67:
			if fieldTypeId == thrift.I32 {
				if err := p.readField67(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 68:
			if fieldTypeId == thrift.I32 {
				if err := p.readField68(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 69:
			if fieldTypeId == thrift.I64 {
				if err := p.readField69(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 70:
			if fieldTypeId == thrift.I64 {
				if err := p.readField70(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 71:
			if fieldTypeId == thrift.STRING {
				if err := p.readField71(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 72:
			if fieldTypeId == thrift.I64 {
				if err := p.readField72(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 73:
			if fieldTypeId == thrift.STRING {
				if err := p.readField73(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 74:
			if fieldTypeId == thrift.STRING {
				if err := p.readField74(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 75:
			if fieldTypeId == thrift.I32 {
				if err := p.readField75(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 76:
			if fieldTypeId == thrift.I32 {
				if err := p.readField76(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 77:
			if fieldTypeId == thrift.I64 {
				if err := p.readField77(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 78:
			if fieldTypeId == thrift.I64 {
				if err := p.readField78(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 79:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField79(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 80:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField80(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 81:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField81(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 82:
			if fieldTypeId == thrift.STRING {
				if err := p.readField82(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 83:
			if fieldTypeId == thrift.STRING {
				if err := p.readField83(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 84:
			if fieldTypeId == thrift.STRING {
				if err := p.readField84(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 85:
			if fieldTypeId == thrift.STRING {
				if err := p.readField85(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 86:
			if fieldTypeId == thrift.I32 {
				if err := p.readField86(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdImpClick) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = LargeIdInt(v)
	}
	return nil
}

func (p *AdImpClick) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DeverId = UidInt(v)
	}
	return nil
}

func (p *AdImpClick) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MediaId = IdInt(v)
	}
	return nil
}

func (p *AdImpClick) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ImpressionTime = TimeInt(v)
	}
	return nil
}

func (p *AdImpClick) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SponsorId = UidInt(v)
	}
	return nil
}

func (p *AdImpClick) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AdPlanId = IdInt(v)
	}
	return nil
}

func (p *AdImpClick) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AdStrategyId = IdInt(v)
	}
	return nil
}

func (p *AdImpClick) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AdCreativeId = IdInt(v)
	}
	return nil
}

func (p *AdImpClick) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AdProperty = v
	}
	return nil
}

func (p *AdImpClick) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Density = v
	}
	return nil
}

func (p *AdImpClick) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Bid = Amount(v)
	}
	return nil
}

func (p *AdImpClick) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ClickPrice = Amount(v)
	}
	return nil
}

func (p *AdImpClick) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ImpressionPrice = Amount(v)
	}
	return nil
}

func (p *AdImpClick) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.AdExpCategory = v
	}
	return nil
}

func (p *AdImpClick) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.AdCtr = v
	}
	return nil
}

func (p *AdImpClick) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.CostType = CostType(v)
	}
	return nil
}

func (p *AdImpClick) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.AdKeyword = v
	}
	return nil
}

func (p *AdImpClick) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.AdType = AdCreativeType(v)
	}
	return nil
}

func (p *AdImpClick) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.IsCharge = v
	}
	return nil
}

func (p *AdImpClick) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.AdPrint = v
	}
	return nil
}

func (p *AdImpClick) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LandingUrl = v
	}
	return nil
}

func (p *AdImpClick) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.ExpId = v
	}
	return nil
}

func (p *AdImpClick) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.Ip = IpInt(v)
	}
	return nil
}

func (p *AdImpClick) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Carrier = CarrierCode(v)
	}
	return nil
}

func (p *AdImpClick) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.UserAgent = v
	}
	return nil
}

func (p *AdImpClick) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.Refer = v
	}
	return nil
}

func (p *AdImpClick) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Device = DeviceCode(v)
	}
	return nil
}

func (p *AdImpClick) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Os = OSCode(v)
	}
	return nil
}

func (p *AdImpClick) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.Imei = LargeIdInt(v)
	}
	return nil
}

func (p *AdImpClick) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Imsi = LargeIdInt(v)
	}
	return nil
}

func (p *AdImpClick) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.CookieId = v
	}
	return nil
}

func (p *AdImpClick) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.PublishId = v
	}
	return nil
}

func (p *AdImpClick) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.Uuid = v
	}
	return nil
}

func (p *AdImpClick) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.MediaType = MediaType(v)
	}
	return nil
}

func (p *AdImpClick) readField48(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 48: %s", err)
	} else {
		p.Region = RegionCode(v)
	}
	return nil
}

func (p *AdImpClick) readField49(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 49: %s", err)
	} else {
		p.Spot = v
	}
	return nil
}

func (p *AdImpClick) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.SdkType = SDKTypeCode(v)
	}
	return nil
}

func (p *AdImpClick) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.SdkVersion = v
	}
	return nil
}

func (p *AdImpClick) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.ClientEncoding = EncodingCode(v)
	}
	return nil
}

func (p *AdImpClick) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.ScreenIsVertical = v
	}
	return nil
}

func (p *AdImpClick) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.ScreenWidth = v
	}
	return nil
}

func (p *AdImpClick) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.ScreenHeight = v
	}
	return nil
}

func (p *AdImpClick) readField56(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 56: %s", err)
	} else {
		p.ScreenDensity = v
	}
	return nil
}

func (p *AdImpClick) readField57(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Capability = make([]AdActionType, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 AdActionType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = AdActionType(v)
		}
		p.Capability = append(p.Capability, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdImpClick) readField58(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 58: %s", err)
	} else {
		p.MediaKeyword = v
	}
	return nil
}

func (p *AdImpClick) readField59(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 59: %s", err)
	} else {
		p.MediaSearchQuery = v
	}
	return nil
}

func (p *AdImpClick) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *AdImpClick) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.AppVersion = v
	}
	return nil
}

func (p *AdImpClick) readField62(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 62: %s", err)
	} else {
		p.CoordTime = TimeInt(v)
	}
	return nil
}

func (p *AdImpClick) readField63(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 63: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *AdImpClick) readField64(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 64: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *AdImpClick) readField65(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 65: %s", err)
	} else {
		p.PostalCode = v
	}
	return nil
}

func (p *AdImpClick) readField66(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 66: %s", err)
	} else {
		p.Birthday = v
	}
	return nil
}

func (p *AdImpClick) readField67(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 67: %s", err)
	} else {
		p.Gender = GenderCode(v)
	}
	return nil
}

func (p *AdImpClick) readField68(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 68: %s", err)
	} else {
		p.AccessType = AccessTypeCode(v)
	}
	return nil
}

func (p *AdImpClick) readField69(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 69: %s", err)
	} else {
		p.ClickTime = TimeInt(v)
	}
	return nil
}

func (p *AdImpClick) readField70(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 70: %s", err)
	} else {
		p.Price = Amount(v)
	}
	return nil
}

func (p *AdImpClick) readField71(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 71: %s", err)
	} else {
		p.UserAgentClk = v
	}
	return nil
}

func (p *AdImpClick) readField72(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 72: %s", err)
	} else {
		p.RemoteAddrClk = IpInt(v)
	}
	return nil
}

func (p *AdImpClick) readField73(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 73: %s", err)
	} else {
		p.RefererClk = v
	}
	return nil
}

func (p *AdImpClick) readField74(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 74: %s", err)
	} else {
		p.CookieClk = v
	}
	return nil
}

func (p *AdImpClick) readField75(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 75: %s", err)
	} else {
		p.RedirectType = RedirectType(v)
	}
	return nil
}

func (p *AdImpClick) readField76(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 76: %s", err)
	} else {
		p.ClickSeq = v
	}
	return nil
}

func (p *AdImpClick) readField77(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 77: %s", err)
	} else {
		p.SettledPrice = Amount(v)
	}
	return nil
}

func (p *AdImpClick) readField78(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 78: %s", err)
	} else {
		p.SettledMediaShare = Amount(v)
	}
	return nil
}

func (p *AdImpClick) readField79(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 79: %s", err)
	} else {
		p.ClkIsCharge = v
	}
	return nil
}

func (p *AdImpClick) readField80(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 80: %s", err)
	} else {
		p.ImpIsFilter = v
	}
	return nil
}

func (p *AdImpClick) readField81(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 81: %s", err)
	} else {
		p.ClkIsFilter = v
	}
	return nil
}

func (p *AdImpClick) readField82(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 82: %s", err)
	} else {
		p.SpamType = v
	}
	return nil
}

func (p *AdImpClick) readField83(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 83: %s", err)
	} else {
		p.ClkSpamType = v
	}
	return nil
}

func (p *AdImpClick) readField84(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 84: %s", err)
	} else {
		p.Determine = v
	}
	return nil
}

func (p *AdImpClick) readField85(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 85: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *AdImpClick) readField86(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 86: %s", err)
	} else {
		p.RegionCode = IdInt(v)
	}
	return nil
}

func (p *AdImpClick) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdImpClick"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := p.writeField48(oprot); err != nil {
		return err
	}
	if err := p.writeField49(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField56(oprot); err != nil {
		return err
	}
	if err := p.writeField57(oprot); err != nil {
		return err
	}
	if err := p.writeField58(oprot); err != nil {
		return err
	}
	if err := p.writeField59(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField62(oprot); err != nil {
		return err
	}
	if err := p.writeField63(oprot); err != nil {
		return err
	}
	if err := p.writeField64(oprot); err != nil {
		return err
	}
	if err := p.writeField65(oprot); err != nil {
		return err
	}
	if err := p.writeField66(oprot); err != nil {
		return err
	}
	if err := p.writeField67(oprot); err != nil {
		return err
	}
	if err := p.writeField68(oprot); err != nil {
		return err
	}
	if err := p.writeField69(oprot); err != nil {
		return err
	}
	if err := p.writeField70(oprot); err != nil {
		return err
	}
	if err := p.writeField71(oprot); err != nil {
		return err
	}
	if err := p.writeField72(oprot); err != nil {
		return err
	}
	if err := p.writeField73(oprot); err != nil {
		return err
	}
	if err := p.writeField74(oprot); err != nil {
		return err
	}
	if err := p.writeField75(oprot); err != nil {
		return err
	}
	if err := p.writeField76(oprot); err != nil {
		return err
	}
	if err := p.writeField77(oprot); err != nil {
		return err
	}
	if err := p.writeField78(oprot); err != nil {
		return err
	}
	if err := p.writeField79(oprot); err != nil {
		return err
	}
	if err := p.writeField80(oprot); err != nil {
		return err
	}
	if err := p.writeField81(oprot); err != nil {
		return err
	}
	if err := p.writeField82(oprot); err != nil {
		return err
	}
	if err := p.writeField83(oprot); err != nil {
		return err
	}
	if err := p.writeField84(oprot); err != nil {
		return err
	}
	if err := p.writeField85(oprot); err != nil {
		return err
	}
	if err := p.writeField86(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdImpClick) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deverId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:deverId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeverId)); err != nil {
		return fmt.Errorf("%T.deverId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:deverId: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:mediaId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.mediaId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:mediaId: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:impressionTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionTime)); err != nil {
		return fmt.Errorf("%T.impressionTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:impressionTime: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sponsorId: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPlanId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:adPlanId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdPlanId)); err != nil {
		return fmt.Errorf("%T.adPlanId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:adPlanId: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adStrategyId", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:adStrategyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdStrategyId)); err != nil {
		return fmt.Errorf("%T.adStrategyId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:adStrategyId: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adCreativeId", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:adCreativeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdCreativeId)); err != nil {
		return fmt.Errorf("%T.adCreativeId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:adCreativeId: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adProperty", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:adProperty: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdProperty)); err != nil {
		return fmt.Errorf("%T.adProperty (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:adProperty: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("density", thrift.DOUBLE, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:density: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Density)); err != nil {
		return fmt.Errorf("%T.density (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:density: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bid", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Bid)); err != nil {
		return fmt.Errorf("%T.bid (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:bid: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickPrice", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:clickPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClickPrice)); err != nil {
		return fmt.Errorf("%T.clickPrice (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:clickPrice: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionPrice", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:impressionPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpressionPrice)); err != nil {
		return fmt.Errorf("%T.impressionPrice (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:impressionPrice: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adExpCategory", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:adExpCategory: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdExpCategory)); err != nil {
		return fmt.Errorf("%T.adExpCategory (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:adExpCategory: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adCtr", thrift.DOUBLE, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:adCtr: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.AdCtr)); err != nil {
		return fmt.Errorf("%T.adCtr (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:adCtr: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:costType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:costType: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adKeyword", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:adKeyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdKeyword)); err != nil {
		return fmt.Errorf("%T.adKeyword (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:adKeyword: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adType", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:adType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdType)); err != nil {
		return fmt.Errorf("%T.adType (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:adType: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isCharge", thrift.BOOL, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:isCharge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsCharge)); err != nil {
		return fmt.Errorf("%T.isCharge (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:isCharge: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPrint", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:adPrint: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdPrint)); err != nil {
		return fmt.Errorf("%T.adPrint (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:adPrint: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("landingUrl", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:landingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LandingUrl)); err != nil {
		return fmt.Errorf("%T.landingUrl (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:landingUrl: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expId", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:expId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExpId)); err != nil {
		return fmt.Errorf("%T.expId (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:expId: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.I64, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:ip: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:ip: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:carrier: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:carrier: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userAgent", thrift.STRING, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:userAgent: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserAgent)); err != nil {
		return fmt.Errorf("%T.userAgent (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:userAgent: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refer", thrift.STRING, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:refer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Refer)); err != nil {
		return fmt.Errorf("%T.refer (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:refer: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:device: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Device)); err != nil {
		return fmt.Errorf("%T.device (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:device: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:os: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Os)); err != nil {
		return fmt.Errorf("%T.os (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:os: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.I64, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:imei: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:imei: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imsi", thrift.I64, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:imsi: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Imsi)); err != nil {
		return fmt.Errorf("%T.imsi (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:imsi: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cookieId", thrift.STRING, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:cookieId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CookieId)); err != nil {
		return fmt.Errorf("%T.cookieId (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:cookieId: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("publishId", thrift.STRING, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:publishId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PublishId)); err != nil {
		return fmt.Errorf("%T.publishId (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:publishId: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uuid", thrift.STRING, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:uuid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uuid)); err != nil {
		return fmt.Errorf("%T.uuid (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:uuid: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaType", thrift.I32, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:mediaType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaType)); err != nil {
		return fmt.Errorf("%T.mediaType (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:mediaType: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField48(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region", thrift.I32, 48); err != nil {
		return fmt.Errorf("%T write field begin error 48:region: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Region)); err != nil {
		return fmt.Errorf("%T.region (48) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 48:region: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField49(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spot", thrift.STRING, 49); err != nil {
		return fmt.Errorf("%T write field begin error 49:spot: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Spot)); err != nil {
		return fmt.Errorf("%T.spot (49) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 49:spot: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdkType", thrift.I32, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:sdkType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdkType)); err != nil {
		return fmt.Errorf("%T.sdkType (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:sdkType: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdkVersion", thrift.STRING, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:sdkVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SdkVersion)); err != nil {
		return fmt.Errorf("%T.sdkVersion (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:sdkVersion: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clientEncoding", thrift.I32, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:clientEncoding: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClientEncoding)); err != nil {
		return fmt.Errorf("%T.clientEncoding (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:clientEncoding: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("screenIsVertical", thrift.BOOL, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:screenIsVertical: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ScreenIsVertical)); err != nil {
		return fmt.Errorf("%T.screenIsVertical (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:screenIsVertical: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("screenWidth", thrift.I32, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:screenWidth: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ScreenWidth)); err != nil {
		return fmt.Errorf("%T.screenWidth (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:screenWidth: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("screenHeight", thrift.I32, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:screenHeight: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ScreenHeight)); err != nil {
		return fmt.Errorf("%T.screenHeight (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:screenHeight: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField56(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("screenDensity", thrift.DOUBLE, 56); err != nil {
		return fmt.Errorf("%T write field begin error 56:screenDensity: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.ScreenDensity)); err != nil {
		return fmt.Errorf("%T.screenDensity (56) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 56:screenDensity: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField57(oprot thrift.TProtocol) (err error) {
	if p.Capability != nil {
		if err := oprot.WriteFieldBegin("capability", thrift.LIST, 57); err != nil {
			return fmt.Errorf("%T write field begin error 57:capability: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Capability)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Capability {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 57:capability: %s", p, err)
		}
	}
	return err
}

func (p *AdImpClick) writeField58(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaKeyword", thrift.STRING, 58); err != nil {
		return fmt.Errorf("%T write field begin error 58:mediaKeyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MediaKeyword)); err != nil {
		return fmt.Errorf("%T.mediaKeyword (58) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 58:mediaKeyword: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField59(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaSearchQuery", thrift.STRING, 59); err != nil {
		return fmt.Errorf("%T write field begin error 59:mediaSearchQuery: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MediaSearchQuery)); err != nil {
		return fmt.Errorf("%T.mediaSearchQuery (59) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 59:mediaSearchQuery: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appName", thrift.STRING, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:appName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.appName (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:appName: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField61(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appVersion", thrift.STRING, 61); err != nil {
		return fmt.Errorf("%T write field begin error 61:appVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppVersion)); err != nil {
		return fmt.Errorf("%T.appVersion (61) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 61:appVersion: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField62(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coordTime", thrift.I64, 62); err != nil {
		return fmt.Errorf("%T write field begin error 62:coordTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CoordTime)); err != nil {
		return fmt.Errorf("%T.coordTime (62) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 62:coordTime: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField63(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.DOUBLE, 63); err != nil {
		return fmt.Errorf("%T write field begin error 63:latitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (63) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 63:latitude: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField64(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.DOUBLE, 64); err != nil {
		return fmt.Errorf("%T write field begin error 64:longitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (64) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 64:longitude: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField65(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("postalCode", thrift.I32, 65); err != nil {
		return fmt.Errorf("%T write field begin error 65:postalCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PostalCode)); err != nil {
		return fmt.Errorf("%T.postalCode (65) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 65:postalCode: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField66(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("birthday", thrift.I32, 66); err != nil {
		return fmt.Errorf("%T write field begin error 66:birthday: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Birthday)); err != nil {
		return fmt.Errorf("%T.birthday (66) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 66:birthday: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField67(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gender", thrift.I32, 67); err != nil {
		return fmt.Errorf("%T write field begin error 67:gender: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Gender)); err != nil {
		return fmt.Errorf("%T.gender (67) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 67:gender: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField68(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accessType", thrift.I32, 68); err != nil {
		return fmt.Errorf("%T write field begin error 68:accessType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessType)); err != nil {
		return fmt.Errorf("%T.accessType (68) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 68:accessType: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField69(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickTime", thrift.I64, 69); err != nil {
		return fmt.Errorf("%T write field begin error 69:clickTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClickTime)); err != nil {
		return fmt.Errorf("%T.clickTime (69) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 69:clickTime: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField70(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 70); err != nil {
		return fmt.Errorf("%T write field begin error 70:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (70) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 70:price: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField71(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userAgentClk", thrift.STRING, 71); err != nil {
		return fmt.Errorf("%T write field begin error 71:userAgentClk: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserAgentClk)); err != nil {
		return fmt.Errorf("%T.userAgentClk (71) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 71:userAgentClk: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField72(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remoteAddrClk", thrift.I64, 72); err != nil {
		return fmt.Errorf("%T write field begin error 72:remoteAddrClk: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RemoteAddrClk)); err != nil {
		return fmt.Errorf("%T.remoteAddrClk (72) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 72:remoteAddrClk: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField73(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refererClk", thrift.STRING, 73); err != nil {
		return fmt.Errorf("%T write field begin error 73:refererClk: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RefererClk)); err != nil {
		return fmt.Errorf("%T.refererClk (73) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 73:refererClk: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField74(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cookieClk", thrift.STRING, 74); err != nil {
		return fmt.Errorf("%T write field begin error 74:cookieClk: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CookieClk)); err != nil {
		return fmt.Errorf("%T.cookieClk (74) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 74:cookieClk: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField75(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("redirectType", thrift.I32, 75); err != nil {
		return fmt.Errorf("%T write field begin error 75:redirectType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RedirectType)); err != nil {
		return fmt.Errorf("%T.redirectType (75) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 75:redirectType: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField76(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickSeq", thrift.I32, 76); err != nil {
		return fmt.Errorf("%T write field begin error 76:clickSeq: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClickSeq)); err != nil {
		return fmt.Errorf("%T.clickSeq (76) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 76:clickSeq: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField77(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settledPrice", thrift.I64, 77); err != nil {
		return fmt.Errorf("%T write field begin error 77:settledPrice: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettledPrice)); err != nil {
		return fmt.Errorf("%T.settledPrice (77) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 77:settledPrice: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField78(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settledMediaShare", thrift.I64, 78); err != nil {
		return fmt.Errorf("%T write field begin error 78:settledMediaShare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettledMediaShare)); err != nil {
		return fmt.Errorf("%T.settledMediaShare (78) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 78:settledMediaShare: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField79(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkIsCharge", thrift.BOOL, 79); err != nil {
		return fmt.Errorf("%T write field begin error 79:clkIsCharge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ClkIsCharge)); err != nil {
		return fmt.Errorf("%T.clkIsCharge (79) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 79:clkIsCharge: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField80(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impIsFilter", thrift.BOOL, 80); err != nil {
		return fmt.Errorf("%T write field begin error 80:impIsFilter: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ImpIsFilter)); err != nil {
		return fmt.Errorf("%T.impIsFilter (80) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 80:impIsFilter: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField81(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkIsFilter", thrift.BOOL, 81); err != nil {
		return fmt.Errorf("%T write field begin error 81:clkIsFilter: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ClkIsFilter)); err != nil {
		return fmt.Errorf("%T.clkIsFilter (81) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 81:clkIsFilter: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField82(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spamType", thrift.STRING, 82); err != nil {
		return fmt.Errorf("%T write field begin error 82:spamType: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SpamType)); err != nil {
		return fmt.Errorf("%T.spamType (82) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 82:spamType: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField83(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkSpamType", thrift.STRING, 83); err != nil {
		return fmt.Errorf("%T write field begin error 83:clkSpamType: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClkSpamType)); err != nil {
		return fmt.Errorf("%T.clkSpamType (83) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 83:clkSpamType: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField84(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("determine", thrift.STRING, 84); err != nil {
		return fmt.Errorf("%T write field begin error 84:determine: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Determine)); err != nil {
		return fmt.Errorf("%T.determine (84) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 84:determine: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField85(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extInfo", thrift.STRING, 85); err != nil {
		return fmt.Errorf("%T write field begin error 85:extInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.extInfo (85) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 85:extInfo: %s", p, err)
	}
	return err
}

func (p *AdImpClick) writeField86(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region_code", thrift.I32, 86); err != nil {
		return fmt.Errorf("%T write field begin error 86:region_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.region_code (86) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 86:region_code: %s", p, err)
	}
	return err
}

func (p *AdImpClick) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdImpClick(%+v)", *p)
}
