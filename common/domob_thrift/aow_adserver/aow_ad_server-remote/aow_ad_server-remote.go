// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"aow_adserver"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>der<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  AowConfig getConfig(RequestHeader header, AowUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  OfferResponse getOfferById(RequestHeader header, AowUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  OfferResponse getInitialOffers(RequestHeader header, AowUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  OfferResponse getSpecificOffers(RequestHeader header, AowUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  OfferResponse getTasks(RequestHeader header, AowUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  OfferResponse getSpecificTasks(RequestHeader header, AowUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  PointResponse getPoint(RequestHeader header, AowUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  PointResponse ConsumePoint(RequestHeader header, AowUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  AowAccumulatedBonus getBonus(RequestHeader header, AowUIProccessedRequest ui_request)")
	fmt.Fprintln(os.Stderr, "  bool bonusCallback(RequestHeader header, i32 did, i32 bid, i32 btype)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := aow_adserver.NewAowAdServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getConfig":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetConfig requires 2 args")
			flag.Usage()
		}
		arg42 := flag.Arg(1)
		mbTrans43 := thrift.NewTMemoryBufferLen(len(arg42))
		defer mbTrans43.Close()
		_, err44 := mbTrans43.WriteString(arg42)
		if err44 != nil {
			Usage()
			return
		}
		factory45 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt46 := factory45.GetProtocol(mbTrans43)
		argvalue0 := aow_adserver.NewRequestHeader()
		err47 := argvalue0.Read(jsProt46)
		if err47 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg48 := flag.Arg(2)
		mbTrans49 := thrift.NewTMemoryBufferLen(len(arg48))
		defer mbTrans49.Close()
		_, err50 := mbTrans49.WriteString(arg48)
		if err50 != nil {
			Usage()
			return
		}
		factory51 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt52 := factory51.GetProtocol(mbTrans49)
		argvalue1 := aow_adserver.NewAowUIProccessedRequest()
		err53 := argvalue1.Read(jsProt52)
		if err53 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetConfig(value0, value1))
		fmt.Print("\n")
		break
	case "getOfferById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetOfferById requires 2 args")
			flag.Usage()
		}
		arg54 := flag.Arg(1)
		mbTrans55 := thrift.NewTMemoryBufferLen(len(arg54))
		defer mbTrans55.Close()
		_, err56 := mbTrans55.WriteString(arg54)
		if err56 != nil {
			Usage()
			return
		}
		factory57 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt58 := factory57.GetProtocol(mbTrans55)
		argvalue0 := aow_adserver.NewRequestHeader()
		err59 := argvalue0.Read(jsProt58)
		if err59 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg60 := flag.Arg(2)
		mbTrans61 := thrift.NewTMemoryBufferLen(len(arg60))
		defer mbTrans61.Close()
		_, err62 := mbTrans61.WriteString(arg60)
		if err62 != nil {
			Usage()
			return
		}
		factory63 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt64 := factory63.GetProtocol(mbTrans61)
		argvalue1 := aow_adserver.NewAowUIProccessedRequest()
		err65 := argvalue1.Read(jsProt64)
		if err65 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetOfferById(value0, value1))
		fmt.Print("\n")
		break
	case "getInitialOffers":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetInitialOffers requires 2 args")
			flag.Usage()
		}
		arg66 := flag.Arg(1)
		mbTrans67 := thrift.NewTMemoryBufferLen(len(arg66))
		defer mbTrans67.Close()
		_, err68 := mbTrans67.WriteString(arg66)
		if err68 != nil {
			Usage()
			return
		}
		factory69 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt70 := factory69.GetProtocol(mbTrans67)
		argvalue0 := aow_adserver.NewRequestHeader()
		err71 := argvalue0.Read(jsProt70)
		if err71 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg72 := flag.Arg(2)
		mbTrans73 := thrift.NewTMemoryBufferLen(len(arg72))
		defer mbTrans73.Close()
		_, err74 := mbTrans73.WriteString(arg72)
		if err74 != nil {
			Usage()
			return
		}
		factory75 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt76 := factory75.GetProtocol(mbTrans73)
		argvalue1 := aow_adserver.NewAowUIProccessedRequest()
		err77 := argvalue1.Read(jsProt76)
		if err77 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetInitialOffers(value0, value1))
		fmt.Print("\n")
		break
	case "getSpecificOffers":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSpecificOffers requires 2 args")
			flag.Usage()
		}
		arg78 := flag.Arg(1)
		mbTrans79 := thrift.NewTMemoryBufferLen(len(arg78))
		defer mbTrans79.Close()
		_, err80 := mbTrans79.WriteString(arg78)
		if err80 != nil {
			Usage()
			return
		}
		factory81 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt82 := factory81.GetProtocol(mbTrans79)
		argvalue0 := aow_adserver.NewRequestHeader()
		err83 := argvalue0.Read(jsProt82)
		if err83 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg84 := flag.Arg(2)
		mbTrans85 := thrift.NewTMemoryBufferLen(len(arg84))
		defer mbTrans85.Close()
		_, err86 := mbTrans85.WriteString(arg84)
		if err86 != nil {
			Usage()
			return
		}
		factory87 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt88 := factory87.GetProtocol(mbTrans85)
		argvalue1 := aow_adserver.NewAowUIProccessedRequest()
		err89 := argvalue1.Read(jsProt88)
		if err89 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetSpecificOffers(value0, value1))
		fmt.Print("\n")
		break
	case "getTasks":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTasks requires 2 args")
			flag.Usage()
		}
		arg90 := flag.Arg(1)
		mbTrans91 := thrift.NewTMemoryBufferLen(len(arg90))
		defer mbTrans91.Close()
		_, err92 := mbTrans91.WriteString(arg90)
		if err92 != nil {
			Usage()
			return
		}
		factory93 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt94 := factory93.GetProtocol(mbTrans91)
		argvalue0 := aow_adserver.NewRequestHeader()
		err95 := argvalue0.Read(jsProt94)
		if err95 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg96 := flag.Arg(2)
		mbTrans97 := thrift.NewTMemoryBufferLen(len(arg96))
		defer mbTrans97.Close()
		_, err98 := mbTrans97.WriteString(arg96)
		if err98 != nil {
			Usage()
			return
		}
		factory99 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt100 := factory99.GetProtocol(mbTrans97)
		argvalue1 := aow_adserver.NewAowUIProccessedRequest()
		err101 := argvalue1.Read(jsProt100)
		if err101 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetTasks(value0, value1))
		fmt.Print("\n")
		break
	case "getSpecificTasks":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSpecificTasks requires 2 args")
			flag.Usage()
		}
		arg102 := flag.Arg(1)
		mbTrans103 := thrift.NewTMemoryBufferLen(len(arg102))
		defer mbTrans103.Close()
		_, err104 := mbTrans103.WriteString(arg102)
		if err104 != nil {
			Usage()
			return
		}
		factory105 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt106 := factory105.GetProtocol(mbTrans103)
		argvalue0 := aow_adserver.NewRequestHeader()
		err107 := argvalue0.Read(jsProt106)
		if err107 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg108 := flag.Arg(2)
		mbTrans109 := thrift.NewTMemoryBufferLen(len(arg108))
		defer mbTrans109.Close()
		_, err110 := mbTrans109.WriteString(arg108)
		if err110 != nil {
			Usage()
			return
		}
		factory111 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt112 := factory111.GetProtocol(mbTrans109)
		argvalue1 := aow_adserver.NewAowUIProccessedRequest()
		err113 := argvalue1.Read(jsProt112)
		if err113 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetSpecificTasks(value0, value1))
		fmt.Print("\n")
		break
	case "getPoint":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPoint requires 2 args")
			flag.Usage()
		}
		arg114 := flag.Arg(1)
		mbTrans115 := thrift.NewTMemoryBufferLen(len(arg114))
		defer mbTrans115.Close()
		_, err116 := mbTrans115.WriteString(arg114)
		if err116 != nil {
			Usage()
			return
		}
		factory117 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt118 := factory117.GetProtocol(mbTrans115)
		argvalue0 := aow_adserver.NewRequestHeader()
		err119 := argvalue0.Read(jsProt118)
		if err119 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg120 := flag.Arg(2)
		mbTrans121 := thrift.NewTMemoryBufferLen(len(arg120))
		defer mbTrans121.Close()
		_, err122 := mbTrans121.WriteString(arg120)
		if err122 != nil {
			Usage()
			return
		}
		factory123 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt124 := factory123.GetProtocol(mbTrans121)
		argvalue1 := aow_adserver.NewAowUIProccessedRequest()
		err125 := argvalue1.Read(jsProt124)
		if err125 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetPoint(value0, value1))
		fmt.Print("\n")
		break
	case "ConsumePoint":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ConsumePoint requires 2 args")
			flag.Usage()
		}
		arg126 := flag.Arg(1)
		mbTrans127 := thrift.NewTMemoryBufferLen(len(arg126))
		defer mbTrans127.Close()
		_, err128 := mbTrans127.WriteString(arg126)
		if err128 != nil {
			Usage()
			return
		}
		factory129 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt130 := factory129.GetProtocol(mbTrans127)
		argvalue0 := aow_adserver.NewRequestHeader()
		err131 := argvalue0.Read(jsProt130)
		if err131 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg132 := flag.Arg(2)
		mbTrans133 := thrift.NewTMemoryBufferLen(len(arg132))
		defer mbTrans133.Close()
		_, err134 := mbTrans133.WriteString(arg132)
		if err134 != nil {
			Usage()
			return
		}
		factory135 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt136 := factory135.GetProtocol(mbTrans133)
		argvalue1 := aow_adserver.NewAowUIProccessedRequest()
		err137 := argvalue1.Read(jsProt136)
		if err137 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ConsumePoint(value0, value1))
		fmt.Print("\n")
		break
	case "getBonus":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetBonus requires 2 args")
			flag.Usage()
		}
		arg138 := flag.Arg(1)
		mbTrans139 := thrift.NewTMemoryBufferLen(len(arg138))
		defer mbTrans139.Close()
		_, err140 := mbTrans139.WriteString(arg138)
		if err140 != nil {
			Usage()
			return
		}
		factory141 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt142 := factory141.GetProtocol(mbTrans139)
		argvalue0 := aow_adserver.NewRequestHeader()
		err143 := argvalue0.Read(jsProt142)
		if err143 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg144 := flag.Arg(2)
		mbTrans145 := thrift.NewTMemoryBufferLen(len(arg144))
		defer mbTrans145.Close()
		_, err146 := mbTrans145.WriteString(arg144)
		if err146 != nil {
			Usage()
			return
		}
		factory147 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt148 := factory147.GetProtocol(mbTrans145)
		argvalue1 := aow_adserver.NewAowUIProccessedRequest()
		err149 := argvalue1.Read(jsProt148)
		if err149 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetBonus(value0, value1))
		fmt.Print("\n")
		break
	case "bonusCallback":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "BonusCallback requires 4 args")
			flag.Usage()
		}
		arg150 := flag.Arg(1)
		mbTrans151 := thrift.NewTMemoryBufferLen(len(arg150))
		defer mbTrans151.Close()
		_, err152 := mbTrans151.WriteString(arg150)
		if err152 != nil {
			Usage()
			return
		}
		factory153 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt154 := factory153.GetProtocol(mbTrans151)
		argvalue0 := aow_adserver.NewRequestHeader()
		err155 := argvalue0.Read(jsProt154)
		if err155 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err156 := (strconv.Atoi(flag.Arg(2)))
		if err156 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err157 := (strconv.Atoi(flag.Arg(3)))
		if err157 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err158 := (strconv.Atoi(flag.Arg(4)))
		if err158 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.BonusCallback(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
