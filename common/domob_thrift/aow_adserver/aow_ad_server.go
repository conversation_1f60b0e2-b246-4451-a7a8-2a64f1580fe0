// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package aow_adserver

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/aow_adserver_types"
	"rtb_model_server/common/domob_thrift/aow_ui_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = aow_adserver_types.GoUnusedProtection__
var _ = aow_ui_types.GoUnusedProtection__

type AowAdServer interface {
	// 配置接口
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	GetConfig(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_ui_types.AowConfig, err error)
	// 根据id获取一个offer,作为offer详情的请求,与列表页请求的返回格式相同,
	// offers只有一个值,且offer详情包含更多的信息
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	GetOfferById(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.OfferResponse, err error)
	// 第一次请求offer列表
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	GetInitialOffers(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.OfferResponse, err error)
	// 根据offerid请求offer列表,用作翻页后的请求,请求单个offer详情时也使用这个接口
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	GetSpecificOffers(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.OfferResponse, err error)
	// 获取深度任务列表
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	GetTasks(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.OfferResponse, err error)
	// 根据id获取深度任务
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	GetSpecificTasks(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.OfferResponse, err error)
	// 获取用户的当前积分
	//
	// 如果系统中有这个用户的记录，则consumeStatus=CONSUME_SUCCESS,否则
	// consumeStatus = INVALID_USER，并且返回的积分都是0
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	GetPoint(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.PointResponse, err error)
	// 积分消费
	//
	// consumeStatus可能出现各种值，客户端应该判断一下
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	ConsumePoint(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.PointResponse, err error)
	// 获取额外奖励，包括累计奖励和单任务下载奖励
	//
	// Parameters:
	//  - Header
	//  - UiRequest
	GetBonus(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_ui_types.AowAccumulatedBonus, err error)
	// 流量云合作的回调接口，记录领取奖励设备id
	//
	// Parameters:
	//  - Header
	//  - Did
	//  - Bid
	//  - Btype
	BonusCallback(header *common.RequestHeader, did int32, bid int32, btype int32) (r bool, err error)
}

type AowAdServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewAowAdServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *AowAdServerClient {
	return &AowAdServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewAowAdServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *AowAdServerClient {
	return &AowAdServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 配置接口
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AowAdServerClient) GetConfig(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_ui_types.AowConfig, err error) {
	if err = p.sendGetConfig(header, ui_request); err != nil {
		return
	}
	return p.recvGetConfig()
}

func (p *AowAdServerClient) sendGetConfig(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getConfig", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetConfigArgs()
	args0.Header = header
	args0.UiRequest = ui_request
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AowAdServerClient) recvGetConfig() (value *aow_ui_types.AowConfig, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetConfigResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

// 根据id获取一个offer,作为offer详情的请求,与列表页请求的返回格式相同,
// offers只有一个值,且offer详情包含更多的信息
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AowAdServerClient) GetOfferById(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.OfferResponse, err error) {
	if err = p.sendGetOfferById(header, ui_request); err != nil {
		return
	}
	return p.recvGetOfferById()
}

func (p *AowAdServerClient) sendGetOfferById(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getOfferById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetOfferByIdArgs()
	args4.Header = header
	args4.UiRequest = ui_request
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AowAdServerClient) recvGetOfferById() (value *aow_adserver_types.OfferResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetOfferByIdResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	return
}

// 第一次请求offer列表
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AowAdServerClient) GetInitialOffers(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.OfferResponse, err error) {
	if err = p.sendGetInitialOffers(header, ui_request); err != nil {
		return
	}
	return p.recvGetInitialOffers()
}

func (p *AowAdServerClient) sendGetInitialOffers(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getInitialOffers", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetInitialOffersArgs()
	args8.Header = header
	args8.UiRequest = ui_request
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AowAdServerClient) recvGetInitialOffers() (value *aow_adserver_types.OfferResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetInitialOffersResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	return
}

// 根据offerid请求offer列表,用作翻页后的请求,请求单个offer详情时也使用这个接口
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AowAdServerClient) GetSpecificOffers(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.OfferResponse, err error) {
	if err = p.sendGetSpecificOffers(header, ui_request); err != nil {
		return
	}
	return p.recvGetSpecificOffers()
}

func (p *AowAdServerClient) sendGetSpecificOffers(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getSpecificOffers", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetSpecificOffersArgs()
	args12.Header = header
	args12.UiRequest = ui_request
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AowAdServerClient) recvGetSpecificOffers() (value *aow_adserver_types.OfferResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetSpecificOffersResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	return
}

// 获取深度任务列表
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AowAdServerClient) GetTasks(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.OfferResponse, err error) {
	if err = p.sendGetTasks(header, ui_request); err != nil {
		return
	}
	return p.recvGetTasks()
}

func (p *AowAdServerClient) sendGetTasks(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTasks", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewGetTasksArgs()
	args16.Header = header
	args16.UiRequest = ui_request
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AowAdServerClient) recvGetTasks() (value *aow_adserver_types.OfferResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewGetTasksResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	return
}

// 根据id获取深度任务
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AowAdServerClient) GetSpecificTasks(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.OfferResponse, err error) {
	if err = p.sendGetSpecificTasks(header, ui_request); err != nil {
		return
	}
	return p.recvGetSpecificTasks()
}

func (p *AowAdServerClient) sendGetSpecificTasks(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getSpecificTasks", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewGetSpecificTasksArgs()
	args20.Header = header
	args20.UiRequest = ui_request
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AowAdServerClient) recvGetSpecificTasks() (value *aow_adserver_types.OfferResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewGetSpecificTasksResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	return
}

// 获取用户的当前积分
//
// 如果系统中有这个用户的记录，则consumeStatus=CONSUME_SUCCESS,否则
// consumeStatus = INVALID_USER，并且返回的积分都是0
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AowAdServerClient) GetPoint(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.PointResponse, err error) {
	if err = p.sendGetPoint(header, ui_request); err != nil {
		return
	}
	return p.recvGetPoint()
}

func (p *AowAdServerClient) sendGetPoint(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPoint", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewGetPointArgs()
	args24.Header = header
	args24.UiRequest = ui_request
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AowAdServerClient) recvGetPoint() (value *aow_adserver_types.PointResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewGetPointResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	return
}

// 积分消费
//
// consumeStatus可能出现各种值，客户端应该判断一下
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AowAdServerClient) ConsumePoint(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_adserver_types.PointResponse, err error) {
	if err = p.sendConsumePoint(header, ui_request); err != nil {
		return
	}
	return p.recvConsumePoint()
}

func (p *AowAdServerClient) sendConsumePoint(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("ConsumePoint", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewConsumePointArgs()
	args28.Header = header
	args28.UiRequest = ui_request
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AowAdServerClient) recvConsumePoint() (value *aow_adserver_types.PointResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewConsumePointResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	return
}

// 获取额外奖励，包括累计奖励和单任务下载奖励
//
// Parameters:
//  - Header
//  - UiRequest
func (p *AowAdServerClient) GetBonus(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (r *aow_ui_types.AowAccumulatedBonus, err error) {
	if err = p.sendGetBonus(header, ui_request); err != nil {
		return
	}
	return p.recvGetBonus()
}

func (p *AowAdServerClient) sendGetBonus(header *common.RequestHeader, ui_request *aow_ui_types.AowUIProccessedRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getBonus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewGetBonusArgs()
	args32.Header = header
	args32.UiRequest = ui_request
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AowAdServerClient) recvGetBonus() (value *aow_ui_types.AowAccumulatedBonus, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewGetBonusResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	return
}

// 流量云合作的回调接口，记录领取奖励设备id
//
// Parameters:
//  - Header
//  - Did
//  - Bid
//  - Btype
func (p *AowAdServerClient) BonusCallback(header *common.RequestHeader, did int32, bid int32, btype int32) (r bool, err error) {
	if err = p.sendBonusCallback(header, did, bid, btype); err != nil {
		return
	}
	return p.recvBonusCallback()
}

func (p *AowAdServerClient) sendBonusCallback(header *common.RequestHeader, did int32, bid int32, btype int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("bonusCallback", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewBonusCallbackArgs()
	args36.Header = header
	args36.Did = did
	args36.Bid = bid
	args36.Btype = btype
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AowAdServerClient) recvBonusCallback() (value bool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewBonusCallbackResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	return
}

type AowAdServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      AowAdServer
}

func (p *AowAdServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *AowAdServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *AowAdServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewAowAdServerProcessor(handler AowAdServer) *AowAdServerProcessor {

	self40 := &AowAdServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self40.processorMap["getConfig"] = &aowAdServerProcessorGetConfig{handler: handler}
	self40.processorMap["getOfferById"] = &aowAdServerProcessorGetOfferById{handler: handler}
	self40.processorMap["getInitialOffers"] = &aowAdServerProcessorGetInitialOffers{handler: handler}
	self40.processorMap["getSpecificOffers"] = &aowAdServerProcessorGetSpecificOffers{handler: handler}
	self40.processorMap["getTasks"] = &aowAdServerProcessorGetTasks{handler: handler}
	self40.processorMap["getSpecificTasks"] = &aowAdServerProcessorGetSpecificTasks{handler: handler}
	self40.processorMap["getPoint"] = &aowAdServerProcessorGetPoint{handler: handler}
	self40.processorMap["ConsumePoint"] = &aowAdServerProcessorConsumePoint{handler: handler}
	self40.processorMap["getBonus"] = &aowAdServerProcessorGetBonus{handler: handler}
	self40.processorMap["bonusCallback"] = &aowAdServerProcessorBonusCallback{handler: handler}
	return self40
}

func (p *AowAdServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x41 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x41.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x41

}

type aowAdServerProcessorGetConfig struct {
	handler AowAdServer
}

func (p *aowAdServerProcessorGetConfig) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetConfigArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getConfig", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetConfigResult()
	if result.Success, err = p.handler.GetConfig(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getConfig: "+err.Error())
		oprot.WriteMessageBegin("getConfig", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getConfig", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type aowAdServerProcessorGetOfferById struct {
	handler AowAdServer
}

func (p *aowAdServerProcessorGetOfferById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetOfferByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getOfferById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetOfferByIdResult()
	if result.Success, err = p.handler.GetOfferById(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getOfferById: "+err.Error())
		oprot.WriteMessageBegin("getOfferById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getOfferById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type aowAdServerProcessorGetInitialOffers struct {
	handler AowAdServer
}

func (p *aowAdServerProcessorGetInitialOffers) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetInitialOffersArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getInitialOffers", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetInitialOffersResult()
	if result.Success, err = p.handler.GetInitialOffers(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getInitialOffers: "+err.Error())
		oprot.WriteMessageBegin("getInitialOffers", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getInitialOffers", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type aowAdServerProcessorGetSpecificOffers struct {
	handler AowAdServer
}

func (p *aowAdServerProcessorGetSpecificOffers) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetSpecificOffersArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getSpecificOffers", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetSpecificOffersResult()
	if result.Success, err = p.handler.GetSpecificOffers(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getSpecificOffers: "+err.Error())
		oprot.WriteMessageBegin("getSpecificOffers", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getSpecificOffers", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type aowAdServerProcessorGetTasks struct {
	handler AowAdServer
}

func (p *aowAdServerProcessorGetTasks) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTasksArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTasks", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTasksResult()
	if result.Success, err = p.handler.GetTasks(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTasks: "+err.Error())
		oprot.WriteMessageBegin("getTasks", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTasks", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type aowAdServerProcessorGetSpecificTasks struct {
	handler AowAdServer
}

func (p *aowAdServerProcessorGetSpecificTasks) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetSpecificTasksArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getSpecificTasks", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetSpecificTasksResult()
	if result.Success, err = p.handler.GetSpecificTasks(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getSpecificTasks: "+err.Error())
		oprot.WriteMessageBegin("getSpecificTasks", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getSpecificTasks", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type aowAdServerProcessorGetPoint struct {
	handler AowAdServer
}

func (p *aowAdServerProcessorGetPoint) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPointArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPointResult()
	if result.Success, err = p.handler.GetPoint(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPoint: "+err.Error())
		oprot.WriteMessageBegin("getPoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPoint", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type aowAdServerProcessorConsumePoint struct {
	handler AowAdServer
}

func (p *aowAdServerProcessorConsumePoint) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewConsumePointArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ConsumePoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewConsumePointResult()
	if result.Success, err = p.handler.ConsumePoint(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ConsumePoint: "+err.Error())
		oprot.WriteMessageBegin("ConsumePoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("ConsumePoint", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type aowAdServerProcessorGetBonus struct {
	handler AowAdServer
}

func (p *aowAdServerProcessorGetBonus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetBonusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getBonus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetBonusResult()
	if result.Success, err = p.handler.GetBonus(args.Header, args.UiRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getBonus: "+err.Error())
		oprot.WriteMessageBegin("getBonus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getBonus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type aowAdServerProcessorBonusCallback struct {
	handler AowAdServer
}

func (p *aowAdServerProcessorBonusCallback) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewBonusCallbackArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("bonusCallback", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewBonusCallbackResult()
	if result.Success, err = p.handler.BonusCallback(args.Header, args.Did, args.Bid, args.Btype); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing bonusCallback: "+err.Error())
		oprot.WriteMessageBegin("bonusCallback", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("bonusCallback", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetConfigArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *aow_ui_types.AowUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetConfigArgs() *GetConfigArgs {
	return &GetConfigArgs{}
}

func (p *GetConfigArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetConfigArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetConfigArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = aow_ui_types.NewAowUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetConfigArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getConfig_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetConfigArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetConfigArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetConfigArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetConfigArgs(%+v)", *p)
}

type GetConfigResult struct {
	Success *aow_ui_types.AowConfig `thrift:"success,0" json:"success"`
}

func NewGetConfigResult() *GetConfigResult {
	return &GetConfigResult{}
}

func (p *GetConfigResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetConfigResult) readField0(iprot thrift.TProtocol) error {
	p.Success = aow_ui_types.NewAowConfig()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetConfigResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getConfig_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetConfigResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetConfigResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetConfigResult(%+v)", *p)
}

type GetOfferByIdArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *aow_ui_types.AowUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetOfferByIdArgs() *GetOfferByIdArgs {
	return &GetOfferByIdArgs{}
}

func (p *GetOfferByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetOfferByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetOfferByIdArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = aow_ui_types.NewAowUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetOfferByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getOfferById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetOfferByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetOfferByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetOfferByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOfferByIdArgs(%+v)", *p)
}

type GetOfferByIdResult struct {
	Success *aow_adserver_types.OfferResponse `thrift:"success,0" json:"success"`
}

func NewGetOfferByIdResult() *GetOfferByIdResult {
	return &GetOfferByIdResult{}
}

func (p *GetOfferByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetOfferByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = aow_adserver_types.NewOfferResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetOfferByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getOfferById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetOfferByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetOfferByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOfferByIdResult(%+v)", *p)
}

type GetInitialOffersArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *aow_ui_types.AowUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetInitialOffersArgs() *GetInitialOffersArgs {
	return &GetInitialOffersArgs{}
}

func (p *GetInitialOffersArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetInitialOffersArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetInitialOffersArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = aow_ui_types.NewAowUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetInitialOffersArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getInitialOffers_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetInitialOffersArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetInitialOffersArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetInitialOffersArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetInitialOffersArgs(%+v)", *p)
}

type GetInitialOffersResult struct {
	Success *aow_adserver_types.OfferResponse `thrift:"success,0" json:"success"`
}

func NewGetInitialOffersResult() *GetInitialOffersResult {
	return &GetInitialOffersResult{}
}

func (p *GetInitialOffersResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetInitialOffersResult) readField0(iprot thrift.TProtocol) error {
	p.Success = aow_adserver_types.NewOfferResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetInitialOffersResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getInitialOffers_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetInitialOffersResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetInitialOffersResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetInitialOffersResult(%+v)", *p)
}

type GetSpecificOffersArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *aow_ui_types.AowUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetSpecificOffersArgs() *GetSpecificOffersArgs {
	return &GetSpecificOffersArgs{}
}

func (p *GetSpecificOffersArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificOffersArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetSpecificOffersArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = aow_ui_types.NewAowUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetSpecificOffersArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSpecificOffers_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificOffersArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetSpecificOffersArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetSpecificOffersArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSpecificOffersArgs(%+v)", *p)
}

type GetSpecificOffersResult struct {
	Success *aow_adserver_types.OfferResponse `thrift:"success,0" json:"success"`
}

func NewGetSpecificOffersResult() *GetSpecificOffersResult {
	return &GetSpecificOffersResult{}
}

func (p *GetSpecificOffersResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificOffersResult) readField0(iprot thrift.TProtocol) error {
	p.Success = aow_adserver_types.NewOfferResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetSpecificOffersResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSpecificOffers_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificOffersResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetSpecificOffersResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSpecificOffersResult(%+v)", *p)
}

type GetTasksArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *aow_ui_types.AowUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetTasksArgs() *GetTasksArgs {
	return &GetTasksArgs{}
}

func (p *GetTasksArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTasksArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetTasksArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = aow_ui_types.NewAowUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetTasksArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTasks_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTasksArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetTasksArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetTasksArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTasksArgs(%+v)", *p)
}

type GetTasksResult struct {
	Success *aow_adserver_types.OfferResponse `thrift:"success,0" json:"success"`
}

func NewGetTasksResult() *GetTasksResult {
	return &GetTasksResult{}
}

func (p *GetTasksResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTasksResult) readField0(iprot thrift.TProtocol) error {
	p.Success = aow_adserver_types.NewOfferResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetTasksResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTasks_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTasksResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTasksResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTasksResult(%+v)", *p)
}

type GetSpecificTasksArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *aow_ui_types.AowUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetSpecificTasksArgs() *GetSpecificTasksArgs {
	return &GetSpecificTasksArgs{}
}

func (p *GetSpecificTasksArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificTasksArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetSpecificTasksArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = aow_ui_types.NewAowUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetSpecificTasksArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSpecificTasks_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificTasksArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetSpecificTasksArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetSpecificTasksArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSpecificTasksArgs(%+v)", *p)
}

type GetSpecificTasksResult struct {
	Success *aow_adserver_types.OfferResponse `thrift:"success,0" json:"success"`
}

func NewGetSpecificTasksResult() *GetSpecificTasksResult {
	return &GetSpecificTasksResult{}
}

func (p *GetSpecificTasksResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificTasksResult) readField0(iprot thrift.TProtocol) error {
	p.Success = aow_adserver_types.NewOfferResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetSpecificTasksResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSpecificTasks_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificTasksResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetSpecificTasksResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSpecificTasksResult(%+v)", *p)
}

type GetPointArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *aow_ui_types.AowUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetPointArgs() *GetPointArgs {
	return &GetPointArgs{}
}

func (p *GetPointArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPointArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetPointArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = aow_ui_types.NewAowUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetPointArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPoint_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPointArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetPointArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetPointArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPointArgs(%+v)", *p)
}

type GetPointResult struct {
	Success *aow_adserver_types.PointResponse `thrift:"success,0" json:"success"`
}

func NewGetPointResult() *GetPointResult {
	return &GetPointResult{}
}

func (p *GetPointResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPointResult) readField0(iprot thrift.TProtocol) error {
	p.Success = aow_adserver_types.NewPointResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetPointResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPoint_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPointResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPointResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPointResult(%+v)", *p)
}

type ConsumePointArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *aow_ui_types.AowUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewConsumePointArgs() *ConsumePointArgs {
	return &ConsumePointArgs{}
}

func (p *ConsumePointArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConsumePointArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ConsumePointArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = aow_ui_types.NewAowUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *ConsumePointArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ConsumePoint_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConsumePointArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ConsumePointArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *ConsumePointArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumePointArgs(%+v)", *p)
}

type ConsumePointResult struct {
	Success *aow_adserver_types.PointResponse `thrift:"success,0" json:"success"`
}

func NewConsumePointResult() *ConsumePointResult {
	return &ConsumePointResult{}
}

func (p *ConsumePointResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConsumePointResult) readField0(iprot thrift.TProtocol) error {
	p.Success = aow_adserver_types.NewPointResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ConsumePointResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ConsumePoint_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConsumePointResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ConsumePointResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumePointResult(%+v)", *p)
}

type GetBonusArgs struct {
	Header    *common.RequestHeader                `thrift:"header,1" json:"header"`
	UiRequest *aow_ui_types.AowUIProccessedRequest `thrift:"ui_request,2" json:"ui_request"`
}

func NewGetBonusArgs() *GetBonusArgs {
	return &GetBonusArgs{}
}

func (p *GetBonusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetBonusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetBonusArgs) readField2(iprot thrift.TProtocol) error {
	p.UiRequest = aow_ui_types.NewAowUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *GetBonusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getBonus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetBonusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetBonusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *GetBonusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetBonusArgs(%+v)", *p)
}

type GetBonusResult struct {
	Success *aow_ui_types.AowAccumulatedBonus `thrift:"success,0" json:"success"`
}

func NewGetBonusResult() *GetBonusResult {
	return &GetBonusResult{}
}

func (p *GetBonusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetBonusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = aow_ui_types.NewAowAccumulatedBonus()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetBonusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getBonus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetBonusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetBonusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetBonusResult(%+v)", *p)
}

type BonusCallbackArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Did    int32                 `thrift:"did,2" json:"did"`
	Bid    int32                 `thrift:"bid,3" json:"bid"`
	Btype  int32                 `thrift:"btype,4" json:"btype"`
}

func NewBonusCallbackArgs() *BonusCallbackArgs {
	return &BonusCallbackArgs{}
}

func (p *BonusCallbackArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BonusCallbackArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *BonusCallbackArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Did = v
	}
	return nil
}

func (p *BonusCallbackArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Bid = v
	}
	return nil
}

func (p *BonusCallbackArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Btype = v
	}
	return nil
}

func (p *BonusCallbackArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("bonusCallback_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BonusCallbackArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *BonusCallbackArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("did", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:did: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Did)); err != nil {
		return fmt.Errorf("%T.did (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:did: %s", p, err)
	}
	return err
}

func (p *BonusCallbackArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:bid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Bid)); err != nil {
		return fmt.Errorf("%T.bid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:bid: %s", p, err)
	}
	return err
}

func (p *BonusCallbackArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("btype", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:btype: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Btype)); err != nil {
		return fmt.Errorf("%T.btype (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:btype: %s", p, err)
	}
	return err
}

func (p *BonusCallbackArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BonusCallbackArgs(%+v)", *p)
}

type BonusCallbackResult struct {
	Success bool `thrift:"success,0" json:"success"`
}

func NewBonusCallbackResult() *BonusCallbackResult {
	return &BonusCallbackResult{}
}

func (p *BonusCallbackResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BonusCallbackResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *BonusCallbackResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("bonusCallback_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BonusCallbackResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *BonusCallbackResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BonusCallbackResult(%+v)", *p)
}
