// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package detect_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/mediainfo_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = mediainfo_types.GoUnusedProtection__
var GoUnusedProtection__ int

//探测返回结果码
type DetectResultCode int64

const (
	DetectResultCode_DRC_OK     DetectResultCode = 0
	DetectResultCode_DRC_FAILED DetectResultCode = 1
)

func (p DetectResultCode) String() string {
	switch p {
	case DetectResultCode_DRC_OK:
		return "DetectResultCode_DRC_OK"
	case DetectResultCode_DRC_FAILED:
		return "DetectResultCode_DRC_FAILED"
	}
	return "<UNSET>"
}

func DetectResultCodeFromString(s string) (DetectResultCode, error) {
	switch s {
	case "DetectResultCode_DRC_OK":
		return DetectResultCode_DRC_OK, nil
	case "DetectResultCode_DRC_FAILED":
		return DetectResultCode_DRC_FAILED, nil
	}
	return DetectResultCode(math.MinInt32 - 1), fmt.Errorf("not a valid DetectResultCode string")
}

//广告代码
//当DetectAdStatus为DAS_TEST_AD时，用于标识当需要出测试广告时，出什么样的测试广告
//当DetectAdStatus为DAS_NO_AD时，用于标识什么原因不出广告
type AdCode int64

const (
	AdCode_AC_NORMAL                          AdCode = 0
	AdCode_AC_USER_NOT_EXIST                  AdCode = 1
	AdCode_AC_USER_FORBIDDEN                  AdCode = 2
	AdCode_AC_MEDIA_NOT_EXIST                 AdCode = 10
	AdCode_AC_MEDIA_PAUSED                    AdCode = 11
	AdCode_AC_MEDIA_PENDING_UPLOAD            AdCode = 12
	AdCode_AC_MEDIA_PENDING_APPROVAL          AdCode = 13
	AdCode_AC_MEDIA_PENDING_APPROVAL_REUPLOAD AdCode = 14
	AdCode_AC_MEDIA_REJECTED                  AdCode = 15
	AdCode_AC_MEDIA_FORBIDDEN                 AdCode = 16
	AdCode_AC_MEDIA_PUBID_FORGED              AdCode = 17
	AdCode_AC_MEDIA_SDK_PLATFORM_UNMATCH      AdCode = 18
	AdCode_AC_MEDIA_PACKAGE_NAME_UNMATCH      AdCode = 19
	AdCode_AC_MEDIA_NO_PACKAGE_INFO           AdCode = 20
	AdCode_AC_MEDIA_UNKNOWN_STATUS            AdCode = 21
	AdCode_AC_PUBID_INVALID_ERROR             AdCode = 22
	AdCode_AC_IDV_INVALID_ERROR               AdCode = 23
	AdCode_AC_PARAM_INVALID_ERROR             AdCode = 24
	AdCode_AC_PLACEMENT_PAUSED                AdCode = 25
	AdCode_AC_OTHER_INTERNAL_ERROR            AdCode = 404
	AdCode_AC_SERVER_INTERNAL_ERROR           AdCode = 500
	AdCode_AC_SERVER_BUSY_ERROR               AdCode = 502
	AdCode_AC_UNKNOWN_ERROR                   AdCode = 600
)

func (p AdCode) String() string {
	switch p {
	case AdCode_AC_NORMAL:
		return "AdCode_AC_NORMAL"
	case AdCode_AC_USER_NOT_EXIST:
		return "AdCode_AC_USER_NOT_EXIST"
	case AdCode_AC_USER_FORBIDDEN:
		return "AdCode_AC_USER_FORBIDDEN"
	case AdCode_AC_MEDIA_NOT_EXIST:
		return "AdCode_AC_MEDIA_NOT_EXIST"
	case AdCode_AC_MEDIA_PAUSED:
		return "AdCode_AC_MEDIA_PAUSED"
	case AdCode_AC_MEDIA_PENDING_UPLOAD:
		return "AdCode_AC_MEDIA_PENDING_UPLOAD"
	case AdCode_AC_MEDIA_PENDING_APPROVAL:
		return "AdCode_AC_MEDIA_PENDING_APPROVAL"
	case AdCode_AC_MEDIA_PENDING_APPROVAL_REUPLOAD:
		return "AdCode_AC_MEDIA_PENDING_APPROVAL_REUPLOAD"
	case AdCode_AC_MEDIA_REJECTED:
		return "AdCode_AC_MEDIA_REJECTED"
	case AdCode_AC_MEDIA_FORBIDDEN:
		return "AdCode_AC_MEDIA_FORBIDDEN"
	case AdCode_AC_MEDIA_PUBID_FORGED:
		return "AdCode_AC_MEDIA_PUBID_FORGED"
	case AdCode_AC_MEDIA_SDK_PLATFORM_UNMATCH:
		return "AdCode_AC_MEDIA_SDK_PLATFORM_UNMATCH"
	case AdCode_AC_MEDIA_PACKAGE_NAME_UNMATCH:
		return "AdCode_AC_MEDIA_PACKAGE_NAME_UNMATCH"
	case AdCode_AC_MEDIA_NO_PACKAGE_INFO:
		return "AdCode_AC_MEDIA_NO_PACKAGE_INFO"
	case AdCode_AC_MEDIA_UNKNOWN_STATUS:
		return "AdCode_AC_MEDIA_UNKNOWN_STATUS"
	case AdCode_AC_PUBID_INVALID_ERROR:
		return "AdCode_AC_PUBID_INVALID_ERROR"
	case AdCode_AC_IDV_INVALID_ERROR:
		return "AdCode_AC_IDV_INVALID_ERROR"
	case AdCode_AC_PARAM_INVALID_ERROR:
		return "AdCode_AC_PARAM_INVALID_ERROR"
	case AdCode_AC_PLACEMENT_PAUSED:
		return "AdCode_AC_PLACEMENT_PAUSED"
	case AdCode_AC_OTHER_INTERNAL_ERROR:
		return "AdCode_AC_OTHER_INTERNAL_ERROR"
	case AdCode_AC_SERVER_INTERNAL_ERROR:
		return "AdCode_AC_SERVER_INTERNAL_ERROR"
	case AdCode_AC_SERVER_BUSY_ERROR:
		return "AdCode_AC_SERVER_BUSY_ERROR"
	case AdCode_AC_UNKNOWN_ERROR:
		return "AdCode_AC_UNKNOWN_ERROR"
	}
	return "<UNSET>"
}

func AdCodeFromString(s string) (AdCode, error) {
	switch s {
	case "AdCode_AC_NORMAL":
		return AdCode_AC_NORMAL, nil
	case "AdCode_AC_USER_NOT_EXIST":
		return AdCode_AC_USER_NOT_EXIST, nil
	case "AdCode_AC_USER_FORBIDDEN":
		return AdCode_AC_USER_FORBIDDEN, nil
	case "AdCode_AC_MEDIA_NOT_EXIST":
		return AdCode_AC_MEDIA_NOT_EXIST, nil
	case "AdCode_AC_MEDIA_PAUSED":
		return AdCode_AC_MEDIA_PAUSED, nil
	case "AdCode_AC_MEDIA_PENDING_UPLOAD":
		return AdCode_AC_MEDIA_PENDING_UPLOAD, nil
	case "AdCode_AC_MEDIA_PENDING_APPROVAL":
		return AdCode_AC_MEDIA_PENDING_APPROVAL, nil
	case "AdCode_AC_MEDIA_PENDING_APPROVAL_REUPLOAD":
		return AdCode_AC_MEDIA_PENDING_APPROVAL_REUPLOAD, nil
	case "AdCode_AC_MEDIA_REJECTED":
		return AdCode_AC_MEDIA_REJECTED, nil
	case "AdCode_AC_MEDIA_FORBIDDEN":
		return AdCode_AC_MEDIA_FORBIDDEN, nil
	case "AdCode_AC_MEDIA_PUBID_FORGED":
		return AdCode_AC_MEDIA_PUBID_FORGED, nil
	case "AdCode_AC_MEDIA_SDK_PLATFORM_UNMATCH":
		return AdCode_AC_MEDIA_SDK_PLATFORM_UNMATCH, nil
	case "AdCode_AC_MEDIA_PACKAGE_NAME_UNMATCH":
		return AdCode_AC_MEDIA_PACKAGE_NAME_UNMATCH, nil
	case "AdCode_AC_MEDIA_NO_PACKAGE_INFO":
		return AdCode_AC_MEDIA_NO_PACKAGE_INFO, nil
	case "AdCode_AC_MEDIA_UNKNOWN_STATUS":
		return AdCode_AC_MEDIA_UNKNOWN_STATUS, nil
	case "AdCode_AC_PUBID_INVALID_ERROR":
		return AdCode_AC_PUBID_INVALID_ERROR, nil
	case "AdCode_AC_IDV_INVALID_ERROR":
		return AdCode_AC_IDV_INVALID_ERROR, nil
	case "AdCode_AC_PARAM_INVALID_ERROR":
		return AdCode_AC_PARAM_INVALID_ERROR, nil
	case "AdCode_AC_PLACEMENT_PAUSED":
		return AdCode_AC_PLACEMENT_PAUSED, nil
	case "AdCode_AC_OTHER_INTERNAL_ERROR":
		return AdCode_AC_OTHER_INTERNAL_ERROR, nil
	case "AdCode_AC_SERVER_INTERNAL_ERROR":
		return AdCode_AC_SERVER_INTERNAL_ERROR, nil
	case "AdCode_AC_SERVER_BUSY_ERROR":
		return AdCode_AC_SERVER_BUSY_ERROR, nil
	case "AdCode_AC_UNKNOWN_ERROR":
		return AdCode_AC_UNKNOWN_ERROR, nil
	}
	return AdCode(math.MinInt32 - 1), fmt.Errorf("not a valid AdCode string")
}

//可出广告状态，用于标识对于特定的请求，包括pubid、imei、uuid的组合，是否出广告，出什么样的广告
type DetectAdStatus int64

const (
	DetectAdStatus_DAS_NORMAL  DetectAdStatus = 0
	DetectAdStatus_DAS_TEST_AD DetectAdStatus = 1
	DetectAdStatus_DAS_NO_AD   DetectAdStatus = 2
)

func (p DetectAdStatus) String() string {
	switch p {
	case DetectAdStatus_DAS_NORMAL:
		return "DetectAdStatus_DAS_NORMAL"
	case DetectAdStatus_DAS_TEST_AD:
		return "DetectAdStatus_DAS_TEST_AD"
	case DetectAdStatus_DAS_NO_AD:
		return "DetectAdStatus_DAS_NO_AD"
	}
	return "<UNSET>"
}

func DetectAdStatusFromString(s string) (DetectAdStatus, error) {
	switch s {
	case "DetectAdStatus_DAS_NORMAL":
		return DetectAdStatus_DAS_NORMAL, nil
	case "DetectAdStatus_DAS_TEST_AD":
		return DetectAdStatus_DAS_TEST_AD, nil
	case "DetectAdStatus_DAS_NO_AD":
		return DetectAdStatus_DAS_NO_AD, nil
	}
	return DetectAdStatus(math.MinInt32 - 1), fmt.Errorf("not a valid DetectAdStatus string")
}

type TimeInt common.TimeInt

type SDKPlatform common.SDKPlatform

type RequestHeader *common.RequestHeader

type MediaType common.MediaType

type MediaTestModeSetting mediainfo_types.MediaTestModeSetting

type UidInt common.UidInt

type MediaIdInt common.IdInt

type PlacementIdInt common.IdInt

type Placement *mediainfo_types.Placement

type DetectRequest struct {
	Sdk           string  `thrift:"sdk,1" json:"sdk"`
	Rt            string  `thrift:"rt,2" json:"rt"`
	Ts            string  `thrift:"ts,3" json:"ts"`
	SdkUa         string  `thrift:"sdk_ua,4" json:"sdk_ua"`
	Ipb           string  `thrift:"ipb,5" json:"ipb"`
	Idv           string  `thrift:"idv,6" json:"idv"`
	V             string  `thrift:"v,7" json:"v"`
	Sv            int32   `thrift:"sv,8" json:"sv"`
	L             string  `thrift:"l,9" json:"l"`
	F             string  `thrift:"f,10" json:"f"`
	E             string  `thrift:"e,11" json:"e"`
	Cid           string  `thrift:"cid,12" json:"cid"`
	LmConfig      TimeInt `thrift:"lm_config,13" json:"lm_config"`
	LmRes         TimeInt `thrift:"lm_res,14" json:"lm_res"`
	Avg           string  `thrift:"avg,15" json:"avg"`
	Apn           string  `thrift:"apn,16" json:"apn"`
	Network       string  `thrift:"network,17" json:"network"`
	PbIdentifier  string  `thrift:"pb_identifier,18" json:"pb_identifier"`
	PbVersionCode string  `thrift:"pb_version_code,19" json:"pb_version_code"`
	PbVersionName string  `thrift:"pb_version_name,20" json:"pb_version_name"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	SdkPlatform    SDKPlatform    `thrift:"sdk_platform,50" json:"sdk_platform"`
	FirstVisitTime TimeInt        `thrift:"firstVisitTime,51" json:"firstVisitTime"`
	Uid            UidInt         `thrift:"uid,52" json:"uid"`
	Mid            MediaIdInt     `thrift:"mid,53" json:"mid"`
	Pmid           PlacementIdInt `thrift:"pmid,54" json:"pmid"`
}

func NewDetectRequest() *DetectRequest {
	return &DetectRequest{
		SdkPlatform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DetectRequest) IsSetSdkPlatform() bool {
	return int64(p.SdkPlatform) != math.MinInt32-1
}

func (p *DetectRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.I32 {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I64 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I32 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.I32 {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I32 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DetectRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Sdk = v
	}
	return nil
}

func (p *DetectRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Rt = v
	}
	return nil
}

func (p *DetectRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Ts = v
	}
	return nil
}

func (p *DetectRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SdkUa = v
	}
	return nil
}

func (p *DetectRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *DetectRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Idv = v
	}
	return nil
}

func (p *DetectRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.V = v
	}
	return nil
}

func (p *DetectRequest) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *DetectRequest) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.L = v
	}
	return nil
}

func (p *DetectRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.F = v
	}
	return nil
}

func (p *DetectRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.E = v
	}
	return nil
}

func (p *DetectRequest) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *DetectRequest) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.LmConfig = TimeInt(v)
	}
	return nil
}

func (p *DetectRequest) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.LmRes = TimeInt(v)
	}
	return nil
}

func (p *DetectRequest) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Avg = v
	}
	return nil
}

func (p *DetectRequest) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Apn = v
	}
	return nil
}

func (p *DetectRequest) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *DetectRequest) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.PbIdentifier = v
	}
	return nil
}

func (p *DetectRequest) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.PbVersionCode = v
	}
	return nil
}

func (p *DetectRequest) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.PbVersionName = v
	}
	return nil
}

func (p *DetectRequest) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.SdkPlatform = SDKPlatform(v)
	}
	return nil
}

func (p *DetectRequest) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.FirstVisitTime = TimeInt(v)
	}
	return nil
}

func (p *DetectRequest) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *DetectRequest) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.Mid = MediaIdInt(v)
	}
	return nil
}

func (p *DetectRequest) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.Pmid = PlacementIdInt(v)
	}
	return nil
}

func (p *DetectRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DetectRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DetectRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sdk: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sdk)); err != nil {
		return fmt.Errorf("%T.sdk (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sdk: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rt", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:rt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Rt)); err != nil {
		return fmt.Errorf("%T.rt (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:rt: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ts", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ts: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ts)); err != nil {
		return fmt.Errorf("%T.ts (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ts: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk_ua", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:sdk_ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SdkUa)); err != nil {
		return fmt.Errorf("%T.sdk_ua (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:sdk_ua: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ipb: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idv", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:idv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idv)); err != nil {
		return fmt.Errorf("%T.idv (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:idv: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("v", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:v: %s", p, err)
	}
	if err := oprot.WriteString(string(p.V)); err != nil {
		return fmt.Errorf("%T.v (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:v: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:sv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:sv: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("l", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:l: %s", p, err)
	}
	if err := oprot.WriteString(string(p.L)); err != nil {
		return fmt.Errorf("%T.l (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:l: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("f", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:f: %s", p, err)
	}
	if err := oprot.WriteString(string(p.F)); err != nil {
		return fmt.Errorf("%T.f (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:f: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("e", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:e: %s", p, err)
	}
	if err := oprot.WriteString(string(p.E)); err != nil {
		return fmt.Errorf("%T.e (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:e: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:cid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:cid: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lm_config", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:lm_config: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LmConfig)); err != nil {
		return fmt.Errorf("%T.lm_config (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:lm_config: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lm_res", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:lm_res: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LmRes)); err != nil {
		return fmt.Errorf("%T.lm_res (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:lm_res: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("avg", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:avg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Avg)); err != nil {
		return fmt.Errorf("%T.avg (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:avg: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("apn", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:apn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Apn)); err != nil {
		return fmt.Errorf("%T.apn (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:apn: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:network: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Network)); err != nil {
		return fmt.Errorf("%T.network (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:network: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pb_identifier", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:pb_identifier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PbIdentifier)); err != nil {
		return fmt.Errorf("%T.pb_identifier (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:pb_identifier: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pb_version_code", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:pb_version_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PbVersionCode)); err != nil {
		return fmt.Errorf("%T.pb_version_code (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:pb_version_code: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pb_version_name", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:pb_version_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PbVersionName)); err != nil {
		return fmt.Errorf("%T.pb_version_name (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:pb_version_name: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk_platform", thrift.I32, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:sdk_platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdkPlatform)); err != nil {
		return fmt.Errorf("%T.sdk_platform (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:sdk_platform: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("firstVisitTime", thrift.I64, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:firstVisitTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FirstVisitTime)); err != nil {
		return fmt.Errorf("%T.firstVisitTime (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:firstVisitTime: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:uid: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:mid: %s", p, err)
	}
	return err
}

func (p *DetectRequest) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pmid", thrift.I32, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:pmid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pmid)); err != nil {
		return fmt.Errorf("%T.pmid (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:pmid: %s", p, err)
	}
	return err
}

func (p *DetectRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DetectRequest(%+v)", *p)
}

type AppUpdate struct {
	AppName              string `thrift:"appName,1" json:"appName"`
	NoticeType           int32  `thrift:"noticeType,2" json:"noticeType"`
	VersionCode          string `thrift:"versionCode,3" json:"versionCode"`
	VersionName          string `thrift:"versionName,4" json:"versionName"`
	AppResId             int32  `thrift:"appResId,5" json:"appResId"`
	ItunesUrl            string `thrift:"itunesUrl,6" json:"itunesUrl"`
	PackageSize          int32  `thrift:"packageSize,7" json:"packageSize"`
	PackageMd5           string `thrift:"packageMd5,8" json:"packageMd5"`
	UpdateLog            string `thrift:"updateLog,9" json:"updateLog"`
	ForceUpdate          bool   `thrift:"forceUpdate,10" json:"forceUpdate"`
	Skip                 bool   `thrift:"skip,11" json:"skip"`
	NextReminderType     int32  `thrift:"nextReminderType,12" json:"nextReminderType"`
	NextReminderInterval int32  `thrift:"nextReminderInterval,13" json:"nextReminderInterval"`
}

func NewAppUpdate() *AppUpdate {
	return &AppUpdate{}
}

func (p *AppUpdate) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppUpdate) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *AppUpdate) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.NoticeType = v
	}
	return nil
}

func (p *AppUpdate) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.VersionCode = v
	}
	return nil
}

func (p *AppUpdate) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.VersionName = v
	}
	return nil
}

func (p *AppUpdate) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AppResId = v
	}
	return nil
}

func (p *AppUpdate) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ItunesUrl = v
	}
	return nil
}

func (p *AppUpdate) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PackageSize = v
	}
	return nil
}

func (p *AppUpdate) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.PackageMd5 = v
	}
	return nil
}

func (p *AppUpdate) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.UpdateLog = v
	}
	return nil
}

func (p *AppUpdate) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ForceUpdate = v
	}
	return nil
}

func (p *AppUpdate) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Skip = v
	}
	return nil
}

func (p *AppUpdate) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.NextReminderType = v
	}
	return nil
}

func (p *AppUpdate) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.NextReminderInterval = v
	}
	return nil
}

func (p *AppUpdate) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppUpdate"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppUpdate) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appName", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:appName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.appName (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:appName: %s", p, err)
	}
	return err
}

func (p *AppUpdate) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("noticeType", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:noticeType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.NoticeType)); err != nil {
		return fmt.Errorf("%T.noticeType (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:noticeType: %s", p, err)
	}
	return err
}

func (p *AppUpdate) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("versionCode", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:versionCode: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VersionCode)); err != nil {
		return fmt.Errorf("%T.versionCode (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:versionCode: %s", p, err)
	}
	return err
}

func (p *AppUpdate) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("versionName", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:versionName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VersionName)); err != nil {
		return fmt.Errorf("%T.versionName (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:versionName: %s", p, err)
	}
	return err
}

func (p *AppUpdate) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appResId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:appResId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppResId)); err != nil {
		return fmt.Errorf("%T.appResId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:appResId: %s", p, err)
	}
	return err
}

func (p *AppUpdate) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunesUrl", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:itunesUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ItunesUrl)); err != nil {
		return fmt.Errorf("%T.itunesUrl (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:itunesUrl: %s", p, err)
	}
	return err
}

func (p *AppUpdate) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageSize", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:packageSize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PackageSize)); err != nil {
		return fmt.Errorf("%T.packageSize (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:packageSize: %s", p, err)
	}
	return err
}

func (p *AppUpdate) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageMd5", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:packageMd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageMd5)); err != nil {
		return fmt.Errorf("%T.packageMd5 (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:packageMd5: %s", p, err)
	}
	return err
}

func (p *AppUpdate) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("updateLog", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:updateLog: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UpdateLog)); err != nil {
		return fmt.Errorf("%T.updateLog (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:updateLog: %s", p, err)
	}
	return err
}

func (p *AppUpdate) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("forceUpdate", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:forceUpdate: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ForceUpdate)); err != nil {
		return fmt.Errorf("%T.forceUpdate (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:forceUpdate: %s", p, err)
	}
	return err
}

func (p *AppUpdate) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("skip", thrift.BOOL, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:skip: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Skip)); err != nil {
		return fmt.Errorf("%T.skip (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:skip: %s", p, err)
	}
	return err
}

func (p *AppUpdate) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nextReminderType", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:nextReminderType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.NextReminderType)); err != nil {
		return fmt.Errorf("%T.nextReminderType (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:nextReminderType: %s", p, err)
	}
	return err
}

func (p *AppUpdate) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nextReminderInterval", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:nextReminderInterval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.NextReminderInterval)); err != nil {
		return fmt.Errorf("%T.nextReminderInterval (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:nextReminderInterval: %s", p, err)
	}
	return err
}

func (p *AppUpdate) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppUpdate(%+v)", *p)
}

type AppRate struct {
	NoticeType           int32  `thrift:"noticeType,1" json:"noticeType"`
	RateUrl              string `thrift:"rateUrl,2" json:"rateUrl"`
	Content              string `thrift:"content,3" json:"content"`
	SkipAllowed          bool   `thrift:"skipAllowed,4" json:"skipAllowed"`
	NextReminderType     int32  `thrift:"nextReminderType,5" json:"nextReminderType"`
	NextReminderInterval int32  `thrift:"nextReminderInterval,6" json:"nextReminderInterval"`
}

func NewAppRate() *AppRate {
	return &AppRate{}
}

func (p *AppRate) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppRate) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.NoticeType = v
	}
	return nil
}

func (p *AppRate) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.RateUrl = v
	}
	return nil
}

func (p *AppRate) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppRate) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SkipAllowed = v
	}
	return nil
}

func (p *AppRate) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.NextReminderType = v
	}
	return nil
}

func (p *AppRate) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.NextReminderInterval = v
	}
	return nil
}

func (p *AppRate) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppRate"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppRate) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("noticeType", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:noticeType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.NoticeType)); err != nil {
		return fmt.Errorf("%T.noticeType (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:noticeType: %s", p, err)
	}
	return err
}

func (p *AppRate) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rateUrl", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:rateUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RateUrl)); err != nil {
		return fmt.Errorf("%T.rateUrl (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:rateUrl: %s", p, err)
	}
	return err
}

func (p *AppRate) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:content: %s", p, err)
	}
	return err
}

func (p *AppRate) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("skipAllowed", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:skipAllowed: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.SkipAllowed)); err != nil {
		return fmt.Errorf("%T.skipAllowed (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:skipAllowed: %s", p, err)
	}
	return err
}

func (p *AppRate) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nextReminderType", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:nextReminderType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.NextReminderType)); err != nil {
		return fmt.Errorf("%T.nextReminderType (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:nextReminderType: %s", p, err)
	}
	return err
}

func (p *AppRate) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nextReminderInterval", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:nextReminderInterval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.NextReminderInterval)); err != nil {
		return fmt.Errorf("%T.nextReminderInterval (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:nextReminderInterval: %s", p, err)
	}
	return err
}

func (p *AppRate) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppRate(%+v)", *p)
}

type DetectResponse struct {
	RefreshInterval int32                `thrift:"refresh_interval,1" json:"refresh_interval"`
	Disabled        bool                 `thrift:"disabled,2" json:"disabled"`
	PrisionTm       TimeInt              `thrift:"prision_tm,3" json:"prision_tm"`
	ConfigTm        TimeInt              `thrift:"config_tm,4" json:"config_tm"`
	ResTm           TimeInt              `thrift:"res_tm,5" json:"res_tm"`
	TestMode        MediaTestModeSetting `thrift:"testMode,6" json:"testMode"`
	ResName         []string             `thrift:"res_name,7" json:"res_name"`
	CookieId        string               `thrift:"cookieId,8" json:"cookieId"`
	Debug           bool                 `thrift:"debug,9" json:"debug"`
	DetectAdStatus  DetectAdStatus       `thrift:"detectAdStatus,10" json:"detectAdStatus"`
	AdCode          AdCode               `thrift:"adCode,11" json:"adCode"`
	IgnorePn        bool                 `thrift:"ignorePn,12" json:"ignorePn"`
	MediaType       MediaType            `thrift:"mediaType,13" json:"mediaType"`
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	AppUpdate *AppUpdate                 `thrift:"appUpdate,50" json:"appUpdate"`
	AppRate   *AppRate                   `thrift:"appRate,51" json:"appRate"`
	Placement *mediainfo_types.Placement `thrift:"placement,52" json:"placement"`
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	// unused field # 97
	// unused field # 98
	// unused field # 99
	// unused field # 100
	// unused field # 101
	// unused field # 102
	// unused field # 103
	// unused field # 104
	// unused field # 105
	// unused field # 106
	// unused field # 107
	// unused field # 108
	// unused field # 109
	// unused field # 110
	// unused field # 111
	// unused field # 112
	// unused field # 113
	// unused field # 114
	// unused field # 115
	// unused field # 116
	// unused field # 117
	// unused field # 118
	// unused field # 119
	// unused field # 120
	// unused field # 121
	// unused field # 122
	// unused field # 123
	// unused field # 124
	// unused field # 125
	// unused field # 126
	// unused field # 127
	// unused field # 128
	// unused field # 129
	// unused field # 130
	// unused field # 131
	// unused field # 132
	// unused field # 133
	// unused field # 134
	// unused field # 135
	// unused field # 136
	// unused field # 137
	// unused field # 138
	// unused field # 139
	// unused field # 140
	// unused field # 141
	// unused field # 142
	// unused field # 143
	// unused field # 144
	// unused field # 145
	// unused field # 146
	// unused field # 147
	// unused field # 148
	// unused field # 149
	// unused field # 150
	// unused field # 151
	// unused field # 152
	// unused field # 153
	// unused field # 154
	// unused field # 155
	// unused field # 156
	// unused field # 157
	// unused field # 158
	// unused field # 159
	// unused field # 160
	// unused field # 161
	// unused field # 162
	// unused field # 163
	// unused field # 164
	// unused field # 165
	// unused field # 166
	// unused field # 167
	// unused field # 168
	// unused field # 169
	// unused field # 170
	// unused field # 171
	// unused field # 172
	// unused field # 173
	// unused field # 174
	// unused field # 175
	// unused field # 176
	// unused field # 177
	// unused field # 178
	// unused field # 179
	// unused field # 180
	// unused field # 181
	// unused field # 182
	// unused field # 183
	// unused field # 184
	// unused field # 185
	// unused field # 186
	// unused field # 187
	// unused field # 188
	// unused field # 189
	// unused field # 190
	// unused field # 191
	// unused field # 192
	// unused field # 193
	// unused field # 194
	// unused field # 195
	// unused field # 196
	// unused field # 197
	// unused field # 198
	// unused field # 199
	// unused field # 200
	// unused field # 201
	// unused field # 202
	// unused field # 203
	// unused field # 204
	// unused field # 205
	// unused field # 206
	// unused field # 207
	// unused field # 208
	// unused field # 209
	// unused field # 210
	// unused field # 211
	// unused field # 212
	// unused field # 213
	// unused field # 214
	// unused field # 215
	// unused field # 216
	// unused field # 217
	// unused field # 218
	// unused field # 219
	// unused field # 220
	// unused field # 221
	// unused field # 222
	// unused field # 223
	// unused field # 224
	// unused field # 225
	// unused field # 226
	// unused field # 227
	// unused field # 228
	// unused field # 229
	// unused field # 230
	// unused field # 231
	// unused field # 232
	// unused field # 233
	// unused field # 234
	// unused field # 235
	// unused field # 236
	// unused field # 237
	// unused field # 238
	// unused field # 239
	// unused field # 240
	// unused field # 241
	// unused field # 242
	// unused field # 243
	// unused field # 244
	// unused field # 245
	// unused field # 246
	// unused field # 247
	// unused field # 248
	// unused field # 249
	// unused field # 250
	// unused field # 251
	// unused field # 252
	// unused field # 253
	// unused field # 254
	// unused field # 255
	// unused field # 256
	// unused field # 257
	// unused field # 258
	// unused field # 259
	// unused field # 260
	// unused field # 261
	// unused field # 262
	// unused field # 263
	// unused field # 264
	// unused field # 265
	// unused field # 266
	// unused field # 267
	// unused field # 268
	// unused field # 269
	// unused field # 270
	// unused field # 271
	// unused field # 272
	// unused field # 273
	// unused field # 274
	// unused field # 275
	// unused field # 276
	// unused field # 277
	// unused field # 278
	// unused field # 279
	// unused field # 280
	// unused field # 281
	// unused field # 282
	// unused field # 283
	// unused field # 284
	// unused field # 285
	// unused field # 286
	// unused field # 287
	// unused field # 288
	// unused field # 289
	// unused field # 290
	// unused field # 291
	// unused field # 292
	// unused field # 293
	// unused field # 294
	// unused field # 295
	// unused field # 296
	// unused field # 297
	// unused field # 298
	// unused field # 299
	// unused field # 300
	// unused field # 301
	// unused field # 302
	// unused field # 303
	// unused field # 304
	// unused field # 305
	// unused field # 306
	// unused field # 307
	// unused field # 308
	// unused field # 309
	// unused field # 310
	// unused field # 311
	// unused field # 312
	// unused field # 313
	// unused field # 314
	// unused field # 315
	// unused field # 316
	// unused field # 317
	// unused field # 318
	// unused field # 319
	// unused field # 320
	// unused field # 321
	// unused field # 322
	// unused field # 323
	// unused field # 324
	// unused field # 325
	// unused field # 326
	// unused field # 327
	// unused field # 328
	// unused field # 329
	// unused field # 330
	// unused field # 331
	// unused field # 332
	// unused field # 333
	// unused field # 334
	// unused field # 335
	// unused field # 336
	// unused field # 337
	// unused field # 338
	// unused field # 339
	// unused field # 340
	// unused field # 341
	// unused field # 342
	// unused field # 343
	// unused field # 344
	// unused field # 345
	// unused field # 346
	// unused field # 347
	// unused field # 348
	// unused field # 349
	// unused field # 350
	// unused field # 351
	// unused field # 352
	// unused field # 353
	// unused field # 354
	// unused field # 355
	// unused field # 356
	// unused field # 357
	// unused field # 358
	// unused field # 359
	// unused field # 360
	// unused field # 361
	// unused field # 362
	// unused field # 363
	// unused field # 364
	// unused field # 365
	// unused field # 366
	// unused field # 367
	// unused field # 368
	// unused field # 369
	// unused field # 370
	// unused field # 371
	// unused field # 372
	// unused field # 373
	// unused field # 374
	// unused field # 375
	// unused field # 376
	// unused field # 377
	// unused field # 378
	// unused field # 379
	// unused field # 380
	// unused field # 381
	// unused field # 382
	// unused field # 383
	// unused field # 384
	// unused field # 385
	// unused field # 386
	// unused field # 387
	// unused field # 388
	// unused field # 389
	// unused field # 390
	// unused field # 391
	// unused field # 392
	// unused field # 393
	// unused field # 394
	// unused field # 395
	// unused field # 396
	// unused field # 397
	// unused field # 398
	// unused field # 399
	// unused field # 400
	// unused field # 401
	// unused field # 402
	// unused field # 403
	// unused field # 404
	// unused field # 405
	// unused field # 406
	// unused field # 407
	// unused field # 408
	// unused field # 409
	// unused field # 410
	// unused field # 411
	// unused field # 412
	// unused field # 413
	// unused field # 414
	// unused field # 415
	// unused field # 416
	// unused field # 417
	// unused field # 418
	// unused field # 419
	// unused field # 420
	// unused field # 421
	// unused field # 422
	// unused field # 423
	// unused field # 424
	// unused field # 425
	// unused field # 426
	// unused field # 427
	// unused field # 428
	// unused field # 429
	// unused field # 430
	// unused field # 431
	// unused field # 432
	// unused field # 433
	// unused field # 434
	// unused field # 435
	// unused field # 436
	// unused field # 437
	// unused field # 438
	// unused field # 439
	// unused field # 440
	// unused field # 441
	// unused field # 442
	// unused field # 443
	// unused field # 444
	// unused field # 445
	// unused field # 446
	// unused field # 447
	// unused field # 448
	// unused field # 449
	// unused field # 450
	// unused field # 451
	// unused field # 452
	// unused field # 453
	// unused field # 454
	// unused field # 455
	// unused field # 456
	// unused field # 457
	// unused field # 458
	// unused field # 459
	// unused field # 460
	// unused field # 461
	// unused field # 462
	// unused field # 463
	// unused field # 464
	// unused field # 465
	// unused field # 466
	// unused field # 467
	// unused field # 468
	// unused field # 469
	// unused field # 470
	// unused field # 471
	// unused field # 472
	// unused field # 473
	// unused field # 474
	// unused field # 475
	// unused field # 476
	// unused field # 477
	// unused field # 478
	// unused field # 479
	// unused field # 480
	// unused field # 481
	// unused field # 482
	// unused field # 483
	// unused field # 484
	// unused field # 485
	// unused field # 486
	// unused field # 487
	// unused field # 488
	// unused field # 489
	// unused field # 490
	// unused field # 491
	// unused field # 492
	// unused field # 493
	// unused field # 494
	// unused field # 495
	// unused field # 496
	// unused field # 497
	// unused field # 498
	// unused field # 499
	// unused field # 500
	// unused field # 501
	// unused field # 502
	// unused field # 503
	// unused field # 504
	// unused field # 505
	// unused field # 506
	// unused field # 507
	// unused field # 508
	// unused field # 509
	// unused field # 510
	// unused field # 511
	// unused field # 512
	// unused field # 513
	// unused field # 514
	// unused field # 515
	// unused field # 516
	// unused field # 517
	// unused field # 518
	// unused field # 519
	// unused field # 520
	// unused field # 521
	// unused field # 522
	// unused field # 523
	// unused field # 524
	// unused field # 525
	// unused field # 526
	// unused field # 527
	// unused field # 528
	// unused field # 529
	// unused field # 530
	// unused field # 531
	// unused field # 532
	// unused field # 533
	// unused field # 534
	// unused field # 535
	// unused field # 536
	// unused field # 537
	// unused field # 538
	// unused field # 539
	// unused field # 540
	// unused field # 541
	// unused field # 542
	// unused field # 543
	// unused field # 544
	// unused field # 545
	// unused field # 546
	// unused field # 547
	// unused field # 548
	// unused field # 549
	// unused field # 550
	// unused field # 551
	// unused field # 552
	// unused field # 553
	// unused field # 554
	// unused field # 555
	// unused field # 556
	// unused field # 557
	// unused field # 558
	// unused field # 559
	// unused field # 560
	// unused field # 561
	// unused field # 562
	// unused field # 563
	// unused field # 564
	// unused field # 565
	// unused field # 566
	// unused field # 567
	// unused field # 568
	// unused field # 569
	// unused field # 570
	// unused field # 571
	// unused field # 572
	// unused field # 573
	// unused field # 574
	// unused field # 575
	// unused field # 576
	// unused field # 577
	// unused field # 578
	// unused field # 579
	// unused field # 580
	// unused field # 581
	// unused field # 582
	// unused field # 583
	// unused field # 584
	// unused field # 585
	// unused field # 586
	// unused field # 587
	// unused field # 588
	// unused field # 589
	// unused field # 590
	// unused field # 591
	// unused field # 592
	// unused field # 593
	// unused field # 594
	// unused field # 595
	// unused field # 596
	// unused field # 597
	// unused field # 598
	// unused field # 599
	// unused field # 600
	// unused field # 601
	// unused field # 602
	// unused field # 603
	// unused field # 604
	// unused field # 605
	// unused field # 606
	// unused field # 607
	// unused field # 608
	// unused field # 609
	// unused field # 610
	// unused field # 611
	// unused field # 612
	// unused field # 613
	// unused field # 614
	// unused field # 615
	// unused field # 616
	// unused field # 617
	// unused field # 618
	// unused field # 619
	// unused field # 620
	// unused field # 621
	// unused field # 622
	// unused field # 623
	// unused field # 624
	// unused field # 625
	// unused field # 626
	// unused field # 627
	// unused field # 628
	// unused field # 629
	// unused field # 630
	// unused field # 631
	// unused field # 632
	// unused field # 633
	// unused field # 634
	// unused field # 635
	// unused field # 636
	// unused field # 637
	// unused field # 638
	// unused field # 639
	// unused field # 640
	// unused field # 641
	// unused field # 642
	// unused field # 643
	// unused field # 644
	// unused field # 645
	// unused field # 646
	// unused field # 647
	// unused field # 648
	// unused field # 649
	// unused field # 650
	// unused field # 651
	// unused field # 652
	// unused field # 653
	// unused field # 654
	// unused field # 655
	// unused field # 656
	// unused field # 657
	// unused field # 658
	// unused field # 659
	// unused field # 660
	// unused field # 661
	// unused field # 662
	// unused field # 663
	// unused field # 664
	// unused field # 665
	// unused field # 666
	// unused field # 667
	// unused field # 668
	// unused field # 669
	// unused field # 670
	// unused field # 671
	// unused field # 672
	// unused field # 673
	// unused field # 674
	// unused field # 675
	// unused field # 676
	// unused field # 677
	// unused field # 678
	// unused field # 679
	// unused field # 680
	// unused field # 681
	// unused field # 682
	// unused field # 683
	// unused field # 684
	// unused field # 685
	// unused field # 686
	// unused field # 687
	// unused field # 688
	// unused field # 689
	// unused field # 690
	// unused field # 691
	// unused field # 692
	// unused field # 693
	// unused field # 694
	// unused field # 695
	// unused field # 696
	// unused field # 697
	// unused field # 698
	// unused field # 699
	// unused field # 700
	// unused field # 701
	// unused field # 702
	// unused field # 703
	// unused field # 704
	// unused field # 705
	// unused field # 706
	// unused field # 707
	// unused field # 708
	// unused field # 709
	// unused field # 710
	// unused field # 711
	// unused field # 712
	// unused field # 713
	// unused field # 714
	// unused field # 715
	// unused field # 716
	// unused field # 717
	// unused field # 718
	// unused field # 719
	// unused field # 720
	// unused field # 721
	// unused field # 722
	// unused field # 723
	// unused field # 724
	// unused field # 725
	// unused field # 726
	// unused field # 727
	// unused field # 728
	// unused field # 729
	// unused field # 730
	// unused field # 731
	// unused field # 732
	// unused field # 733
	// unused field # 734
	// unused field # 735
	// unused field # 736
	// unused field # 737
	// unused field # 738
	// unused field # 739
	// unused field # 740
	// unused field # 741
	// unused field # 742
	// unused field # 743
	// unused field # 744
	// unused field # 745
	// unused field # 746
	// unused field # 747
	// unused field # 748
	// unused field # 749
	// unused field # 750
	// unused field # 751
	// unused field # 752
	// unused field # 753
	// unused field # 754
	// unused field # 755
	// unused field # 756
	// unused field # 757
	// unused field # 758
	// unused field # 759
	// unused field # 760
	// unused field # 761
	// unused field # 762
	// unused field # 763
	// unused field # 764
	// unused field # 765
	// unused field # 766
	// unused field # 767
	// unused field # 768
	// unused field # 769
	// unused field # 770
	// unused field # 771
	// unused field # 772
	// unused field # 773
	// unused field # 774
	// unused field # 775
	// unused field # 776
	// unused field # 777
	// unused field # 778
	// unused field # 779
	// unused field # 780
	// unused field # 781
	// unused field # 782
	// unused field # 783
	// unused field # 784
	// unused field # 785
	// unused field # 786
	// unused field # 787
	// unused field # 788
	// unused field # 789
	// unused field # 790
	// unused field # 791
	// unused field # 792
	// unused field # 793
	// unused field # 794
	// unused field # 795
	// unused field # 796
	// unused field # 797
	// unused field # 798
	// unused field # 799
	// unused field # 800
	// unused field # 801
	// unused field # 802
	// unused field # 803
	// unused field # 804
	// unused field # 805
	// unused field # 806
	// unused field # 807
	// unused field # 808
	// unused field # 809
	// unused field # 810
	// unused field # 811
	// unused field # 812
	// unused field # 813
	// unused field # 814
	// unused field # 815
	// unused field # 816
	// unused field # 817
	// unused field # 818
	// unused field # 819
	// unused field # 820
	// unused field # 821
	// unused field # 822
	// unused field # 823
	// unused field # 824
	// unused field # 825
	// unused field # 826
	// unused field # 827
	// unused field # 828
	// unused field # 829
	// unused field # 830
	// unused field # 831
	// unused field # 832
	// unused field # 833
	// unused field # 834
	// unused field # 835
	// unused field # 836
	// unused field # 837
	// unused field # 838
	// unused field # 839
	// unused field # 840
	// unused field # 841
	// unused field # 842
	// unused field # 843
	// unused field # 844
	// unused field # 845
	// unused field # 846
	// unused field # 847
	// unused field # 848
	// unused field # 849
	// unused field # 850
	// unused field # 851
	// unused field # 852
	// unused field # 853
	// unused field # 854
	// unused field # 855
	// unused field # 856
	// unused field # 857
	// unused field # 858
	// unused field # 859
	// unused field # 860
	// unused field # 861
	// unused field # 862
	// unused field # 863
	// unused field # 864
	// unused field # 865
	// unused field # 866
	// unused field # 867
	// unused field # 868
	// unused field # 869
	// unused field # 870
	// unused field # 871
	// unused field # 872
	// unused field # 873
	// unused field # 874
	// unused field # 875
	// unused field # 876
	// unused field # 877
	// unused field # 878
	// unused field # 879
	// unused field # 880
	// unused field # 881
	// unused field # 882
	// unused field # 883
	// unused field # 884
	// unused field # 885
	// unused field # 886
	// unused field # 887
	// unused field # 888
	// unused field # 889
	// unused field # 890
	// unused field # 891
	// unused field # 892
	// unused field # 893
	// unused field # 894
	// unused field # 895
	// unused field # 896
	// unused field # 897
	// unused field # 898
	// unused field # 899
	// unused field # 900
	// unused field # 901
	// unused field # 902
	// unused field # 903
	// unused field # 904
	// unused field # 905
	// unused field # 906
	// unused field # 907
	// unused field # 908
	// unused field # 909
	// unused field # 910
	// unused field # 911
	// unused field # 912
	// unused field # 913
	// unused field # 914
	// unused field # 915
	// unused field # 916
	// unused field # 917
	// unused field # 918
	// unused field # 919
	// unused field # 920
	// unused field # 921
	// unused field # 922
	// unused field # 923
	// unused field # 924
	// unused field # 925
	// unused field # 926
	// unused field # 927
	// unused field # 928
	// unused field # 929
	// unused field # 930
	// unused field # 931
	// unused field # 932
	// unused field # 933
	// unused field # 934
	// unused field # 935
	// unused field # 936
	// unused field # 937
	// unused field # 938
	// unused field # 939
	// unused field # 940
	// unused field # 941
	// unused field # 942
	// unused field # 943
	// unused field # 944
	// unused field # 945
	// unused field # 946
	// unused field # 947
	// unused field # 948
	// unused field # 949
	// unused field # 950
	// unused field # 951
	// unused field # 952
	// unused field # 953
	// unused field # 954
	// unused field # 955
	// unused field # 956
	// unused field # 957
	// unused field # 958
	// unused field # 959
	// unused field # 960
	// unused field # 961
	// unused field # 962
	// unused field # 963
	// unused field # 964
	// unused field # 965
	// unused field # 966
	// unused field # 967
	// unused field # 968
	// unused field # 969
	// unused field # 970
	// unused field # 971
	// unused field # 972
	// unused field # 973
	// unused field # 974
	// unused field # 975
	// unused field # 976
	// unused field # 977
	// unused field # 978
	// unused field # 979
	// unused field # 980
	// unused field # 981
	// unused field # 982
	// unused field # 983
	// unused field # 984
	// unused field # 985
	// unused field # 986
	// unused field # 987
	// unused field # 988
	// unused field # 989
	// unused field # 990
	// unused field # 991
	// unused field # 992
	// unused field # 993
	// unused field # 994
	// unused field # 995
	// unused field # 996
	// unused field # 997
	// unused field # 998
	// unused field # 999
	Result DetectResultCode `thrift:"result,1000" json:"result"`
}

func NewDetectResponse() *DetectResponse {
	return &DetectResponse{
		TestMode: math.MinInt32 - 1, // unset sentinal value

		DetectAdStatus: math.MinInt32 - 1, // unset sentinal value

		AdCode: math.MinInt32 - 1, // unset sentinal value

		MediaType: math.MinInt32 - 1, // unset sentinal value

		Result: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DetectResponse) IsSetTestMode() bool {
	return int64(p.TestMode) != math.MinInt32-1
}

func (p *DetectResponse) IsSetDetectAdStatus() bool {
	return int64(p.DetectAdStatus) != math.MinInt32-1
}

func (p *DetectResponse) IsSetAdCode() bool {
	return int64(p.AdCode) != math.MinInt32-1
}

func (p *DetectResponse) IsSetMediaType() bool {
	return int64(p.MediaType) != math.MinInt32-1
}

func (p *DetectResponse) IsSetResult() bool {
	return int64(p.Result) != math.MinInt32-1
}

func (p *DetectResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1000:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1000(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DetectResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.RefreshInterval = v
	}
	return nil
}

func (p *DetectResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Disabled = v
	}
	return nil
}

func (p *DetectResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PrisionTm = TimeInt(v)
	}
	return nil
}

func (p *DetectResponse) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ConfigTm = TimeInt(v)
	}
	return nil
}

func (p *DetectResponse) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ResTm = TimeInt(v)
	}
	return nil
}

func (p *DetectResponse) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.TestMode = MediaTestModeSetting(v)
	}
	return nil
}

func (p *DetectResponse) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ResName = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.ResName = append(p.ResName, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DetectResponse) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.CookieId = v
	}
	return nil
}

func (p *DetectResponse) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Debug = v
	}
	return nil
}

func (p *DetectResponse) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.DetectAdStatus = DetectAdStatus(v)
	}
	return nil
}

func (p *DetectResponse) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.AdCode = AdCode(v)
	}
	return nil
}

func (p *DetectResponse) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.IgnorePn = v
	}
	return nil
}

func (p *DetectResponse) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.MediaType = MediaType(v)
	}
	return nil
}

func (p *DetectResponse) readField50(iprot thrift.TProtocol) error {
	p.AppUpdate = NewAppUpdate()
	if err := p.AppUpdate.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppUpdate)
	}
	return nil
}

func (p *DetectResponse) readField51(iprot thrift.TProtocol) error {
	p.AppRate = NewAppRate()
	if err := p.AppRate.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppRate)
	}
	return nil
}

func (p *DetectResponse) readField52(iprot thrift.TProtocol) error {
	p.Placement = mediainfo_types.NewPlacement()
	if err := p.Placement.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Placement)
	}
	return nil
}

func (p *DetectResponse) readField1000(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1000: %s", err)
	} else {
		p.Result = DetectResultCode(v)
	}
	return nil
}

func (p *DetectResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DetectResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField1000(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DetectResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refresh_interval", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:refresh_interval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RefreshInterval)); err != nil {
		return fmt.Errorf("%T.refresh_interval (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:refresh_interval: %s", p, err)
	}
	return err
}

func (p *DetectResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("disabled", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:disabled: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Disabled)); err != nil {
		return fmt.Errorf("%T.disabled (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:disabled: %s", p, err)
	}
	return err
}

func (p *DetectResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("prision_tm", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:prision_tm: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PrisionTm)); err != nil {
		return fmt.Errorf("%T.prision_tm (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:prision_tm: %s", p, err)
	}
	return err
}

func (p *DetectResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("config_tm", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:config_tm: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ConfigTm)); err != nil {
		return fmt.Errorf("%T.config_tm (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:config_tm: %s", p, err)
	}
	return err
}

func (p *DetectResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("res_tm", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:res_tm: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ResTm)); err != nil {
		return fmt.Errorf("%T.res_tm (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:res_tm: %s", p, err)
	}
	return err
}

func (p *DetectResponse) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("testMode", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:testMode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TestMode)); err != nil {
		return fmt.Errorf("%T.testMode (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:testMode: %s", p, err)
	}
	return err
}

func (p *DetectResponse) writeField7(oprot thrift.TProtocol) (err error) {
	if p.ResName != nil {
		if err := oprot.WriteFieldBegin("res_name", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:res_name: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ResName)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ResName {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:res_name: %s", p, err)
		}
	}
	return err
}

func (p *DetectResponse) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cookieId", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:cookieId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CookieId)); err != nil {
		return fmt.Errorf("%T.cookieId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:cookieId: %s", p, err)
	}
	return err
}

func (p *DetectResponse) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("debug", thrift.BOOL, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:debug: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Debug)); err != nil {
		return fmt.Errorf("%T.debug (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:debug: %s", p, err)
	}
	return err
}

func (p *DetectResponse) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetDetectAdStatus() {
		if err := oprot.WriteFieldBegin("detectAdStatus", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:detectAdStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.DetectAdStatus)); err != nil {
			return fmt.Errorf("%T.detectAdStatus (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:detectAdStatus: %s", p, err)
		}
	}
	return err
}

func (p *DetectResponse) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdCode() {
		if err := oprot.WriteFieldBegin("adCode", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:adCode: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdCode)); err != nil {
			return fmt.Errorf("%T.adCode (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:adCode: %s", p, err)
		}
	}
	return err
}

func (p *DetectResponse) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ignorePn", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ignorePn: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IgnorePn)); err != nil {
		return fmt.Errorf("%T.ignorePn (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ignorePn: %s", p, err)
	}
	return err
}

func (p *DetectResponse) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaType", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:mediaType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaType)); err != nil {
		return fmt.Errorf("%T.mediaType (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:mediaType: %s", p, err)
	}
	return err
}

func (p *DetectResponse) writeField50(oprot thrift.TProtocol) (err error) {
	if p.AppUpdate != nil {
		if err := oprot.WriteFieldBegin("appUpdate", thrift.STRUCT, 50); err != nil {
			return fmt.Errorf("%T write field begin error 50:appUpdate: %s", p, err)
		}
		if err := p.AppUpdate.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppUpdate)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 50:appUpdate: %s", p, err)
		}
	}
	return err
}

func (p *DetectResponse) writeField51(oprot thrift.TProtocol) (err error) {
	if p.AppRate != nil {
		if err := oprot.WriteFieldBegin("appRate", thrift.STRUCT, 51); err != nil {
			return fmt.Errorf("%T write field begin error 51:appRate: %s", p, err)
		}
		if err := p.AppRate.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppRate)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 51:appRate: %s", p, err)
		}
	}
	return err
}

func (p *DetectResponse) writeField52(oprot thrift.TProtocol) (err error) {
	if p.Placement != nil {
		if err := oprot.WriteFieldBegin("placement", thrift.STRUCT, 52); err != nil {
			return fmt.Errorf("%T write field begin error 52:placement: %s", p, err)
		}
		if err := p.Placement.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Placement)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 52:placement: %s", p, err)
		}
	}
	return err
}

func (p *DetectResponse) writeField1000(oprot thrift.TProtocol) (err error) {
	if p.IsSetResult() {
		if err := oprot.WriteFieldBegin("result", thrift.I32, 1000); err != nil {
			return fmt.Errorf("%T write field begin error 1000:result: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Result)); err != nil {
			return fmt.Errorf("%T.result (1000) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1000:result: %s", p, err)
		}
	}
	return err
}

func (p *DetectResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DetectResponse(%+v)", *p)
}
