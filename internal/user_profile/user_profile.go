package up

import (
	"rtb_model_server/common/domob_thrift/rtb_types"
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/zaplog"
	"sync"

	"go.uber.org/zap"
)

type UserProfileProcesor struct {
	fillerManager *FieldFillerManager
}

var globalProcessor *UserProfileProcesor
var once sync.Once

func GetUserProfileProcessor() *UserProfileProcesor {
	once.Do(func() {
		globalProcessor = &UserProfileProcesor{}
		globalProcessor.initializeFillers()
	})
	return globalProcessor
}

// initializeFillers 初始化所有字段填充器
func (p *UserProfileProcesor) initializeFillers() {
	// 创建填充器管理器
	p.fillerManager = NewFieldFillerManager()

	// 创建填充器工厂
	factory := NewFieldFillerFactory()

	// 创建并注册所有填充器
	fillers := factory.CreateAllFillers()
	for _, filler := range fillers {
		p.fillerManager.RegisterFiller(filler)
	}

	zaplog.Logger.Info("Field fillers initialized",
		zap.Int("total", p.fillerManager.GetFillerCount()),
		zap.Int("enabled", p.fillerManager.GetEnabledFillerCount()))
}

func (p *UserProfileProcesor) Process(ctx *ctx.RequestContext) error {
	if p.fillerManager == nil {
		zaplog.Logger.Warn("Filler manager not initialized")
		return nil
	}

	// 使用填充器管理器填充所有字段
	return p.fillerManager.FillAllFields(ctx.BidRequest)
}

// GetFillerManager 获取字段填充器管理器
// 提供对填充器管理器的访问，用于高级操作和调试
func (p *UserProfileProcesor) GetFillerManager() *FieldFillerManager {
	return p.fillerManager
}

// FillSpecificField 使用指定的填充器填充字段
// 允许选择性地使用特定填充器，提供更精细的控制
func (p *UserProfileProcesor) FillSpecificField(req *rtb_types.RTBBidRequest, fillerName string) error {
	if p.fillerManager == nil {
		zaplog.Logger.Warn("Filler manager not initialized")
		return nil
	}

	filler := p.fillerManager.GetFillerByName(fillerName)
	if filler == nil {
		zaplog.Logger.Warn("Filler not found", zap.String("name", fillerName))
		return nil
	}

	if !filler.IsEnabled() {
		zaplog.Logger.Warn("Filler is disabled", zap.String("name", fillerName))
		return nil
	}

	return filler.Fill(req)
}
