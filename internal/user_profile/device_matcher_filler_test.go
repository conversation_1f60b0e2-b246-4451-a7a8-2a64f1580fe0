package up

import (
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/conf"
	"testing"
)

func TestDeviceMatcherFiller(t *testing.T) {
	// 初始化配置
	conf.GlobalConfig.ModelIndex.DeviceMappingPath = "/Users/<USER>/codes/rtb_model_server_go/conf/device_mapping.txt"
	
	// 创建填充器
	filler, err := NewDeviceMatcherFiller()
	if err != nil {
		t.Fatalf("Failed to create DeviceMatcherFiller: %v", err)
	}
	
	if !filler.IsEnabled() {
		t.Error("DeviceMatcherFiller should be enabled by default")
	}
	
	if filler.Name() != "DeviceMatcher" {
		t.<PERSON><PERSON>rf("Expected name 'DeviceMatcher', got '%s'", filler.Name())
	}
	
	// 检查设备数据是否加载
	deviceCount := filler.GetDeviceCount()
	if deviceCount == 0 {
		t.Error("No device data loaded")
	}
	t.Logf("Loaded %d device entries", deviceCount)
}

func TestDeviceMatcherFill(t *testing.T) {
	// 初始化配置
	conf.GlobalConfig.ModelIndex.DeviceMappingPath = "/Users/<USER>/codes/rtb_model_server_go/conf/device_mapping.txt"
	
	// 创建填充器
	filler, err := NewDeviceMatcherFiller()
	if err != nil {
		t.Fatalf("Failed to create DeviceMatcherFiller: %v", err)
	}
	
	tests := []struct {
		name      string
		userAgent string
		maker     string
		model     string
		expected  struct {
			deviceId int32
			osId     int32
		}
	}{
		{
			name:      "iPhone 15 Pro",
			userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
			maker:     "Apple",
			model:     "15Pro",
			expected: struct {
				deviceId int32
				osId     int32
			}{
				deviceId: 1000001,
				osId:     30000,
			},
		},
		{
			name:      "Samsung Galaxy S21 Ultra",
			userAgent: "Mozilla/5.0 (Linux; Android 11; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
			maker:     "Samsung",
			model:     "Galaxy S21 Ultra",
			expected: struct {
				deviceId int32
				osId     int32
			}{
				deviceId: 2000001,
				osId:     10000,
			},
		},
		{
			name:      "Xiaomi Mi 11",
			userAgent: "Mozilla/5.0 (Linux; Android 11; M2102J2SC) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
			maker:     "Xiaomi",
			model:     "Mi 11",
			expected: struct {
				deviceId int32
				osId     int32
			}{
				deviceId: 4000001,
				osId:     10000,
			},
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试请求
			req := &rtb_types.RTBBidRequest{
				Device: &rtb_types.RTBDeviceInfo{
					UserAgent: tt.userAgent,
					DevMake:   tt.maker,
					DevModel:  tt.model,
				},
			}
			
			// 执行填充
			err := filler.Fill(req)
			if err != nil {
				t.Errorf("Fill failed: %v", err)
				return
			}
			
			// 验证结果
			if req.Device.DmDeviceId != tt.expected.deviceId {
				t.Errorf("Expected device ID %d, got %d", tt.expected.deviceId, req.Device.DmDeviceId)
			}
			
			if req.Device.DmOsId != tt.expected.osId {
				t.Errorf("Expected OS ID %d, got %d", tt.expected.osId, req.Device.DmOsId)
			}
			
			t.Logf("Device ID: %d, OS ID: %d", req.Device.DmDeviceId, req.Device.DmOsId)
		})
	}
}

func TestDeviceMatcherUserAgentMatching(t *testing.T) {
	// 初始化配置
	conf.GlobalConfig.ModelIndex.DeviceMappingPath = "/Users/<USER>/codes/rtb_model_server_go/conf/device_mapping.txt"
	
	// 创建填充器
	filler, err := NewDeviceMatcherFiller()
	if err != nil {
		t.Fatalf("Failed to create DeviceMatcherFiller: %v", err)
	}
	
	// 测试仅通过UA匹配的情况
	tests := []struct {
		name      string
		userAgent string
		expectMatch bool
	}{
		{
			name:        "iPhone in UA",
			userAgent:   "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15",
			expectMatch: true, // UA中包含Apple品牌信息，会匹配到Apple设备
		},
		{
			name:        "Apple in UA",
			userAgent:   "Mozilla/5.0 (Apple iPhone; CPU iPhone OS 15_0 like Mac OS X)",
			expectMatch: true,
		},
		{
			name:        "Samsung in UA",
			userAgent:   "Mozilla/5.0 (Linux; Android 11; Samsung SM-G998B)",
			expectMatch: true,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &rtb_types.RTBBidRequest{
				Device: &rtb_types.RTBDeviceInfo{
					UserAgent: tt.userAgent,
				},
			}
			
			err := filler.Fill(req)
			if err != nil {
				t.Errorf("Fill failed: %v", err)
				return
			}
			
			matched := req.Device.DmDeviceId > 0
			if matched != tt.expectMatch {
				t.Errorf("Expected match: %v, got match: %v (Device ID: %d)", 
					tt.expectMatch, matched, req.Device.DmDeviceId)
			}
		})
	}
}

func TestDeviceMatcherGetDeviceInfo(t *testing.T) {
	// 初始化配置
	conf.GlobalConfig.ModelIndex.DeviceMappingPath = "/Users/<USER>/codes/rtb_model_server_go/conf/device_mapping.txt"
	
	// 创建填充器
	filler, err := NewDeviceMatcherFiller()
	if err != nil {
		t.Fatalf("Failed to create DeviceMatcherFiller: %v", err)
	}
	
	// 测试获取设备信息
	deviceInfo := filler.GetDeviceInfo("apple")
	if deviceInfo == nil {
		t.Error("Should find Apple device info")
	} else {
		if deviceInfo.Brand != "Apple" {
			t.Errorf("Expected brand 'Apple', got '%s'", deviceInfo.Brand)
		}
		if deviceInfo.BrandCode != 1000001 {
			t.Errorf("Expected brand code 1000001, got %d", deviceInfo.BrandCode)
		}
	}
	
	// 测试不存在的设备
	nonExistentDevice := filler.GetDeviceInfo("nonexistent")
	if nonExistentDevice != nil {
		t.Error("Should not find non-existent device")
	}
}