package up

import (
	"rtb_model_server/conf"
	"sync"
	"testing"
)

// TestDeviceMatcherSingleton 测试单例模式
func TestDeviceMatcherSingleton(t *testing.T) {
	// 初始化配置
	conf.GlobalConfig.ModelIndex.DeviceMappingPath = "/Users/<USER>/codes/rtb_model_server_go/conf/device_mapping.txt"
	
	// 并发创建多个实例
	var wg sync.WaitGroup
	instances := make([]*DeviceMatcherFiller, 10)
	errors := make([]error, 10)
	
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			instances[index], errors[index] = NewDeviceMatcherFiller()
		}(i)
	}
	
	wg.Wait()
	
	// 验证所有实例都是同一个对象
	for i := 1; i < 10; i++ {
		if errors[i] != nil {
			t.Fatalf("Failed to create DeviceMatcherFiller at index %d: %v", i, errors[i])
		}
		
		if instances[0] != instances[i] {
			t.Errorf("Instance %d is not the same as instance 0. Expected same pointer address.", i)
		}
	}
	
	// 验证实例功能正常
	if instances[0] == nil {
		t.Fatal("DeviceMatcherFiller instance is nil")
	}
	
	if !instances[0].IsEnabled() {
		t.Error("DeviceMatcherFiller should be enabled")
	}
	
	if instances[0].GetDeviceCount() == 0 {
		t.Error("DeviceMatcherFiller should have loaded device data")
	}
	
	t.Logf("Singleton test passed. All %d instances point to the same object.", len(instances))
	t.Logf("Device count: %d", instances[0].GetDeviceCount())
}

// TestDeviceMatcherSingletonPerformance 测试单例模式性能
func TestDeviceMatcherSingletonPerformance(t *testing.T) {
	// 初始化配置
	conf.GlobalConfig.ModelIndex.DeviceMappingPath = "/Users/<USER>/codes/rtb_model_server_go/conf/device_mapping.txt"
	
	// 第一次调用，会执行初始化
	first, err := NewDeviceMatcherFiller()
	if err != nil {
		t.Fatalf("Failed to create first instance: %v", err)
	}
	
	// 后续调用应该直接返回已初始化的实例
	for i := 0; i < 1000; i++ {
		instance, err := NewDeviceMatcherFiller()
		if err != nil {
			t.Fatalf("Failed to create instance at iteration %d: %v", i, err)
		}
		
		if instance != first {
			t.Errorf("Instance at iteration %d is not the same as first instance", i)
		}
	}
	
	t.Log("Performance test passed. All subsequent calls return the same instance quickly.")
}

// BenchmarkDeviceMatcherSingletonCreation 基准测试单例创建性能
func BenchmarkDeviceMatcherSingletonCreation(b *testing.B) {
	// 初始化配置
	conf.GlobalConfig.ModelIndex.DeviceMappingPath = "/Users/<USER>/codes/rtb_model_server_go/conf/device_mapping.txt"
	
	// 预热，确保单例已初始化
	_, err := NewDeviceMatcherFiller()
	if err != nil {
		b.Fatalf("Failed to create DeviceMatcherFiller: %v", err)
	}
	
	b.ResetTimer()
	
	// 基准测试后续调用的性能
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, err := NewDeviceMatcherFiller()
			if err != nil {
				b.Errorf("Failed to get singleton instance: %v", err)
			}
		}
	})
}