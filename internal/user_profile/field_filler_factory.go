package up

import (
	"fmt"
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/internal/zaplog"

	"go.uber.org/zap"
)

// FieldFiller 字段填充器接口
// 使用策略模式，允许不同的填充器独立实现各自的逻辑
type FieldFiller interface {
	// Fill 填充字段到RTB请求中
	Fill(req *rtb_types.RTBBidRequest) error
	// Name 返回填充器名称，用于日志和调试
	Name() string
	// IsEnabled 检查填充器是否启用
	IsEnabled() bool
}

// FillerType 填充器类型枚举
type FillerType string

const (
	// IP2RegionFillerType IP2Region填充器类型
	IP2RegionFillerType FillerType = "ip2region"
	// UserAgentFillerType User-Agent解析填充器类型
	UserAgentFillerType FillerType = "user_agent"
	// DeviceMatcherFillerType 设备匹配填充器类型
	DeviceMatcherFillerType FillerType = "device_matcher"
	// 可以在这里添加更多填充器类型
	// GeoLocationFillerType FillerType = "geo_location"
)

// FieldFillerFactory 字段填充器工厂
// 使用工厂模式创建和管理不同类型的填充器
type FieldFillerFactory struct {
	// 可以添加配置或依赖注入
}

// NewFieldFillerFactory 创建字段填充器工厂
func NewFieldFillerFactory() *FieldFillerFactory {
	return &FieldFillerFactory{}
}

// CreateFiller 根据类型创建填充器
func (f *FieldFillerFactory) CreateFiller(fillerType FillerType) (FieldFiller, error) {
	switch fillerType {
	case IP2RegionFillerType:
		return NewIP2RegionFiller()
	case UserAgentFillerType:
		return NewUserAgentFiller()
	case DeviceMatcherFillerType:
		return NewDeviceMatcherFiller()
	// 可以在这里添加更多填充器的创建逻辑
	// case GeoLocationFillerType:
	//     return NewGeoLocationFiller()
	default:
		return nil, fmt.Errorf("unknown filler type: %s", fillerType)
	}
}

// CreateAllFillers 创建所有可用的填充器
func (f *FieldFillerFactory) CreateAllFillers() []FieldFiller {
	fillers := make([]FieldFiller, 0)

	// 定义所有可用的填充器类型
	availableTypes := []FillerType{
		IP2RegionFillerType,
		UserAgentFillerType,
		DeviceMatcherFillerType,
		// 可以在这里添加更多类型
	}

	for _, fillerType := range availableTypes {
		filler, err := f.CreateFiller(fillerType)
		if err != nil {
			zaplog.Logger.Error("Failed to create filler",
				zap.String("type", string(fillerType)),
				zap.Error(err))
			continue
		}
		fillers = append(fillers, filler)
	}

	return fillers
}

// CreateEnabledFillers 创建所有启用的填充器
func (f *FieldFillerFactory) CreateEnabledFillers() []FieldFiller {
	allFillers := f.CreateAllFillers()
	enabledFillers := make([]FieldFiller, 0)

	for _, filler := range allFillers {
		if filler.IsEnabled() {
			enabledFillers = append(enabledFillers, filler)
		}
	}

	return enabledFillers
}

// GetAvailableFillerTypes 获取所有可用的填充器类型
func (f *FieldFillerFactory) GetAvailableFillerTypes() []FillerType {
	return []FillerType{
		IP2RegionFillerType,
		UserAgentFillerType,
		DeviceMatcherFillerType,
		// 可以在这里添加更多类型
	}
}
