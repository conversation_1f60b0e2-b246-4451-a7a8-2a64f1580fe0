package up

import (
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/conf"
	"sync"
	"testing"
)

// BenchmarkDeviceMatcherConcurrent 并发性能测试
func BenchmarkDeviceMatcherConcurrent(b *testing.B) {
	// 初始化配置
	conf.GlobalConfig.ModelIndex.DeviceMappingPath = "/Users/<USER>/codes/rtb_model_server_go/conf/device_mapping.txt"
	
	// 创建填充器
	filler, err := NewDeviceMatcherFiller()
	if err != nil {
		b.Fatalf("Failed to create DeviceMatcherFiller: %v", err)
	}
	
	// 准备测试数据
	testCases := []*rtb_types.RTBBidRequest{
		{
			Device: &rtb_types.RTBDeviceInfo{
				UserAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15",
				DevMake:   "Apple",
				DevModel:  "iPhone13,2",
			},
		},
		{
			Device: &rtb_types.RTBDeviceInfo{
				UserAgent: "Mozilla/5.0 (Linux; Android 11; SM-G998B) AppleWebKit/537.36",
				DevMake:   "Samsung",
				DevModel:  "Galaxy S21 Ultra",
			},
		},
		{
			Device: &rtb_types.RTBDeviceInfo{
				UserAgent: "Mozilla/5.0 (Linux; Android 10; ELS-AN00) AppleWebKit/537.36",
				DevMake:   "Huawei",
				DevModel:  "P40 Pro",
			},
		},
	}
	
	b.ResetTimer()
	
	// 并发测试
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			for _, req := range testCases {
				// 创建请求副本避免并发修改
				reqCopy := &rtb_types.RTBBidRequest{
					Device: &rtb_types.RTBDeviceInfo{
						UserAgent: req.Device.UserAgent,
						DevMake:   req.Device.DevMake,
						DevModel:  req.Device.DevModel,
					},
				}
				filler.Fill(reqCopy)
			}
		}
	})
}

// BenchmarkDeviceMatcherSequential 顺序性能测试
func BenchmarkDeviceMatcherSequential(b *testing.B) {
	// 初始化配置
	conf.GlobalConfig.ModelIndex.DeviceMappingPath = "/Users/<USER>/codes/rtb_model_server_go/conf/device_mapping.txt"
	
	// 创建填充器
	filler, err := NewDeviceMatcherFiller()
	if err != nil {
		b.Fatalf("Failed to create DeviceMatcherFiller: %v", err)
	}
	
	// 准备测试数据
	req := &rtb_types.RTBBidRequest{
		Device: &rtb_types.RTBDeviceInfo{
			UserAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15",
			DevMake:   "Apple",
			DevModel:  "iPhone13,2",
		},
	}
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		// 创建请求副本
		reqCopy := &rtb_types.RTBBidRequest{
			Device: &rtb_types.RTBDeviceInfo{
				UserAgent: req.Device.UserAgent,
				DevMake:   req.Device.DevMake,
				DevModel:  req.Device.DevModel,
			},
		}
		filler.Fill(reqCopy)
	}
}

// TestDeviceMatcherConcurrentSafety 并发安全性测试
func TestDeviceMatcherConcurrentSafety(t *testing.T) {
	// 初始化配置
	conf.GlobalConfig.ModelIndex.DeviceMappingPath = "/Users/<USER>/codes/rtb_model_server_go/conf/device_mapping.txt"
	
	// 创建填充器
	filler, err := NewDeviceMatcherFiller()
	if err != nil {
		t.Fatalf("Failed to create DeviceMatcherFiller: %v", err)
	}
	
	// 并发测试
	var wg sync.WaitGroup
	goroutineCount := 100
	operationsPerGoroutine := 1000
	
	for i := 0; i < goroutineCount; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			for j := 0; j < operationsPerGoroutine; j++ {
				req := &rtb_types.RTBBidRequest{
					Device: &rtb_types.RTBDeviceInfo{
						UserAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15",
						DevMake:   "Apple",
						DevModel:  "iPhone13,2",
					},
				}
				
				err := filler.Fill(req)
				if err != nil {
					t.Errorf("Goroutine %d: Fill failed: %v", id, err)
					return
				}
				
				// 验证结果
				if req.Device.DmDeviceId == 0 {
					t.Errorf("Goroutine %d: Device ID not filled", id)
					return
				}
				
				// 测试其他方法
				deviceInfo := filler.GetDeviceInfo("apple")
				if deviceInfo == nil {
					t.Errorf("Goroutine %d: GetDeviceInfo failed", id)
					return
				}
				
				count := filler.GetDeviceCount()
				if count == 0 {
					t.Errorf("Goroutine %d: GetDeviceCount returned 0", id)
					return
				}
			}
		}(i)
	}
	
	wg.Wait()
	t.Logf("Concurrent safety test completed: %d goroutines × %d operations", goroutineCount, operationsPerGoroutine)
}