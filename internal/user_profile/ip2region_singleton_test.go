package up

import (
	"rtb_model_server/conf"
	"rtb_model_server/internal/zaplog"
	"sync"
	"testing"

	"go.uber.org/zap"
)

// TestIP2RegionFillerSingleton 测试IP2Region填充器的单例模式
func TestIP2RegionFillerSingleton(t *testing.T) {
	// 初始化测试用的logger
	if zaplog.Logger == nil {
		zaplog.Logger = zap.NewNop()
	}
	
	// 设置测试配置（空路径，这样会禁用filler但不会报错）
	conf.GlobalConfig.ModelIndex.IP2RegionDbPath = ""
	
	// 重置单例状态以确保测试的独立性
	ip2regionInstance = nil
	ip2regionOnce = sync.Once{}
	
	// 第一次创建实例
	filler1, err1 := NewIP2RegionFiller()
	if err1 != nil {
		t.Errorf("First instance creation failed: %v", err1)
		return
	}

	// 第二次创建实例
	filler2, err2 := NewIP2RegionFiller()
	if err2 != nil {
		t.Errorf("Second instance creation failed: %v", err2)
		return
	}

	// 验证是否为同一个实例
	if filler1 != filler2 {
		t.Erro<PERSON>("Expected same instance, got different instances")
	}
	t.Logf("Singleton test passed: both calls returned the same instance")

	// 测试获取实例的方法
	instance := GetIP2RegionInstance()
	if instance != filler1 {
		t.Errorf("GetIP2RegionInstance() returned different instance")
	}

	// 测试初始化状态检查（配置为空时应该是disabled状态）
	isInitialized := IsIP2RegionInitialized()
	if isInitialized {
		t.Errorf("IsIP2RegionInitialized() should return false when db path is empty")
	}

	// 验证filler是存在的但是disabled
	if filler1 == nil {
		t.Errorf("Filler should not be nil even when disabled")
	}
	if filler1.IsEnabled() {
		t.Errorf("Filler should be disabled when db path is empty")
	}

	t.Logf("IP2Region singleton test completed successfully")
}

// TestIP2RegionFillerConcurrency 测试并发创建的线程安全性
func TestIP2RegionFillerConcurrency(t *testing.T) {
	// 初始化测试用的logger
	if zaplog.Logger == nil {
		zaplog.Logger = zap.NewNop()
	}
	
	// 设置测试配置
	conf.GlobalConfig.ModelIndex.IP2RegionDbPath = ""
	
	// 重置单例状态
	ip2regionInstance = nil
	ip2regionOnce = sync.Once{}
	
	const numGoroutines = 10
	results := make([]*IP2RegionFiller, numGoroutines)
	errors := make([]error, numGoroutines)
	done := make(chan int, numGoroutines)

	// 并发创建多个实例
	for i := 0; i < numGoroutines; i++ {
		go func(index int) {
			results[index], errors[index] = NewIP2RegionFiller()
			done <- index
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// 验证所有实例都是成功创建的
	for i := 0; i < numGoroutines; i++ {
		if errors[i] != nil {
			t.Errorf("Goroutine %d failed to create instance: %v", i, errors[i])
			return
		}
	}

	// 验证所有实例都是同一个
	firstInstance := results[0]
	for i := 1; i < numGoroutines; i++ {
		if results[i] != firstInstance {
			t.Errorf("Goroutine %d returned different instance", i)
		}
	}

	// 验证所有实例都是disabled状态
	for i := 0; i < numGoroutines; i++ {
		if results[i].IsEnabled() {
			t.Errorf("Goroutine %d returned enabled instance, should be disabled", i)
		}
	}

	t.Logf("IP2Region concurrency test completed successfully")
}