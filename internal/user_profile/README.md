# User Profile 字段填充器架构

## 概述

本模块采用策略模式和工厂模式的组合，实现了一个可扩展的字段填充器架构，用于为RTB请求填充各种用户画像相关的字段。

## 架构设计

### 核心接口

- **FieldFiller**: 字段填充器接口，定义了所有填充器必须实现的方法
- **FieldFillerManager**: 填充器管理器，负责管理和协调多个填充器
- **FieldFillerFactory**: 填充器工厂，负责创建不同类型的填充器

### 设计模式

1. **策略模式**: 每个填充器都是一个独立的策略，可以独立开发和测试
2. **工厂模式**: 通过工厂统一创建和管理填充器实例
3. **组合模式**: 管理器可以组合多个填充器，统一对外提供服务

## 现有填充器

### IP2RegionFiller
- **功能**: 根据IP地址填充城市编码信息
- **依赖**: ip2region数据库文件
- **字段**: `req.Device.GeoCity`
- **特性**: 单例模式，确保只有一个实例常驻内存，避免重复加载数据库

### UserAgentFiller
- **功能**: 解析User-Agent字符串，提取操作系统、浏览器等信息
- **依赖**: 无外部依赖
- **字段**: `req.Device.DevOs`

### DeviceMatcherFiller
- **功能**: 基于UA和机型匹配机制，填充设备和操作系统编码
- **依赖**: 通过 `conf.GlobalConfig.ModelIndex.DeviceMappingPath` 配置的设备映射文件
- **字段**: `req.Device.DmDeviceId`, `req.Device.DmOsId`, `req.Device.DevMake`, `req.Device.DevModel`
- **特性**: 支持品牌、机型、UA的多维度匹配，采用AC自动机类似的匹配算法

## 使用方式

### 基本用法

```go
// 获取用户画像处理器
processor := GetUserProfileProcessor()

// 填充所有字段
err := processor.FillCityCodeInfo(req)

// 使用特定填充器
err := processor.FillSpecificField(req, "UserAgent")

// 获取填充器管理器
manager := processor.GetFillerManager()
```

### 添加新的填充器

1. **实现FieldFiller接口**:
```go
type MyCustomFiller struct {
    enabled bool
}

func (f *MyCustomFiller) Name() string {
    return "MyCustom"
}

func (f *MyCustomFiller) IsEnabled() bool {
    return f.enabled
}

func (f *MyCustomFiller) Fill(req *rtb_types.RTBBidRequest) error {
    // 实现填充逻辑
    return nil
}
```

2. **在工厂中注册**:
```go
// 在field_filler_factory.go中添加新的填充器类型
const (
    IP2RegionFillerType FillerType = iota
    UserAgentFillerType
    MyCustomFillerType  // 新增
)

// 在CreateFiller方法中添加创建逻辑
case MyCustomFillerType:
    return NewMyCustomFiller()
```

3. **更新相关方法**:
- `CreateAllFillers()`
- `GetAvailableFillerTypes()`

## 单例模式优化

### IP2RegionFiller 单例模式

IP2RegionFiller 采用单例模式实现，具有以下优势：

1. **内存效率**: 只加载一次IP2Region数据库到内存，避免重复占用
2. **性能优化**: 避免每次请求都重新加载数据库文件
3. **线程安全**: 使用sync.Once确保并发安全的初始化
4. **资源管理**: 减少文件IO操作，提高系统性能

### 使用示例

```go
// 多次调用返回同一个实例
filler1, _ := NewIP2RegionFiller()
filler2, _ := NewIP2RegionFiller()
// filler1 == filler2 (同一个实例)

// 获取单例实例
instance := GetIP2RegionInstance()

// 检查初始化状态
if IsIP2RegionInitialized() {
    // 实例已成功初始化且启用
}
```

### 测试验证

```bash
# 运行单例模式测试
go test -v -run TestIP2RegionFillerSingleton
go test -v -run TestIP2RegionFillerConcurrency
```

## 配置

填充器的配置通过`conf.GlobalConfig.ModelIndex`进行管理：

```go
type ModelIndexConfig struct {
    IP2RegionDbPath   string `yaml:"ip2region_db_path"`
    DeviceMappingPath string `yaml:"device_mapping_path"`
    // 可以添加其他填充器的配置
}
```

## 测试

每个填充器都有对应的单元测试：

```bash
# 运行所有测试
go test -v

# 运行特定测试
go test -v -run TestUserAgentFiller
```

## 扩展性

该架构具有良好的扩展性：

1. **添加新字段**: 只需实现新的填充器
2. **修改现有逻辑**: 只需修改对应的填充器
3. **配置管理**: 通过配置文件控制填充器的启用/禁用
4. **错误处理**: 单个填充器的失败不会影响其他填充器
5. **性能优化**: 可以并行执行多个填充器（未来扩展）

## 文件结构

```
user_profile/
├── user_profile.go              # 主要的用户画像处理器
├── field_filler_manager.go      # 填充器管理器
├── field_filler_factory.go      # 填充器工厂
├── ip2region_filler.go          # IP2Region填充器（单例模式）
├── user_agent_filler.go         # UserAgent填充器
├── user_profile_test.go         # 测试文件
├── ip2region_singleton_test.go  # IP2Region单例模式测试
├── examples/
│   └── singleton_demo.go        # 单例模式使用示例
└── README.md                    # 本文档
```