package up

import (
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/internal/zaplog"
	"sync"

	"go.uber.org/zap"
)

// FieldFillerManager 字段填充器管理器
// 使用组合模式管理多个填充器，提供统一的填充接口
type FieldFillerManager struct {
	fillers []FieldFiller
	mu      sync.RWMutex
}

// NewFieldFillerManager 创建字段填充器管理器
func NewFieldFillerManager() *FieldFillerManager {
	return &FieldFillerManager{
		fillers: make([]FieldFiller, 0),
	}
}

// RegisterFiller 注册字段填充器
func (m *FieldFillerManager) RegisterFiller(filler FieldFiller) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.fillers = append(m.fillers, filler)
	// Debug: Field filler registered
}

// FillAllFields 填充所有字段
// 按注册顺序依次执行所有启用的填充器
func (m *FieldFillerManager) FillAllFields(req *rtb_types.RTBBidRequest) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	for _, filler := range m.fillers {
		if !filler.IsEnabled() {
			continue
		}

		if err := filler.Fill(req); err != nil {
			zaplog.Logger.Warn("fill field failed", zap.String("filler", filler.Name()), zap.Error(err))
			// 记录错误但不中断其他填充器的执行
			// Debug: Field filler failed
			// 可以根据业务需求决定是否继续执行其他填充器
			// 这里选择继续执行，保证系统的健壮性
		}
	}

	return nil
}

// GetFillerByName 根据名称获取填充器
func (m *FieldFillerManager) GetFillerByName(name string) FieldFiller {
	m.mu.RLock()
	defer m.mu.RUnlock()

	for _, filler := range m.fillers {
		if filler.Name() == name {
			return filler
		}
	}
	return nil
}

// GetEnabledFillers 获取所有启用的填充器
func (m *FieldFillerManager) GetEnabledFillers() []FieldFiller {
	m.mu.RLock()
	defer m.mu.RUnlock()

	enabledFillers := make([]FieldFiller, 0)
	for _, filler := range m.fillers {
		if filler.IsEnabled() {
			enabledFillers = append(enabledFillers, filler)
		}
	}
	return enabledFillers
}

// GetFillerCount 获取填充器总数
func (m *FieldFillerManager) GetFillerCount() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return len(m.fillers)
}

// GetEnabledFillerCount 获取启用的填充器数量
func (m *FieldFillerManager) GetEnabledFillerCount() int {
	m.mu.RLock()
	defer m.mu.RUnlock()

	count := 0
	for _, filler := range m.fillers {
		if filler.IsEnabled() {
			count++
		}
	}
	return count
}
