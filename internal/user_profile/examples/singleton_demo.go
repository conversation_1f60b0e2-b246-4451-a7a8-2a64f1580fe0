package main

import (
	"fmt"
	"rtb_model_server/conf"
	up "rtb_model_server/internal/user_profile"
	"rtb_model_server/internal/zaplog"
	"sync"
	"time"

	"go.uber.org/zap"
)

// 这个示例演示了IP2RegionFiller的单例模式使用
func main() {
	// 初始化logger
	zaplog.Logger = zap.NewNop()

	// 设置配置（在实际使用中，这应该通过配置文件设置）
	conf.GlobalConfig.ModelIndex.IP2RegionDbPath = "" // 空路径用于演示

	fmt.Println("=== IP2RegionFiller 单例模式演示 ===")

	// 演示1: 基本单例使用
	fmt.Println("\n1. 基本单例使用:")
	demonstrateBasicSingleton()

	// 演示2: 并发安全性
	fmt.Println("\n2. 并发安全性演示:")
	demonstrateConcurrencySafety()

	// 演示3: 内存效率
	fmt.Println("\n3. 内存效率对比:")
	demonstrateMemoryEfficiency()
}

// 演示基本的单例使用
func demonstrateBasicSingleton() {
	// 多次调用NewIP2RegionFiller()，应该返回同一个实例
	filler1, err1 := up.NewIP2RegionFiller()
	if err1 != nil {
		fmt.Printf("创建第一个实例失败: %v\n", err1)
		return
	}

	filler2, err2 := up.NewIP2RegionFiller()
	if err2 != nil {
		fmt.Printf("创建第二个实例失败: %v\n", err2)
		return
	}

	filler3, err3 := up.NewIP2RegionFiller()
	if err3 != nil {
		fmt.Printf("创建第三个实例失败: %v\n", err3)
		return
	}

	// 检查是否为同一个实例
	if filler1 == filler2 && filler2 == filler3 {
		fmt.Printf("✓ 成功: 三次调用返回了同一个实例\n")
		fmt.Printf("  实例地址: %p\n", filler1)
		fmt.Printf("  实例名称: %s\n", filler1.Name())
		fmt.Printf("  是否启用: %t\n", filler1.IsEnabled())
	} else {
		fmt.Printf("✗ 失败: 返回了不同的实例\n")
	}

	// 使用GetIP2RegionInstance()获取实例
	instance := up.GetIP2RegionInstance()
	if instance == filler1 {
		fmt.Printf("✓ GetIP2RegionInstance() 返回了相同的实例\n")
	} else {
		fmt.Printf("✗ GetIP2RegionInstance() 返回了不同的实例\n")
	}

	// 检查初始化状态
	isInitialized := up.IsIP2RegionInitialized()
	fmt.Printf("  初始化状态: %t\n", isInitialized)
}

// 演示并发安全性
func demonstrateConcurrencySafety() {
	const numGoroutines = 100
	var wg sync.WaitGroup
	results := make([]*up.IP2RegionFiller, numGoroutines)

	start := time.Now()

	// 并发创建多个实例
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			filler, err := up.NewIP2RegionFiller()
			if err != nil {
				fmt.Printf("Goroutine %d 创建实例失败: %v\n", index, err)
				return
			}
			results[index] = filler
		}(i)
	}

	wg.Wait()
	duration := time.Since(start)

	// 验证所有实例都是同一个
	firstInstance := results[0]
	allSame := true
	for i := 1; i < numGoroutines; i++ {
		if results[i] != firstInstance {
			allSame = false
			break
		}
	}

	if allSame {
		fmt.Printf("✓ 成功: %d个并发goroutine都返回了同一个实例\n", numGoroutines)
		fmt.Printf("  耗时: %v\n", duration)
		fmt.Printf("  实例地址: %p\n", firstInstance)
	} else {
		fmt.Printf("✗ 失败: 并发创建返回了不同的实例\n")
	}
}

// 演示内存效率对比
func demonstrateMemoryEfficiency() {
	fmt.Printf("单例模式的优势:\n")
	fmt.Printf("  ✓ 只加载一次IP2Region数据库到内存\n")
	fmt.Printf("  ✓ 避免重复的文件IO操作\n")
	fmt.Printf("  ✓ 减少内存占用（特别是数据库文件较大时）\n")
	fmt.Printf("  ✓ 提高初始化性能\n")
	fmt.Printf("  ✓ 线程安全的并发访问\n")

	// 获取当前实例信息
	instance := up.GetIP2RegionInstance()
	if instance != nil {
		fmt.Printf("\n当前单例实例信息:\n")
		fmt.Printf("  名称: %s\n", instance.Name())
		fmt.Printf("  地址: %p\n", instance)
		fmt.Printf("  启用状态: %t\n", instance.IsEnabled())
		fmt.Printf("  初始化状态: %t\n", up.IsIP2RegionInitialized())
	} else {
		fmt.Printf("\n当前没有初始化的实例\n")
	}
}
