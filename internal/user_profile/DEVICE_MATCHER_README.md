# 设备匹配填充器 (DeviceMatcherFiller)

## 概述

设备匹配填充器是一个基于用户画像项目机型编码匹配机制的填充器，用于在RTB请求中填充设备和操作系统编码。它通过分析User-Agent、厂商信息和机型信息，实现精确的设备识别和编码填充。

## 核心功能

### 1. 设备信息匹配
- **品牌匹配**: 根据设备厂商信息进行匹配
- **机型匹配**: 支持内部机型号和发布机型号的匹配
- **UA解析**: 通过User-Agent字符串进行模糊匹配
- **组合匹配**: 支持品牌+机型的组合匹配策略

### 2. 编码填充
- **设备编码**: 填充 `DmDeviceId` 字段
- **操作系统编码**: 填充 `DmOsId` 字段
- **厂商信息**: 补充缺失的 `DevMake` 字段
- **机型信息**: 补充缺失的 `DevModel` 字段

## 匹配算法

### 匹配优先级策略

1. **精确匹配优先**: 厂商+机型组合匹配
2. **机型匹配**: 单独机型信息匹配
3. **厂商匹配**: 单独厂商信息匹配
4. **UA模糊匹配**: 基于User-Agent的字符串匹配

### AC自动机类似的匹配机制

```go
// 匹配策略：优先选择匹配长度最长的结果
for key, deviceInfo := range f.deviceMap {
    if strings.Contains(userAgent, key) {
        if len(key) > maxMatchLen {
            bestMatch = deviceInfo
            maxMatchLen = len(key)
        }
    }
}
```

## 编码规则

### 操作系统编码范围
- **Android**: `10000-19999`
- **iOS**: `30000-39999` 
- **Windows Mobile**: `90000-99999`
- **其他**: `0` (未知)

### 设备编码
- 采用7位数字编码格式
- 通过配置文件定义品牌和机型的对应关系

## 配置文件格式

配置文件路径通过 `conf/rtb_model_server.yaml` 中的 `ModelIndex.DeviceMappingPath` 配置项指定：

```yaml
ModelIndex:
  DeviceMappingPath: conf/device_mapping.txt
```

```
# 格式：品牌名\t品牌编码\t内部机型号\t发布机型号\t机型编码
Apple	1000001	202132XC	15Pro	1000001
Samsung	2000001	SM-G998B	Galaxy S21 Ultra	2000001
Huawei	3000001	ELS-AN00	P40 Pro	3000001
```

## 使用方式

### 1. 基本使用

```go
// 创建填充器
filler, err := NewDeviceMatcherFiller()
if err != nil {
    log.Fatal(err)
}

// 填充RTB请求
err = filler.Fill(rtbRequest)
if err != nil {
    log.Error("填充失败", err)
}
```

### 2. 配置文件设置

在 `conf/rtb_model_server.yaml` 中配置设备映射文件路径：

```yaml
ModelIndex:
  DeviceMappingPath: conf/device_mapping.txt  # 设备映射文件路径
```

### 3. 自定义配置文件（测试用）

```go
// 仅用于测试，使用自定义配置文件
filler, err := NewDeviceMatcherFillerWithConfig("/path/to/custom/config.txt")
```

### 3. 集成到用户画像处理器

填充器会自动通过 `FieldFillerFactory` 创建并注册到 `FieldFillerManager` 中：

```go
// 获取用户画像处理器
processor := GetUserProfileProcessor()

// 填充所有字段（包括设备匹配）
err := processor.FillAllFields(rtbRequest)
```

## 测试用例

### 1. 基本功能测试

```bash
go test ./internal/user_profile -v -run TestDeviceMatcherFiller
```

### 2. 匹配功能测试

```bash
go test ./internal/user_profile -v -run TestDeviceMatcherFill
```

### 3. UA匹配测试

```bash
go test ./internal/user_profile -v -run TestDeviceMatcherUserAgentMatching
```

## 性能特性

### 1. 线程安全
- 使用 `sync.RWMutex` 保证并发安全
- 支持多goroutine同时访问

### 2. 内存优化
- 配置数据一次加载，内存常驻
- 使用map结构提供O(1)查找性能

### 3. 多索引支持
- 品牌名索引
- 内部机型号索引  
- 发布机型号索引
- 组合索引（品牌+机型）

## 扩展性

### 1. 添加新设备
只需在配置文件中添加新的设备条目：

```
新品牌	编码	内部型号	发布型号	机型编码
```

### 2. 自定义匹配逻辑
可以继承 `DeviceMatcherFiller` 并重写 `matchDevice` 方法来实现自定义匹配逻辑。

### 3. 配置热更新
未来可以扩展支持配置文件的热更新功能。

## 错误处理

- **配置文件不存在**: 返回创建错误
- **配置格式错误**: 跳过错误行，继续处理
- **设备信息为空**: 跳过处理，不报错
- **匹配失败**: 不填充编码，不报错

## 日志和调试

### 调试方法

```go
// 获取设备信息
deviceInfo := filler.GetDeviceInfo("apple")
if deviceInfo != nil {
    fmt.Printf("Brand: %s, Code: %d\n", deviceInfo.Brand, deviceInfo.BrandCode)
}

// 获取加载的设备数量
count := filler.GetDeviceCount()
fmt.Printf("Loaded %d devices\n", count)
```

## 兼容性检查

填充器实现了与用户画像项目相同的兼容性检查机制：

```go
// 操作系统与设备的兼容性检查
func isOSDeviceCompatible(osId, deviceId int32) bool {
    return (osId/OS_ID_SCALE == deviceId/OS_ID_SCALE || 
            osId/DEVICE_ID_SCALE == deviceId/DEVICE_ID_SCALE)
}
```

## 依赖关系

- `rtb_types.RTBBidRequest`: RTB请求结构体
- `rtb_types.RTBDeviceInfo`: 设备信息结构体
- 标准库: `bufio`, `os`, `strings`, `strconv`, `sync`

## 注意事项

1. **配置文件路径**: 确保配置文件路径正确且可访问
2. **编码格式**: 配置文件使用Tab分隔，注意格式正确性
3. **大小写敏感**: 匹配时会转换为小写进行比较
4. **性能考虑**: 大量设备数据时注意内存使用
5. **线程安全**: 多goroutine环境下安全使用