package up

import (
	"rtb_model_server/common/domob_thrift/rtb_types"
	"testing"
)

func TestFieldFillerFactory(t *testing.T) {
	// 测试工厂创建填充器
	factory := NewFieldFillerFactory()
	if factory == nil {
		t.Fatal("Factory should not be nil")
	}

	// 测试获取可用填充器类型
	availableTypes := factory.GetAvailableFillerTypes()
	if len(availableTypes) == 0 {
		t.<PERSON>rror("Should have at least one available filler type")
	}

	t.Logf("Available filler types: %v", availableTypes)

	// 测试创建UserAgent填充器（不依赖外部配置）
	userAgentFiller, err := factory.CreateFiller(UserAgentFillerType)
	if err != nil {
		t.Errorf("Failed to create UserAgent filler: %v", err)
	}

	if userAgentFiller == nil {
		t.Error("UserAgent filler should not be nil")
	}

	if userAgentFiller.Name() != "UserAgent" {
		t.<PERSON><PERSON><PERSON>("Expected filler name 'UserAgent', got '%s'", userAgentFiller.Name())
	}

	if !userAgentFiller.IsEnabled() {
		t.Error("UserAgent filler should be enabled")
	}
}

func TestUserAgentFiller(t *testing.T) {
	// 创建UserAgent填充器
	filler, err := NewUserAgentFiller()
	if err != nil {
		t.Fatalf("Failed to create UserAgent filler: %v", err)
	}

	// 测试用例
	testCases := []struct {
		name      string
		userAgent string
		expectedOS string
	}{
		{
			name:      "Chrome on Windows",
			userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			expectedOS: "Windows",
		},
		{
			name:      "Safari on macOS",
			userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
			expectedOS: "macOS",
		},
		{
			name:      "Chrome on Android",
			userAgent: "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
			expectedOS: "Android",
		},
		{
			name:      "Safari on iPhone",
			userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
			expectedOS: "iOS",
		},
		{
			name:      "Empty UserAgent",
			userAgent: "",
			expectedOS: "", // 空UserAgent不应该设置OS
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建测试请求
			req := &rtb_types.RTBBidRequest{
				Device: &rtb_types.RTBDeviceInfo{
					UserAgent: tc.userAgent,
				},
			}

			// 使用填充器填充数据
			err := filler.Fill(req)
			if err != nil {
				t.Errorf("Fill failed: %v", err)
			}

			// 验证结果
			if tc.expectedOS == "" {
				// 空UserAgent的情况，DevOs应该保持空
				if req.Device.DevOs != "" {
					t.Errorf("Expected empty DevOs for empty UserAgent, got '%s'", req.Device.DevOs)
				}
			} else {
				// 有效UserAgent的情况，应该设置DevOs
				if req.Device.DevOs != tc.expectedOS {
					t.Errorf("Expected DevOs '%s', got '%s'", tc.expectedOS, req.Device.DevOs)
				}
			}

			t.Logf("UserAgent: %s, DevOs: %s", tc.userAgent, req.Device.DevOs)
		})
	}
}

func TestFieldFillerManager(t *testing.T) {
	// 创建填充器管理器
	manager := NewFieldFillerManager()
	if manager == nil {
		t.Fatal("Manager should not be nil")
	}

	// 创建并注册UserAgent填充器
	userAgentFiller, err := NewUserAgentFiller()
	if err != nil {
		t.Fatalf("Failed to create UserAgent filler: %v", err)
	}

	manager.RegisterFiller(userAgentFiller)

	// 验证填充器数量
	if manager.GetFillerCount() != 1 {
		t.Errorf("Expected 1 filler, got %d", manager.GetFillerCount())
	}

	if manager.GetEnabledFillerCount() != 1 {
		t.Errorf("Expected 1 enabled filler, got %d", manager.GetEnabledFillerCount())
	}

	// 测试根据名称获取填充器
	filler := manager.GetFillerByName("UserAgent")
	if filler == nil {
		t.Error("Should find UserAgent filler")
	}

	// 测试不存在的填充器
	nonExistentFiller := manager.GetFillerByName("NonExistent")
	if nonExistentFiller != nil {
		t.Error("Should not find non-existent filler")
	}

	// 测试填充所有字段
	req := &rtb_types.RTBBidRequest{
		Device: &rtb_types.RTBDeviceInfo{
			UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		},
	}

	err = manager.FillAllFields(req)
	if err != nil {
		t.Errorf("FillAllFields failed: %v", err)
	}

	// 验证字段被正确填充
	if req.Device.DevOs != "Windows" {
		t.Errorf("Expected DevOs 'Windows', got '%s'", req.Device.DevOs)
	}
}
