package up

import (
	"rtb_model_server/common/domob_thrift/rtb_types"
	"strings"
)

// UserAgentFiller User-Agent解析填充器
// 这是一个示例填充器，展示如何扩展字段填充系统
type UserAgentFiller struct {
	enabled bool
}

// NewUserAgentFiller 创建User-Agent填充器
func NewUserAgentFiller() (*UserAgentFiller, error) {
	filler := &UserAgentFiller{
		enabled: true, // 默认启用
	}
	return filler, nil
}

// Name 返回填充器名称
func (f *UserAgentFiller) Name() string {
	return "UserAgent"
}

// IsEnabled 检查填充器是否启用
func (f *UserAgentFiller) IsEnabled() bool {
	return f.enabled
}

// Fill 填充设备信息
func (f *UserAgentFiller) Fill(req *rtb_types.RTBBidRequest) error {
	if !f.enabled {
		return nil
	}

	if req.Device == nil {
		return nil // 设备信息为空时不处理
	}

	userAgent := req.Device.UserAgent
	if userAgent == "" {
		return nil // User-Agent为空时不处理
	}

	// 解析操作系统
	osType := f.parseOSType(userAgent)
	if osType != "" {
		req.Device.DevOs = osType
	}

	// 解析浏览器类型
	browserType := f.parseBrowserType(userAgent)
	if browserType != "" {
		// 这里可以设置到自定义字段，或者扩展RTB结构体
		// req.Device.Browser = browserType
	}

	// 解析设备类型
	deviceType := f.parseDeviceType(userAgent)
	if deviceType != "" {
		// 这里可以设置设备类型相关字段
		// req.Device.DeviceType = deviceType
	}

	// Debug logging removed to avoid zaplog dependency

	return nil
}

// parseOSType 解析操作系统类型
func (f *UserAgentFiller) parseOSType(userAgent string) string {
	userAgent = strings.ToLower(userAgent)

	// 简单的操作系统检测逻辑
	if strings.Contains(userAgent, "android") {
		return "Android"
	}
	if strings.Contains(userAgent, "iphone") || strings.Contains(userAgent, "ipad") {
		return "iOS"
	}
	if strings.Contains(userAgent, "windows") {
		return "Windows"
	}
	if strings.Contains(userAgent, "macintosh") || strings.Contains(userAgent, "mac os") {
		return "macOS"
	}
	if strings.Contains(userAgent, "linux") {
		return "Linux"
	}

	return "Unknown"
}

// parseBrowserType 解析浏览器类型
func (f *UserAgentFiller) parseBrowserType(userAgent string) string {
	userAgent = strings.ToLower(userAgent)

	// 简单的浏览器检测逻辑
	if strings.Contains(userAgent, "chrome") && !strings.Contains(userAgent, "edg") {
		return "Chrome"
	}
	if strings.Contains(userAgent, "firefox") {
		return "Firefox"
	}
	if strings.Contains(userAgent, "safari") && !strings.Contains(userAgent, "chrome") {
		return "Safari"
	}
	if strings.Contains(userAgent, "edg") {
		return "Edge"
	}
	if strings.Contains(userAgent, "opera") {
		return "Opera"
	}

	return "Unknown"
}

// parseDeviceType 解析设备类型
func (f *UserAgentFiller) parseDeviceType(userAgent string) string {
	userAgent = strings.ToLower(userAgent)

	// 简单的设备类型检测逻辑
	if strings.Contains(userAgent, "mobile") || strings.Contains(userAgent, "android") || strings.Contains(userAgent, "iphone") {
		return "Mobile"
	}
	if strings.Contains(userAgent, "tablet") || strings.Contains(userAgent, "ipad") {
		return "Tablet"
	}

	return "Desktop"
}