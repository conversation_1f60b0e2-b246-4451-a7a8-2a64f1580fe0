package up

import (
	"bufio"
	"fmt"
	"os"
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/conf"
	"strconv"
	"strings"
	"sync"
)

// DeviceInfo 设备信息结构
type DeviceInfo struct {
	Brand         string // 品牌名
	BrandCode     int32  // 品牌编码
	ModelInternal string // 内部机型号
	ModelPublic   string // 发布机型号
	ModelCode     int32  // 机型编码
}

// DeviceMatcherFiller 设备匹配填充器
// 实现UA和机型匹配的机制，填充系统和机型编码
// 优化为常驻内存，不支持热加载，专注于高并发性能
type DeviceMatcherFiller struct {
	enabled   bool
	deviceMap map[string]*DeviceInfo // key: 品牌名或机型名，只读映射
	// 移除mutex，因为配置常驻内存，初始化后不再修改
	// 这样可以避免读锁开销，提升并发性能
}

var (
	// 单例实例
	deviceMatcherInstance *DeviceMatcherFiller
	// 单例初始化错误
	deviceMatcherInitError error
	// 确保只初始化一次
	deviceMatcherOnce sync.Once
)

// NewDeviceMatcherFiller 创建设备匹配填充器（单例模式）
// 使用sync.Once确保只初始化一次，提升性能并保证线程安全
func NewDeviceMatcherFiller() (*DeviceMatcherFiller, error) {
	deviceMatcherOnce.Do(func() {
		// 从全局配置中获取设备映射文件路径
		deviceMappingPath := conf.GlobalConfig.ModelIndex.DeviceMappingPath
		if deviceMappingPath == "" {
			deviceMatcherInitError = fmt.Errorf("device mapping path not configured")
			return
		}
		deviceMatcherInstance, deviceMatcherInitError = NewDeviceMatcherFillerWithConfig(deviceMappingPath)
	})

	return deviceMatcherInstance, deviceMatcherInitError
}

// NewDeviceMatcherFillerWithConfig 使用指定配置文件创建设备匹配填充器
// 优化为一次性加载，常驻内存，不支持热加载
func NewDeviceMatcherFillerWithConfig(configPath string) (*DeviceMatcherFiller, error) {
	// 直接加载设备映射配置到临时map
	deviceMap, err := loadDeviceMappingFromFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load device mapping: %v", err)
	}

	filler := &DeviceMatcherFiller{
		enabled:   true,
		deviceMap: deviceMap, // 直接赋值，避免后续修改
	}

	return filler, nil
}

// Name 返回填充器名称
func (f *DeviceMatcherFiller) Name() string {
	return "DeviceMatcher"
}

// IsEnabled 检查填充器是否启用
func (f *DeviceMatcherFiller) IsEnabled() bool {
	return f.enabled
}

// Fill 填充设备信息
func (f *DeviceMatcherFiller) Fill(req *rtb_types.RTBBidRequest) error {
	if !f.enabled {
		return nil
	}

	if req.Device == nil {
		return nil
	}

	// 获取UA、厂商、机型信息
	userAgent := req.Device.UserAgent
	maker := req.Device.DevMake
	model := req.Device.DevModel

	// 尝试匹配设备信息
	deviceInfo := f.matchDevice(userAgent, maker, model)
	if deviceInfo != nil {
		// 填充设备编码和操作系统编码
		f.fillDeviceCodes(req.Device, deviceInfo)
	}

	return nil
}

// loadDeviceMappingFromFile 从文件加载设备映射配置
// 独立函数，用于一次性加载配置到内存
func loadDeviceMappingFromFile(configPath string) (map[string]*DeviceInfo, error) {
	file, err := os.Open(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open config file %s: %v", configPath, err)
	}
	defer file.Close()

	// 预分配map容量，减少扩容开销
	deviceMap := make(map[string]*DeviceInfo, 1000)

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		// 跳过注释和空行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 解析配置行：品牌名\t品牌编码\t内部机型号\t发布机型号\t机型编码
		parts := strings.Split(line, "\t")
		if len(parts) != 5 {
			continue // 跳过格式不正确的行
		}

		brandCode, err := strconv.ParseInt(parts[1], 10, 32)
		if err != nil {
			continue
		}

		modelCode, err := strconv.ParseInt(parts[4], 10, 32)
		if err != nil {
			continue
		}

		deviceInfo := &DeviceInfo{
			Brand:         parts[0],
			BrandCode:     int32(brandCode),
			ModelInternal: parts[2],
			ModelPublic:   parts[3],
			ModelCode:     int32(modelCode),
		}

		// 建立多个索引键，优化查找性能
		// 1. 品牌名索引
		deviceMap[strings.ToLower(parts[0])] = deviceInfo
		// 2. 内部机型号索引
		deviceMap[strings.ToLower(parts[2])] = deviceInfo
		// 3. 发布机型号索引
		deviceMap[strings.ToLower(parts[3])] = deviceInfo
		// 4. 组合索引：品牌+机型
		deviceMap[strings.ToLower(parts[0]+" "+parts[3])] = deviceInfo
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading config file: %v", err)
	}

	return deviceMap, nil
}

// matchDevice 匹配设备信息
// 实现AC自动机类似的匹配策略，优先选择匹配长度最长的结果
// 优化：移除读锁，因为deviceMap在初始化后不会修改，提升并发性能
func (f *DeviceMatcherFiller) matchDevice(userAgent, maker, model string) *DeviceInfo {
	var bestMatch *DeviceInfo
	maxMatchLen := 0

	// 1. 优先使用已知的厂商和机型信息进行精确匹配
	if maker != "" && model != "" {
		// 尝试组合匹配
		combinedKey := strings.ToLower(maker + " " + model)
		if deviceInfo, exists := f.deviceMap[combinedKey]; exists {
			return deviceInfo
		}

		// 尝试单独匹配机型
		modelKey := strings.ToLower(model)
		if deviceInfo, exists := f.deviceMap[modelKey]; exists {
			if len(model) > maxMatchLen {
				bestMatch = deviceInfo
				maxMatchLen = len(model)
			}
		}
	}

	// 2. 如果有厂商信息，尝试匹配厂商
	if maker != "" {
		makerKey := strings.ToLower(maker)
		if deviceInfo, exists := f.deviceMap[makerKey]; exists {
			if len(maker) > maxMatchLen {
				bestMatch = deviceInfo
				maxMatchLen = len(maker)
			}
		}
	}

	// 3. 使用UA进行模糊匹配（类似AC自动机的字符串匹配）
	if userAgent != "" {
		userAgent = strings.ToLower(userAgent)

		// 遍历所有设备信息，寻找在UA中出现的设备标识
		for key, deviceInfo := range f.deviceMap {
			if strings.Contains(userAgent, key) {
				// 优先选择匹配长度最长的
				if len(key) > maxMatchLen {
					bestMatch = deviceInfo
					maxMatchLen = len(key)
				}
			}
		}
	}

	return bestMatch
}

// fillDeviceCodes 填充设备编码
func (f *DeviceMatcherFiller) fillDeviceCodes(device *rtb_types.RTBDeviceInfo, deviceInfo *DeviceInfo) {
	// 填充设备ID编码
	device.DmDeviceId = deviceInfo.ModelCode

	// 根据设备信息推断操作系统编码
	osCode := f.inferOSCode(deviceInfo, device.DevOs)
	if osCode > 0 {
		device.DmOsId = osCode
	}

	// 如果厂商信息为空，填充厂商信息
	if device.DevMake == "" {
		device.DevMake = deviceInfo.Brand
	}

	// 如果机型信息为空，填充机型信息
	if device.DevModel == "" {
		device.DevModel = deviceInfo.ModelPublic
	}
}

// inferOSCode 推断操作系统编码
// 根据用户画像项目的编码规则实现
func (f *DeviceMatcherFiller) inferOSCode(deviceInfo *DeviceInfo, currentOS string) int32 {
	// 根据品牌和当前OS信息推断操作系统编码
	brand := strings.ToLower(deviceInfo.Brand)
	currentOS = strings.ToLower(currentOS)

	// iOS设备编码范围：30000-39999
	if brand == "apple" || strings.Contains(currentOS, "ios") {
		return 30000 // iOS基础编码
	}

	// Android设备编码范围：10000-19999
	if strings.Contains(currentOS, "android") ||
		brand == "samsung" || brand == "huawei" || brand == "xiaomi" ||
		brand == "oppo" || brand == "vivo" || brand == "oneplus" ||
		brand == "realme" || brand == "honor" || brand == "google" {
		return 10000 // Android基础编码
	}

	// Windows Mobile编码范围：90000-99999
	if strings.Contains(currentOS, "windows") {
		return 90000
	}

	// 其他情况返回0，表示未知
	return 0
}

// GetDeviceInfo 获取设备信息（用于调试和测试）
// 优化：移除读锁，因为deviceMap在初始化后不会修改
func (f *DeviceMatcherFiller) GetDeviceInfo(key string) *DeviceInfo {
	return f.deviceMap[strings.ToLower(key)]
}

// GetDeviceCount 获取已加载的设备数量
// 优化：移除读锁，因为deviceMap在初始化后不会修改
func (f *DeviceMatcherFiller) GetDeviceCount() int {
	return len(f.deviceMap)
}
