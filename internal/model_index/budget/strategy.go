package budget

import (
	"rtb_model_server/internal/context"
)

// BudgetControlResult 预算控制结果
// 包含预算控制策略执行后的所有相关信息
type BudgetControlResult struct {
	Allowed     bool    // 是否允许出价
	BidRate     float64 // 竞价比率 (0-1)，用于调整最终出价
	RestBudget  float64 // 剩余预算金额
	SpeedRate   float64 // 速率控制参数 (0-1)，用于控制投放速度
	MediaPrice  int32   // 媒体价格，单位：分
	Reason      string  // 控制原因，用于日志记录和调试
}

// BudgetStrategy 预算控制策略接口
// 定义了所有预算控制策略必须实现的方法
// 不同的策略可以实现不同的预算控制逻辑
type BudgetStrategy interface {
	// ApplyBudgetControl 应用预算控制逻辑
	// 参数:
	//   - ctx: 请求上下文，包含广告索引等信息
	//   - cid: 创意ID
	// 返回:
	//   - *BudgetControlResult: 预算控制结果
	ApplyBudgetControl(ctx *context.RequestContext, cid int32) *BudgetControlResult
	
	// GetStrategyName 获取策略名称
	// 返回策略的标识名称，用于日志记录和调试
	GetStrategyName() string
}