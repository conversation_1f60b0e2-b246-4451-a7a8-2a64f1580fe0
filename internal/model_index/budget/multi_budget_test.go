package budget

import (
	"testing"
	"rtb_model_server/conf"
)

// 测试用的策略结构
type TestStrategy struct {
	Id          int32
	DailyBudget int64
}

// 测试用的活动结构
type TestCampaign struct {
	Id                int32
	ActualDailyBudget int64
}

// 测试多级预算控制功能
func TestMultiLevelBudgetControl(t *testing.T) {
	// 初始化配置
	conf.GlobalConfig.BudgetConfig = conf.BudgetConfig{
		SpeedStopRate:        0.01,
		SmallBudgetThreshold: 10000,
		BudgetThresholdRatio: 0.2,
		MaxBidPerMinute:      100,
		DecelerationFactor:   3.0,
		DefaultMediaPrice:    100,
		StatsReloadInterval:  300,
		StatsFilePath:        "data/budget/stats.data",
	}
	
	// 创建基础策略
	strategy := NewBaseBudgetStrategy()

	// 测试用例1：策略预算为0，只检查活动预算
	t.Run("StrategyBudgetZero", func(t *testing.T) {
		testStrategy := &TestStrategy{Id: 1, DailyBudget: 0}
		testCampaign := &TestCampaign{Id: 1, ActualDailyBudget: 1000}

		result := strategy.applyMultiLevelBudgetControl(1001, testStrategy, testCampaign)

		if !result.FinalAllowed {
			t.Errorf("Expected allowed when strategy budget is 0, got %v", result.FinalAllowed)
		}

		if result.StrategyResult.Reason != "strategy budget is 0, skip check" {
			t.Errorf("Expected strategy skip reason, got %s", result.StrategyResult.Reason)
		}
	})

	// 测试用例2：策略和活动预算都充足
	t.Run("BothBudgetsSufficient", func(t *testing.T) {
		testStrategy := &TestStrategy{Id: 2, DailyBudget: 1000}
		testCampaign := &TestCampaign{Id: 2, ActualDailyBudget: 2000}

		result := strategy.applyMultiLevelBudgetControl(1002, testStrategy, testCampaign)

		if !result.FinalAllowed {
			t.Errorf("Expected allowed when both budgets are sufficient, got %v", result.FinalAllowed)
		}

		if result.FinalBidRate <= 0 || result.FinalBidRate > 1 {
			t.Errorf("Expected bid rate between 0 and 1, got %f", result.FinalBidRate)
		}
	})

	// 测试用例3：策略预算不足
	t.Run("StrategyBudgetInsufficient", func(t *testing.T) {
		testStrategy := &TestStrategy{Id: 3, DailyBudget: -100}
		testCampaign := &TestCampaign{Id: 3, ActualDailyBudget: 2000}

		result := strategy.applyMultiLevelBudgetControl(1003, testStrategy, testCampaign)

		if result.FinalAllowed {
			t.Errorf("Expected not allowed when strategy budget is negative, got %v", result.FinalAllowed)
		}
	})

	// 测试用例4：活动预算不足
	t.Run("CampaignBudgetInsufficient", func(t *testing.T) {
		testStrategy := &TestStrategy{Id: 4, DailyBudget: 1000}
		testCampaign := &TestCampaign{Id: 4, ActualDailyBudget: -100}

		result := strategy.applyMultiLevelBudgetControl(1004, testStrategy, testCampaign)

		if result.FinalAllowed {
			t.Errorf("Expected not allowed when campaign budget is negative, got %v", result.FinalAllowed)
		}
	})
}

// 测试非线性减速函数
func TestNonLinearSpeedRate(t *testing.T) {
	// 初始化配置
	conf.GlobalConfig.BudgetConfig = conf.BudgetConfig{
		SpeedStopRate:        0.01,
		SmallBudgetThreshold: 10000,
		BudgetThresholdRatio: 0.2,
		MaxBidPerMinute:      100,
		DecelerationFactor:   3.0,
		DefaultMediaPrice:    100,
		StatsReloadInterval:  300,
		StatsFilePath:        "data/budget/stats.data",
	}
	
	strategy := NewBaseBudgetStrategy()

	// 测试用例1：预算充足时应该返回1.0
	t.Run("SufficientBudget", func(t *testing.T) {
		speedRate := strategy.calculateNonLinearSpeedRate(500, 1000) // 50%剩余
		if speedRate != 1.0 {
			t.Errorf("Expected speed rate 1.0 for sufficient budget, got %f", speedRate)
		}
	})

	// 测试用例2：预算不足时应该减速
	t.Run("InsufficientBudget", func(t *testing.T) {
		speedRate := strategy.calculateNonLinearSpeedRate(100, 1000) // 10%剩余，低于20%阈值
		if speedRate >= 1.0 {
			t.Errorf("Expected speed rate < 1.0 for insufficient budget, got %f", speedRate)
		}
		if speedRate < 0.01 {
			t.Errorf("Expected speed rate >= 0.01, got %f", speedRate)
		}
	})

	// 测试用例3：预算为0时应该返回0
	t.Run("ZeroBudget", func(t *testing.T) {
		speedRate := strategy.calculateNonLinearSpeedRate(100, 0)
		if speedRate != 0 {
			t.Errorf("Expected speed rate 0 for zero total budget, got %f", speedRate)
		}
	})
}

// 测试辅助方法
func TestHelperMethods(t *testing.T) {
	// 初始化配置
	conf.GlobalConfig.BudgetConfig = conf.BudgetConfig{
		SpeedStopRate:        0.01,
		SmallBudgetThreshold: 10000,
		BudgetThresholdRatio: 0.2,
		MaxBidPerMinute:      100,
		DecelerationFactor:   3.0,
		DefaultMediaPrice:    100,
		StatsReloadInterval:  300,
		StatsFilePath:        "data/budget/stats.data",
	}
	
	strategy := NewBaseBudgetStrategy()

	// 测试策略预算获取
	t.Run("GetStrategyBudget", func(t *testing.T) {
		testStrategy := &TestStrategy{Id: 1, DailyBudget: 1000}
		budget := strategy.getStrategyBudget(testStrategy)
		if budget != 1000 {
			t.Errorf("Expected budget 1000, got %d", budget)
		}
	})

	// 测试策略ID获取
	t.Run("GetStrategyId", func(t *testing.T) {
		testStrategy := &TestStrategy{Id: 123, DailyBudget: 1000}
		id := strategy.getStrategyId(testStrategy)
		if id != 123 {
			t.Errorf("Expected ID 123, got %d", id)
		}
	})

	// 测试活动预算获取
	t.Run("GetCampaignBudget", func(t *testing.T) {
		testCampaign := &TestCampaign{Id: 1, ActualDailyBudget: 2000}
		budget := strategy.getCampaignBudget(testCampaign)
		if budget != 2000 {
			t.Errorf("Expected budget 2000, got %d", budget)
		}
	})

	// 测试活动ID获取
	t.Run("GetCampaignId", func(t *testing.T) {
		testCampaign := &TestCampaign{Id: 456, ActualDailyBudget: 2000}
		id := strategy.getCampaignId(testCampaign)
		if id != 456 {
			t.Errorf("Expected ID 456, got %d", id)
		}
	})
}