package budget

// 预算类型枚举
type BudgetType int32

const (
	BUT_MONEY BudgetType = 1 // 按金额预算
	BUT_IMP   BudgetType = 2 // 按展示次数预算
)

// 预算统计信息结构体
type BudgetStats struct {
	CreativeId     int32 // 创意ID
	StrategyId     int32 // 策略ID
	CampaignId     int32 // 活动ID
	ExchangeId     int32 // 交易所ID
	Cost           int64 // 消费金额
	Impressions    int64 // 展示次数
	Clicks         int64 // 点击次数
	LastUpdateTime int64 // 最后更新时间
	StrategyStats  *Stats // 策略层级统计
	CampaignStats  *Stats // 活动层级统计
}

// 统计数据结构体
type Stats struct {
	Cost        int64 // 消费金额
	Impressions int64 // 展示次数
	Clicks      int64 // 点击次数
}

// 多层级预算控制结果
type MultiBudgetControlResult struct {
	StrategyResult *BudgetControlResult // 策略层级控制结果
	CampaignResult *BudgetControlResult // 活动层级控制结果
	FinalAllowed   bool                 // 最终是否允许出价
	FinalBidRate   float64              // 最终竞价比率
	FinalSpeedRate float64              // 最终速率控制参数
	Reason         string               // 控制原因
}

