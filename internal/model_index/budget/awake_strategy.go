package budget

import (
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"
	"rtb_model_server/internal/context"
)

// AwakeBudgetStrategy 唤醒类广告预算控制策略
// 专门针对唤醒类广告的预算控制策略
// 唤醒类广告通常用于重新激活不活跃用户，具有以下特点：
// 1. 需要严格的频率控制，避免过度打扰用户
// 2. 竞价相对保守，注重长期效果而非短期转化
// 3. 需要精确的投放时机控制
//
// 该策略在基础策略基础上增加了：
// 1. 出价频率限制机制
// 2. 保守的竞价比率调整
// 3. 基于时间窗口的频率统计
type AwakeBudgetStrategy struct {
	*BaseBudgetStrategy              // 嵌入基础策略，继承所有基础功能
	bidCountMap         map[string]int32 // 出价计数映射，key格式："策略ID_出价类型_分钟时间戳"
	bidCountMu          sync.RWMutex     // 读写锁，保护bidCountMap的并发访问
}

// NewAwakeBudgetStrategy 创建唤醒类广告预算控制策略实例
// 返回一个初始化完成的唤醒策略对象
// 初始化包括基础策略和频率控制相关的数据结构
func NewAwakeBudgetStrategy() *AwakeBudgetStrategy {
	return &AwakeBudgetStrategy{
		BaseBudgetStrategy: NewBaseBudgetStrategy(),
		bidCountMap:        make(map[string]int32), // 初始化出价计数映射
	}
}

// GetStrategyName 获取策略名称
// 返回唤醒策略的标识名称，用于日志记录和调试
func (s *AwakeBudgetStrategy) GetStrategyName() string {
	return "AwakeBudgetStrategy"
}

// ApplyBudgetControl 应用唤醒类广告预算控制逻辑
// 参数:
//   - ctx: 请求上下文，包含广告索引等信息
//   - cid: 创意ID
// 返回:
//   - BudgetControlResult: 预算控制结果，包含频率控制和保守竞价的结果
//
// 执行流程:
// 1. 先执行基础预算控制逻辑
// 2. 如果基础控制不通过，直接返回
// 3. 获取创意信息，用于频率控制
// 4. 检查出价频率是否超限
// 5. 应用保守的竞价比率调整
// 6. 返回控制结果
func (s *AwakeBudgetStrategy) ApplyBudgetControl(ctx *context.RequestContext, cid int32) *BudgetControlResult {
	// 先执行基础控制逻辑
	// 基础控制包括预算检查、速率控制等通用逻辑
	result := s.BaseBudgetStrategy.ApplyBudgetControl(ctx, cid)
	if !result.Allowed {
		// 如果基础控制不通过，直接返回结果
		return result
	}

	// 获取创意信息
	// 需要策略ID来进行频率控制
	creative, err := ctx.AdIndex.GetCreative(cid)
	if err != nil {
		return &BudgetControlResult{
			Allowed: false,
			Reason:  "creative not found",
		}
	}

	// 唤醒类广告特殊逻辑：频率控制
	// 检查当前策略的出价频率是否超过限制
	if !s.checkBidFrequency(creative.StrategyId, "awake", s.config.MaxBidPerMinute) {
		return &BudgetControlResult{
			Allowed: false,
			Reason:  "bid frequency limit exceeded",
		}
	}

	// 唤醒类广告采用保守的竞价策略
	// 降低竞价比率到70%，避免过度竞争
	// 这样可以确保在合适的价位获得流量，同时控制成本
	result.BidRate = result.BidRate * 0.7
	result.Reason = "awake strategy conservative bidding applied"

	return result
}

// checkBidFrequency 检查出价频率是否超限
// 参数:
//   - strategyId: 策略ID
//   - bidType: 出价类型（如"awake"）
//   - maxBidPerMinute: 每分钟最大出价次数
// 返回:
//   - bool: true表示频率正常，false表示频率超限
//
// 算法说明:
// 1. 使用"策略ID_出价类型_分钟时间戳"作为key
// 2. 统计当前分钟内的出价次数
// 3. 如果超过限制则拒绝出价
// 4. 定期清理过期的统计数据
func (s *AwakeBudgetStrategy) checkBidFrequency(strategyId int32, bidType string, maxBidPerMinute int32) bool {
	// 加写锁，保护并发访问
	s.bidCountMu.Lock()
	defer s.bidCountMu.Unlock()

	// 构造当前分钟的统计key
	// 格式："策略ID_出价类型_分钟时间戳"
	currentMinute := time.Now().Unix() / 60
	key := fmt.Sprintf("%d_%s_%d", strategyId, bidType, currentMinute)
	
	// 获取当前分钟的出价次数
	count := s.bidCountMap[key]

	// 检查是否超过频率限制
	if count >= maxBidPerMinute {
		// 超过限制，拒绝出价
		return false
	}

	// 增加出价计数
	s.bidCountMap[key] = count + 1

	// 清理过期的计数数据
	// 只保留最近5分钟的数据，避免内存泄漏
	for k := range s.bidCountMap {
		parts := strings.Split(k, "_")
		if len(parts) >= 3 {
			// 解析时间戳
			if minute, err := strconv.ParseInt(parts[2], 10, 64); err == nil {
				// 清理5分钟前的数据
				if currentMinute-minute > 5 {
					delete(s.bidCountMap, k)
				}
			}
		}
	}

	// 频率检查通过
	return true
}