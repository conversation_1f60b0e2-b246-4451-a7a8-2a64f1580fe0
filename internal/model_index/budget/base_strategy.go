package budget

import (
	"math"
	"reflect"
	"rtb_model_server/conf"
	"rtb_model_server/internal/context"
)

// BaseBudgetStrategy 基础预算控制策略
// 提供所有预算控制策略的基础功能，包括：
// 1. 基础预算检查和控制逻辑
// 2. 速率控制参数计算
// 3. 竞价比率计算
// 4. 统计信息处理
//
// 该策略作为其他具体策略的基础，实现了通用的预算控制算法
type BaseBudgetStrategy struct {
	statsProcessor *StatsProcessor // 统计信息处理器，用于获取广告投放统计数据
	config         *conf.BudgetConfig // 预算配置信息，包含各种阈值和参数
}

// NewBaseBudgetStrategy 创建基础预算控制策略实例
// 返回一个初始化完成的基础策略对象，包含统计处理器和配置信息
func NewBaseBudgetStrategy() *BaseBudgetStrategy {
	return &BaseBudgetStrategy{
		statsProcessor: GetStatsProcessor(),
		config:         &conf.GlobalConfig.BudgetConfig,
	}
}

// GetStrategyName 获取策略名称
// 返回策略的标识名称，用于日志记录和调试
func (s *BaseBudgetStrategy) GetStrategyName() string {
	return "BaseBudgetStrategy"
}

// ApplyBudgetControl 应用多层级预算控制逻辑
// 参数:
//   - ctx: 请求上下文，包含广告索引等信息
//   - cid: 创意ID
// 返回:
//   - BudgetControlResult: 预算控制结果，包含是否允许出价、竞价比率等信息
//
// 执行流程:
// 1. 获取创意和策略信息
// 2. 获取Campaign信息
// 3. 检查Campaign和Strategy层级预算
// 4. 计算多层级速率控制参数
// 5. 计算最终竞价比率
// 6. 返回控制结果
func (s *BaseBudgetStrategy) ApplyBudgetControl(ctx *context.RequestContext, cid int32) *BudgetControlResult {
	// 获取创意信息
	creative, err := ctx.AdIndex.GetCreative(cid)
	if err != nil {
		return &BudgetControlResult{
			Allowed: false,
			Reason:  "creative not found",
		}
	}

	// 获取策略信息
	strategy, err := ctx.AdIndex.GetStrategy(creative.StrategyId)
	if err != nil {
		return &BudgetControlResult{
			Allowed: false,
			Reason:  "strategy not found",
		}
	}

	// 获取活动信息
	campaign, err := ctx.AdIndex.GetCampaign(creative.CampaignId)
	if err != nil {
		return &BudgetControlResult{
			Allowed: false,
			Reason:  "campaign not found",
		}
	}

	// 执行多层级预算控制
	multiResult := s.applyMultiLevelBudgetControl(cid, strategy, campaign)
	if !multiResult.FinalAllowed {
		return &BudgetControlResult{
			Allowed: false,
			Reason:  multiResult.Reason,
		}
	}

	return &BudgetControlResult{
		Allowed:    true,
		BidRate:    multiResult.FinalBidRate,
		RestBudget: 0, // 这里可以根据需要设置具体的剩余预算
		SpeedRate:  multiResult.FinalSpeedRate,
		MediaPrice: int32(s.config.DefaultMediaPrice),
		Reason:     "multi-level budget control passed",
	}
}

// applyMultiLevelBudgetControl 应用多层级预算控制
// 参数:
//   - cid: 创意ID
//   - strategy: 策略信息
//   - campaign: 活动信息
// 返回:
//   - *MultiBudgetControlResult: 多层级预算控制结果
func (s *BaseBudgetStrategy) applyMultiLevelBudgetControl(cid int32, strategy interface{}, campaign interface{}) *MultiBudgetControlResult {
	// 获取策略预算和统计
	strategyBudget := s.getStrategyBudget(strategy)
	strategyStats := s.statsProcessor.GetStrategyStats(s.getStrategyId(strategy))
	
	// 获取活动预算和统计
	campaignBudget := s.getCampaignBudget(campaign)
	campaignStats := s.statsProcessor.GetCampaignStats(s.getCampaignId(campaign))
	
	// 策略层级预算控制
	strategyResult := s.applySingleLevelBudgetControl(strategyBudget, strategyStats, "strategy")
	
	// 活动层级预算控制
	campaignResult := s.applySingleLevelBudgetControl(campaignBudget, campaignStats, "campaign")
	
	// 如果策略预算设置为0，只看活动级别的预算情况
	if strategyBudget == 0 {
		return &MultiBudgetControlResult{
			StrategyResult: &BudgetControlResult{Allowed: true, BidRate: 1.0, SpeedRate: 1.0, Reason: "strategy budget is 0, skip check"},
			CampaignResult: campaignResult,
			FinalAllowed:   campaignResult.Allowed,
			FinalBidRate:   campaignResult.BidRate,
			FinalSpeedRate: campaignResult.SpeedRate,
			Reason:         campaignResult.Reason,
		}
	}
	
	// 两个层级都需要通过才能允许出价
	finalAllowed := strategyResult.Allowed && campaignResult.Allowed
	var finalBidRate, finalSpeedRate float64
	var reason string
	
	if finalAllowed {
		// 取两个层级中更严格的控制参数
		finalBidRate = math.Min(strategyResult.BidRate, campaignResult.BidRate)
		finalSpeedRate = math.Min(strategyResult.SpeedRate, campaignResult.SpeedRate)
		reason = "multi-level budget control passed"
	} else {
		if !strategyResult.Allowed {
			reason = "strategy " + strategyResult.Reason
		} else {
			reason = "campaign " + campaignResult.Reason
		}
	}
	
	return &MultiBudgetControlResult{
		StrategyResult: strategyResult,
		CampaignResult: campaignResult,
		FinalAllowed:   finalAllowed,
		FinalBidRate:   finalBidRate,
		FinalSpeedRate: finalSpeedRate,
		Reason:         reason,
	}
}

// applySingleLevelBudgetControl 应用单层级预算控制
func (s *BaseBudgetStrategy) applySingleLevelBudgetControl(budget int64, stats *Stats, level string) *BudgetControlResult {
	if budget <= 0 {
		return &BudgetControlResult{
			Allowed: false,
			Reason:  level + " budget is 0 or negative",
		}
	}
	
	// 如果没有统计信息，允许出价
	if stats == nil {
		return &BudgetControlResult{
			Allowed:    true,
			BidRate:    1.0,
			RestBudget: float64(budget),
			SpeedRate:  1.0,
			Reason:     level + " no stats available",
		}
	}
	
	// 计算剩余预算
	restBudget := float64(budget) - float64(stats.Cost)
	if restBudget <= 0 {
		return &BudgetControlResult{
			Allowed: false,
			Reason:  level + " budget exhausted",
		}
	}
	
	// 计算非线性减速参数
	speedRate := s.calculateNonLinearSpeedRate(restBudget, float64(budget))
	if speedRate < s.config.SpeedStopRate {
		return &BudgetControlResult{
			Allowed: false,
			Reason:  level + " speed rate too low",
		}
	}
	
	// 计算竞价比率
	bidRate := s.calculateBidRate(restBudget, speedRate)
	
	return &BudgetControlResult{
		Allowed:    true,
		BidRate:    bidRate,
		RestBudget: restBudget,
		SpeedRate:  speedRate,
		Reason:     level + " budget control passed",
	}
}

// calculateNonLinearSpeedRate 计算非线性减速参数
// 参数:
//   - restBudget: 剩余预算
//   - totalBudget: 总预算
// 返回:
//   - float64: 速率控制参数 (0-1)
//
// 算法说明:
// 1. 当剩余预算比例高于阈值时，保持正常速率(1.0)
// 2. 当剩余预算比例低于阈值时，使用非线性函数降低速率
// 3. 使用指数函数实现非线性减速，当预算只剩下阈值以内时触发减速
func (s *BaseBudgetStrategy) calculateNonLinearSpeedRate(restBudget, totalBudget float64) float64 {
	if totalBudget <= 0 {
		return 0
	}

	// 计算预算剩余比例
	budgetRatio := restBudget / totalBudget
	if budgetRatio >= s.config.BudgetThresholdRatio {
		// 预算充足时，保持正常速率
		return 1.0
	}

	// 预算不足时，使用非线性减速函数
	// 使用指数函数: speedRate = (budgetRatio / threshold)^decelerationFactor
	// 这样可以实现非线性减速，当预算越少时减速越明显
	normalizedRatio := budgetRatio / s.config.BudgetThresholdRatio
	speedRate := math.Pow(normalizedRatio, s.config.DecelerationFactor)
	
	// 确保速率在合理范围内
	if speedRate < 0.01 {
		speedRate = 0.01
	}
	
	return speedRate
}

// calculateBidRate 计算竞价比率
// 参数:
//   - restBudget: 剩余预算
//   - speedRate: 速率控制参数
// 返回:
//   - float64: 竞价比率 (0-1)
//
// 算法说明:
// 1. 小预算时降低竞价比率，避免预算快速耗尽
// 2. 正常预算时使用速率参数作为竞价比率
func (s *BaseBudgetStrategy) calculateBidRate(restBudget, speedRate float64) float64 {
	if restBudget < float64(s.config.SmallBudgetThreshold) {
		// 小预算时，降低竞价比率
		// 竞价比率 = 速率 * 0.8
		return speedRate * 0.8
	}

	// 正常预算时，使用速率作为竞价比率
	return speedRate
}

// 定义接口类型
type StrategyWithBudget interface {
	GetDailyBudget() int64
}

type StrategyWithId interface {
	GetId() int32
}

type CampaignWithBudget interface {
	GetActualDailyBudget() int64
}

type CampaignWithId interface {
	GetId() int32
}

// 辅助方法：获取策略预算
func (s *BaseBudgetStrategy) getStrategyBudget(strategy interface{}) int64 {
	// 使用反射获取DailyBudget字段
	v := reflect.ValueOf(strategy)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	if v.Kind() == reflect.Struct {
		field := v.FieldByName("DailyBudget")
		if field.IsValid() && field.CanInterface() {
			if budget, ok := field.Interface().(int64); ok {
				return budget
			}
		}
	}
	return 0
}

// 辅助方法：获取策略ID
func (s *BaseBudgetStrategy) getStrategyId(strategy interface{}) int32 {
	// 使用反射获取Id字段
	v := reflect.ValueOf(strategy)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	if v.Kind() == reflect.Struct {
		field := v.FieldByName("Id")
		if field.IsValid() && field.CanInterface() {
			if id, ok := field.Interface().(int32); ok {
				return id
			}
		}
	}
	return 0
}

// 辅助方法：获取活动预算
func (s *BaseBudgetStrategy) getCampaignBudget(campaign interface{}) int64 {
	// 使用反射获取ActualDailyBudget字段
	v := reflect.ValueOf(campaign)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	if v.Kind() == reflect.Struct {
		field := v.FieldByName("ActualDailyBudget")
		if field.IsValid() && field.CanInterface() {
			if budget, ok := field.Interface().(int64); ok {
				return budget
			}
		}
	}
	return 0
}

// 辅助方法：获取活动ID
func (s *BaseBudgetStrategy) getCampaignId(campaign interface{}) int32 {
	// 使用反射获取Id字段
	v := reflect.ValueOf(campaign)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	if v.Kind() == reflect.Struct {
		field := v.FieldByName("Id")
		if field.IsValid() && field.CanInterface() {
			if id, ok := field.Interface().(int32); ok {
				return id
			}
		}
	}
	return 0
}