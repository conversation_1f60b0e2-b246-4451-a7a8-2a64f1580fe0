package budget

import (
	"math"
	"rtb_model_server/internal/context"
)

// CPCBudgetStrategy CPC广告预算控制策略
// 专门针对CPC（按点击付费）广告的预算控制策略
// 继承基础策略的所有功能，并添加以下CPC特有的优化：
// 1. 基于点击率(CTR)的动态竞价调整
// 2. 高CTR创意提升竞价比率，获得更多流量
// 3. 低CTR创意降低竞价比率，减少无效消耗
//
// 适用场景:
// - CPC计费模式的广告投放
// - 需要根据点击效果优化投放的场景
// - 追求点击转化效率的广告主
type CPCBudgetStrategy struct {
	*BaseBudgetStrategy // 嵌入基础策略，继承所有基础功能
}

// NewCPCBudgetStrategy 创建CPC预算控制策略实例
// 返回一个初始化完成的CPC策略对象
// 该策略基于基础策略构建，添加了CPC特有的优化逻辑
func NewCPCBudgetStrategy() *CPCBudgetStrategy {
	return &CPCBudgetStrategy{
		BaseBudgetStrategy: NewBaseBudgetStrategy(),
	}
}

// GetStrategyName 获取策略名称
// 返回CPC策略的标识名称，用于日志记录和调试
func (s *CPCBudgetStrategy) GetStrategyName() string {
	return "CPCBudgetStrategy"
}

// ApplyBudgetControl 应用CPC预算控制逻辑
// 参数:
//   - ctx: 请求上下文，包含广告索引等信息
//   - cid: 创意ID
// 返回:
//   - BudgetControlResult: 预算控制结果，包含CPC优化后的竞价比率
//
// 执行流程:
// 1. 先执行基础预算控制逻辑
// 2. 如果基础控制不通过，直接返回
// 3. 获取创意的点击统计数据
// 4. 计算点击率(CTR)
// 5. 根据CTR调整竞价比率
// 6. 返回优化后的控制结果
func (s *CPCBudgetStrategy) ApplyBudgetControl(ctx *context.RequestContext, cid int32) *BudgetControlResult {
	// 先执行基础控制逻辑
	// 基础控制包括预算检查、速率控制等通用逻辑
	result := s.BaseBudgetStrategy.ApplyBudgetControl(ctx, cid)
	if !result.Allowed {
		// 如果基础控制不通过，直接返回结果
		return result
	}

	// CPC特殊逻辑：基于点击率调整竞价比率
	// 获取创意的统计数据，用于计算CTR
	stats := s.statsProcessor.GetStats(cid)
	if stats != nil && stats.Impressions > 0 {
		// 计算点击率 CTR = 点击次数 / 展示次数
		ctr := float64(stats.Clicks) / float64(stats.Impressions)
		
		// 根据CTR水平调整竞价比率
		if ctr > 0.01 {
			// 点击率大于1%时提高竞价比率
			// 高CTR说明创意质量好，用户喜欢，值得投入更多预算
			// 竞价比率提升20%，但不超过100%
			result.BidRate = math.Min(1.0, result.BidRate*1.2)
			result.Reason = "high CTR boost applied"
		} else if ctr < 0.005 {
			// 点击率小于0.5%时降低竞价比率
			// 低CTR说明创意效果差，用户不感兴趣，应该降低投入
			// 竞价比率降低20%
			result.BidRate = result.BidRate * 0.8
			result.Reason = "low CTR penalty applied"
		} else {
			// CTR在正常范围内，保持原有竞价比率
			result.Reason = "normal CTR, no adjustment"
		}
	} else {
		// 没有足够的统计数据时，保持原有竞价比率
		result.Reason = "insufficient stats for CTR calculation"
	}

	return result
}