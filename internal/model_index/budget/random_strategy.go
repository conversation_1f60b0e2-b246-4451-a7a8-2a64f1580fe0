package budget

import (
	"math/rand"
	"rtb_model_server/internal/context"
	"rtb_model_server/internal/zaplog"
	"go.uber.org/zap"
)

// RandomBudgetStrategy 随机预算控制策略
// 专门用于A/B测试和实验性投放的预算控制策略
// 该策略在基础预算控制的基础上，引入随机因子来影响竞价决策
//
// 主要特点:
// 1. 基于随机因子进行竞价调整
// 2. 适用于A/B测试场景
// 3. 可以模拟不同的投放策略效果
// 4. 提供详细的调试日志
//
// 适用场景:
// - A/B测试实验
// - 策略效果对比
// - 算法优化验证
// - 风险控制测试
type RandomBudgetStrategy struct {
	*BaseBudgetStrategy        // 嵌入基础策略，继承所有基础功能
	randomFactor        float64 // 随机因子，控制随机调整的概率 (0-1)
}

// NewRandomBudgetStrategy 创建随机预算控制策略实例
// 参数:
//   - randomFactor: 随机因子，取值范围0-1
//     - 0: 不进行随机调整，等同于基础策略
//     - 1: 总是进行随机调整
//     - 0.1: 10%的概率进行随机调整
// 返回:
//   - *RandomBudgetStrategy: 初始化完成的随机策略对象
func NewRandomBudgetStrategy(randomFactor float64) *RandomBudgetStrategy {
	return &RandomBudgetStrategy{
		BaseBudgetStrategy: NewBaseBudgetStrategy(),
		randomFactor:       randomFactor,
	}
}

// GetStrategyName 获取策略名称
// 返回随机策略的标识名称，用于日志记录和调试
func (s *RandomBudgetStrategy) GetStrategyName() string {
	return "RandomBudgetStrategy"
}

// ApplyBudgetControl 应用随机预算控制逻辑
// 参数:
//   - ctx: 请求上下文，包含广告索引等信息
//   - cid: 创意ID
// 返回:
//   - BudgetControlResult: 预算控制结果，包含随机调整后的竞价比率
//
// 执行流程:
// 1. 先执行基础预算控制逻辑
// 2. 如果基础控制不通过，直接返回
// 3. 生成随机数
// 4. 根据随机因子决定是否进行随机调整
// 5. 如果需要调整，则应用随机竞价比率
// 6. 记录调试日志
// 7. 返回控制结果
func (s *RandomBudgetStrategy) ApplyBudgetControl(ctx *context.RequestContext, cid int32) *BudgetControlResult {
	// 先执行基础控制逻辑
	// 基础控制包括预算检查、速率控制等通用逻辑
	result := s.BaseBudgetStrategy.ApplyBudgetControl(ctx, cid)
	if !result.Allowed {
		// 如果基础控制不通过，直接返回结果
		return result
	}

	// 生成0-1之间的随机数
	randomValue := rand.Float64()
	
	// 根据随机因子决定是否进行随机调整
	if randomValue < s.randomFactor {
		// 应用随机调整
		// 随机竞价比率 = 原竞价比率 * (0.5 + 随机值)
		// 这样可以确保调整后的比率在 [0.5*原比率, 1.5*原比率] 范围内
		originalBidRate := result.BidRate
		result.BidRate = result.BidRate * (0.5 + randomValue)
		result.Reason = "random adjustment applied"
		
		// 记录调试日志，便于分析随机策略的效果
		zaplog.Logger.Debug("random budget control applied",
			zap.Int32("cid", cid),
			zap.Float64("random_value", randomValue),
			zap.Float64("random_factor", s.randomFactor),
			zap.Float64("original_bid_rate", originalBidRate),
			zap.Float64("adjusted_bid_rate", result.BidRate),
			zap.Float64("adjustment_multiplier", 0.5+randomValue))
	} else {
		// 不进行随机调整，保持原有竞价比率
		result.Reason = "no random adjustment"
		
		// 记录未调整的情况，用于统计分析
		zaplog.Logger.Debug("random budget control skipped",
			zap.Int32("cid", cid),
			zap.Float64("random_value", randomValue),
			zap.Float64("random_factor", s.randomFactor),
			zap.String("reason", "random value exceeds factor"))
	}

	return result
}

// GetRandomFactor 获取当前的随机因子
// 返回当前策略使用的随机因子值
// 用于外部监控和调试
func (s *RandomBudgetStrategy) GetRandomFactor() float64 {
	return s.randomFactor
}

// SetRandomFactor 设置随机因子
// 参数:
//   - factor: 新的随机因子值 (0-1)
// 允许动态调整随机因子，用于实时优化实验参数
func (s *RandomBudgetStrategy) SetRandomFactor(factor float64) {
	if factor >= 0 && factor <= 1 {
		s.randomFactor = factor
		zaplog.Logger.Info("random factor updated",
			zap.Float64("new_factor", factor))
	} else {
		zaplog.Logger.Warn("invalid random factor",
			zap.Float64("attempted_factor", factor),
			zap.String("valid_range", "0-1"))
	}
}