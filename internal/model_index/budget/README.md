# 预算控制策略模块

本模块实现了RTB广告投放系统的预算控制功能，通过不同的策略来控制广告的投放预算和竞价行为。

## 文件结构

```
budget/
├── strategy.go          # 策略接口定义和结果结构体
├── base_strategy.go     # 基础预算控制策略
├── cpc_strategy.go      # CPC广告预算控制策略
├── awake_strategy.go    # 唤醒类广告预算控制策略
├── random_strategy.go   # 随机预算控制策略（用于A/B测试）
├── budget_processor.go  # 预算处理器（策略管理和调度）
├── stats_processor.go   # 统计数据处理器
└── README.md           # 本文档
```

## 策略说明

### 1. 基础策略 (BaseBudgetStrategy)

**文件**: `base_strategy.go`

**功能**:
- 提供所有预算控制策略的基础功能
- 实现通用的预算检查和控制逻辑
- 速率控制参数计算
- 竞价比率计算

**核心算法**:
- 剩余预算检查：`剩余预算 = 日预算 - 已消耗预算`
- 速率控制：当预算剩余比例低于阈值时，按比例降低投放速率
- 竞价调整：小预算时降低竞价比率，避免预算快速耗尽

**适用场景**:
- 通用广告投放
- 作为其他策略的基础类

### 2. CPC策略 (CPCBudgetStrategy)

**文件**: `cpc_strategy.go`

**功能**:
- 专门针对CPC（按点击付费）广告的预算控制
- 基于点击率(CTR)的动态竞价调整
- 继承基础策略的所有功能

**核心算法**:
- CTR计算：`CTR = 点击次数 / 展示次数`
- 高CTR优化：CTR > 1% 时，竞价比率提升20%
- 低CTR惩罚：CTR < 0.5% 时，竞价比率降低20%

**适用场景**:
- CPC计费模式的广告投放
- 需要根据点击效果优化投放的场景
- 追求点击转化效率的广告主

### 3. 唤醒策略 (AwakeBudgetStrategy)

**文件**: `awake_strategy.go`

**功能**:
- 专门针对唤醒类广告的预算控制
- 严格的频率控制，避免过度打扰用户
- 保守的竞价策略

**核心算法**:
- 频率控制：基于"策略ID_出价类型_分钟时间戳"的计数机制
- 保守竞价：竞价比率固定降低到70%
- 自动清理：定期清理5分钟前的频率统计数据

**适用场景**:
- 重新激活不活跃用户的广告
- 需要精确控制投放频率的场景
- 注重长期效果而非短期转化的投放

### 4. 随机策略 (RandomBudgetStrategy)

**文件**: `random_strategy.go`

**功能**:
- 用于A/B测试和实验性投放
- 基于随机因子进行竞价调整
- 提供详细的调试日志

**核心算法**:
- 随机调整：`调整后竞价比率 = 原竞价比率 * (0.5 + 随机值)`
- 概率控制：通过随机因子控制调整的概率
- 范围限制：确保调整后的比率在合理范围内

**适用场景**:
- A/B测试实验
- 策略效果对比
- 算法优化验证
- 风险控制测试

## 接口定义

### BudgetStrategy 接口

```go
type BudgetStrategy interface {
    // 应用预算控制逻辑
    ApplyBudgetControl(ctx *context.RequestContext, cid int32) *BudgetControlResult
    
    // 获取策略名称
    GetStrategyName() string
}
```

### BudgetControlResult 结构体

```go
type BudgetControlResult struct {
    Allowed     bool    // 是否允许出价
    BidRate     float64 // 竞价比率 (0-1)
    RestBudget  float64 // 剩余预算金额
    SpeedRate   float64 // 速率控制参数 (0-1)
    MediaPrice  int32   // 媒体价格，单位：分
    Reason      string  // 控制原因，用于日志记录和调试
}
```

## 使用方法

### 1. 获取预算处理器

```go
processor := budget.GetBudgetProcessor()
```

### 2. 应用预算控制

```go
results := processor.ApplyBudgetControl(ctx, creativeIds)
for cid, result := range results {
    if result.Allowed {
        // 允许出价，使用 result.BidRate 调整出价
        finalBid := baseBid * result.BidRate
    } else {
        // 拒绝出价，原因：result.Reason
    }
}
```

### 3. 添加自定义策略

```go
customStrategy := &MyCustomStrategy{}
processor.AddStrategy("custom", customStrategy)
```

### 4. 更新预算消耗

```go
processor.UpdateBudgetConsumption(creativeId, cost, impressions, clicks)
```

## 策略选择逻辑

预算处理器会根据广告类型自动选择合适的策略：

1. **CPC广告** (CostType = 2) → 使用 CPCBudgetStrategy
2. **唤醒类广告** (AdMatchType = 8) → 使用 AwakeBudgetStrategy
3. **其他广告** → 使用 BaseBudgetStrategy（默认策略）

## 配置管理

### 配置文件位置
`data/budget/budget_config.json`

### 配置参数说明

```json
{
  "speed_stop_rate": 0.5,           // 速率停止阈值
  "small_budget_threshold": 1000.0, // 小预算阈值
  "max_bid_per_minute": 10,         // 每分钟最大出价次数
  "budget_threshold_ratio": 0.1,    // 预算阈值比例
  "deceleration_factor": 2.0,       // 减速因子
  "default_media_price": 100,       // 默认媒体价格
  "stats_reload_interval": 300      // 统计数据重载间隔(秒)
}
```

## 数据文件

### 统计数据文件
`data/budget/stats.data`

格式：`创意ID,策略ID,活动ID,交易所ID,消费金额,展示次数,点击次数`

示例：
```
12345,100,10,1,150.50,1000,50
67890,200,20,2,89.30,800,25
```

## 使用示例

```go
// 创建预算处理器
processor, err := NewBudgetProcessor("data/budget/stats.data")
if err != nil {
    log.Fatal(err)
}

// 在请求处理中使用
func handleBidRequest(ctx *context.RequestContext) {
    // 执行预算过滤
    processor.Process(ctx)
    
    // 继续处理剩余的创意...
}

// 更新预算消耗
processor.UpdateBudgetConsumption(exchangeId, strategyId, experimentId, cost, impressions, clicks)

// 获取预算控制统计信息
stats := processor.GetBudgetControlStats()
```

## 测试

运行测试：
```bash
go test ./internal/model_index/budget/
```

测试覆盖：
- 基本功能测试
- 预算过滤测试
- 出价频率限制测试
- 预算消费更新测试

## 注意事项

1. **线程安全**: 所有公共方法都是线程安全的
2. **性能优化**: 使用读写锁减少锁竞争
3. **内存管理**: 自动清理过期的出价计数数据
4. **配置热更新**: 支持运行时更新配置参数
5. **日志记录**: 重要操作都有详细的日志记录

## 监控指标

通过 `GetBudgetControlStats()` 可以获取：
- 总创意数量
- 预算控制规则数量
- 各交易所的统计信息
- 最后加载时间