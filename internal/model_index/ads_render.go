package model_index

import (
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/model_index/predict"

	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	ms "rtb_model_server/common/domob_thrift/rtb_model_server"

	"github.com/pkg/errors"
)

type AdsRender struct {
}

func NewAdsRender() *AdsRender {
	return &AdsRender{}
}

// 将bidResult的结果填写到ctx中的BidResponse
func (a *AdsRender) Render(requestCtx *ctx.RequestContext, bidResult *predict.BidResult) error {
	bidRequest := requestCtx.BidRequest
	requestCtx.BidResponse = &ms.RTBModelServerResponse{
		Status:        0,
		SearchId:      bidRequest.SearchId,
		DmpTagIds:     []int32{},
		DmUid:         0,
		Idfa:          bidRequest.Device.Idfa,
		Imei:          bidRequest.Device.Imei,
		DmpKeywordIds: []int64{},
		DmpCrowdIds:   []int32{},
		ResponseList:  []*ms.RTBModelServerResponseInfo{},
	}
	if bidResult == nil || bidResult.CreativeId == 0 {
		return nil
	}

	responseInfo, err := a.renderCreative(requestCtx, bidResult)
	if err != nil {
		return err
	}
	requestCtx.BidResponse.ResponseList = append(requestCtx.BidResponse.ResponseList, responseInfo)
	return nil
}
func (a *AdsRender) renderCreative(requestCtx *ctx.RequestContext, bidResult *predict.BidResult) (*ms.RTBModelServerResponseInfo, error) {
	creative, err := requestCtx.AdIndex.GetCreative(bidResult.CreativeId)
	if err != nil {
		return nil, errors.Wrap(err, "render creative failed")
	}
	strategy, err := requestCtx.AdIndex.GetStrategy(creative.StrategyId)
	if err != nil {
		return nil, errors.Wrap(err, "render creative failed")
	}
	campaign, err := requestCtx.AdIndex.GetCampaign(creative.CampaignId)
	if err != nil {
		return nil, errors.Wrap(err, "render creative failed")
	}
	sponsor, err := requestCtx.AdIndex.GetSponsor(creative.SponsorId)
	if err != nil {
		return nil, errors.Wrap(err, "render creative failed")
	}
	promotion, err := requestCtx.AdIndex.GetPromotion(creative.PromotionIds[0])
	if err != nil {
		return nil, errors.Wrap(err, "render creative failed")
	}

	respInfo := &ms.RTBModelServerResponseInfo{
		SearchImpId: bidResult.SearchImpId,
		Expid:       -1,
		TrafId:      0,
		AdList:      []*ms.RTBModelServerAdInfo{},
	}

	adInfo := &ms.RTBModelServerAdInfo{
		CreativeId:  bidResult.CreativeId,
		CampaignId:  creative.CampaignId,
		StrategyId:  creative.StrategyId,
		SponsorId:   creative.SponsorId,
		Bid:         bidResult.BidPrice,
		Ctr:         int32(bidResult.PreCtr * 1000000),
		Atr:         int32(bidResult.PreAtr * 1000000),
		Winrate:     int32(bidResult.WinRate * 1000000),
		AdMatchType: bidResult.MatchedAdMatchType,
		AdPosId:     bidResult.MatchedAdPosId,
		SelectProb:  bidResult.SelectProb,
		CpaBid:      bidResult.CpaBid,
		Creative:    creative,
		Strategy:    strategy,
		Campaign:    campaign,
		Sponsor:     sponsor,
		Promotions:  []*rtb_adinfo_types.RTBPromotion{promotion},
	}
	respInfo.AdList = append(respInfo.AdList, adInfo)
	return respInfo, nil
}
