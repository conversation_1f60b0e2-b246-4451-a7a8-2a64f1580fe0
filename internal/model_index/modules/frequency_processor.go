package modules

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"rtb_model_server/conf"
	"rtb_model_server/internal/zaplog"
	"strconv"
	"sync"
	"time"

	"go.uber.org/zap"
)

type FrequencyProcessor struct {
	redisPool *RedisPool
	mu        sync.Mutex
}

type FrequencyConfig struct {
	ID            int    `json:"id"`
	Name          string `json:"name"`
	Type          string `json:"type"`
	SponsorID     int    `json:"sponsor_id"`
	FreqDays      int    `json:"freq_days"`
	ImpFreqTarget int    `json:"imp_freq_target"`
	ClkFreqTarget int    `json:"clk_freq_target"`
	UID           int    `json:"uid"`
	Status        int    `json:"status"`
	CreateTime    int    `json:"create_time"`
	LastUpdate    int    `json:"last_update"`
}

var defaultFreqConfigs map[int32]*FrequencyConfig

func NewFrequencyProcessor(redisPool *RedisPool) *FrequencyProcessor {

	defaultFreqConfigs = make(map[int32]*FrequencyConfig, 0)
	fp := &FrequencyProcessor{
		redisPool: redisPool,
	}

	fp.Process()
	fp.StartLooper()
	return fp
}

func (fp *FrequencyProcessor) StartLooper() {
	go func() {
		for {
			time.Sleep(time.Second * time.Duration(conf.GlobalConfig.FrequencyConfig.LoopInterval))
			err := fp.Process()
			if err != nil {
				zaplog.Logger.Error("FrequencyProcessor Process failed", zap.Error(err))
			}
		}
	}()
}

func (fp *FrequencyProcessor) Process() error {
	client := fp.redisPool.GetClient()
	fcMap, err := client.HGetAll(context.Background(), fp.buildFCKey()).Result()
	if err != nil {
		return err
	}
	newFreqConfigs := make(map[int32]*FrequencyConfig, 0)
	for _, fc := range fcMap {
		config, err := fp.parseConfig(fc)
		if err != nil {
			zaplog.Logger.Warn("FrequencyProcessor Process parseConfig failed", zap.String("fc", fc), zap.Error(err))
			continue
		}
		newFreqConfigs[int32(config.SponsorID)] = config
	}
	fp.mu.Lock()
	fp.mu.Unlock()
	defaultFreqConfigs = newFreqConfigs
	zaplog.Logger.Info("FrequencyProcessor Process success", zap.Int("config_count", len(defaultFreqConfigs)))
	return nil
}

func (fp *FrequencyProcessor) GetFreqConfigs() map[int32]*FrequencyConfig {
	fp.mu.Lock()
	defer fp.mu.Unlock()
	return defaultFreqConfigs
}

func (fp *FrequencyProcessor) GetClkFrequencyData(oaidMd5, gaid, idfaMd5 string) (map[string]int, error) {
	return fp.getFrequencyData(conf.GlobalConfig.FrequencyConfig.ClkFrequencyKeyPrefix, oaidMd5, gaid, idfaMd5)
}

func (fp *FrequencyProcessor) GetImpFrequencyData(oaidMd5, gaid, idfaMd5 string) (map[string]int, error) {
	return fp.getFrequencyData(conf.GlobalConfig.FrequencyConfig.ImpFrequencyKeyPrefix, oaidMd5, gaid, idfaMd5)
}

func (p *FrequencyProcessor) buildEventKey(eventType, oaidMd5, gaid, idfaMd5 string) (string, error) {
	// 优先oaidmd5
	if oaidMd5 != "" && len(oaidMd5) == 32 {
		return fmt.Sprintf("%s:%s::", eventType, oaidMd5), nil
	}
	if gaid != "" {
		return fmt.Sprintf("%s::%s:", eventType, gaid), nil
	}
	if idfaMd5 != "" && len(idfaMd5) == 32 {
		return fmt.Sprintf("%s:::%s", eventType, idfaMd5), nil
	}
	return "", errors.New("invalid device id")
}

func (fp *FrequencyProcessor) getFrequencyData(eventType, oaidMd5, gaid, idfaMd5 string) (map[string]int, error) {
	key, err := fp.buildEventKey(eventType, oaidMd5, gaid, idfaMd5)
	if err != nil {
		return nil, err
	}

	data, err := fp.redisPool.GetClient().HGetAll(context.Background(), key).Result()
	if err != nil {
		return nil, err
	}
	res := make(map[string]int, 0)
	for k, v := range data {
		freq, err := strconv.Atoi(v)
		if err != nil {
			zaplog.Logger.Warn("FrequencyProcessor getFrequencyData Atoi failed", zap.String("key", k), zap.String("value", v), zap.Error(err))
			continue
		}
		res[k] = freq
	}
	return res, nil
}

func (fp *FrequencyProcessor) parseConfig(line string) (*FrequencyConfig, error) {
	c := &FrequencyConfig{}
	err := json.Unmarshal([]byte(line), c)
	return c, err
}

func (fp *FrequencyProcessor) buildFCKey() string {
	return fmt.Sprintf("%s_%d", conf.GlobalConfig.FrequencyConfig.FrequencyKey, rand.New(rand.NewSource(time.Now().UnixNano())).Intn(conf.GlobalConfig.FrequencyConfig.FrequencyKeyCount))
}
