package modules

import (
	"fmt"
	"io/ioutil"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"rtb_model_server/conf"
	"rtb_model_server/internal/zaplog"

	"github.com/fsnotify/fsnotify"
	"go.uber.org/zap"
)

var globalDeviceConfig map[string]*FunnelConfig

var globalCreativeConfig map[int32]*FunnelConfig

type FunnelProcessor struct {
	funnelConfigFilePath string
	watcher              *fsnotify.Watcher
	mu                   sync.RWMutex
	stopCh               chan struct{}
}

type FunnelConfig struct {
	ID      int32  `json:"id"`      // 配置ID
	Type    string `json:"type"`    // 配置类型
	Value   string `json:"value"`   // 配置值
	Expired int64  `json:"expired"` // 过期时间戳
}

func NewFunnelProcessor() (*FunnelProcessor, error) {
	fp := &FunnelProcessor{
		funnelConfigFilePath: conf.GlobalConfig.FunnelConfig.ConfigFilePath,
		stopCh:               make(chan struct{}),
	}

	// 初始化文件监控
	if err := fp.initWatcher(); err != nil {
		return nil, err
	}

	// 初始加载配置
	if err := fp.loadConfig(); err != nil {
		return nil, err
	}

	// 启动监控协程
	go fp.watchConfigFile()

	return fp, nil
}

func (p *FunnelProcessor) DeviceInFunnelConfig(did string) int32 {
	if did == "" {
		return 0
	}
	p.mu.RLock()
	defer p.mu.RUnlock()
	if v, ok := globalDeviceConfig[did]; ok {
		return v.ID
	}
	return 0
}

func (p *FunnelProcessor) GetCreativeFunnelConfig() map[int32]int32 {
	p.mu.RLock()
	defer p.mu.RUnlock()
	data := make(map[int32]int32)
	for _, v := range globalCreativeConfig {
		if v.Type == "creative" {
			id, err := strconv.Atoi(v.Value)
			if err != nil {
				continue
			}
			data[int32(id)] = v.ID
		}
	}
	return data
}

// initWatcher 初始化文件监控器
func (p *FunnelProcessor) initWatcher() error {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return fmt.Errorf("failed to create watcher: %w", err)
	}

	p.watcher = watcher

	// 添加配置文件到监控列表
	err = p.watcher.Add(p.funnelConfigFilePath)
	if err != nil {
		return fmt.Errorf("failed to add file to watcher: %w", err)
	}

	zaplog.Logger.Info("File watcher initialized", zap.String("path", p.funnelConfigFilePath))
	return nil
}

// watchConfigFile 监控配置文件变化
func (p *FunnelProcessor) watchConfigFile() {
	if p.watcher == nil {
		log.Printf("Watcher is not initialized")
		return
	}

	for {
		select {
		case event, ok := <-p.watcher.Events:
			if !ok {
				return
			}
			// 监听写入和创建事件
			if event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create {
				zaplog.Logger.Info("Config file modified", zap.String("path", p.funnelConfigFilePath))

				// 延迟一点时间再加载，避免文件正在写入时读取
				time.Sleep(1000 * time.Millisecond)
				if err := p.loadConfig(); err != nil {
					zaplog.Logger.Error("Failed to reload config", zap.Error(err))
				} else {
					zaplog.Logger.Info("device config reloaded successfully", zap.String("path", p.funnelConfigFilePath))
				}
			}
		case err, ok := <-p.watcher.Errors:
			if !ok {
				return
			}
			log.Printf("watcher error: %v", err)
		case <-p.stopCh:
			log.Printf("sopping config file watcher")
			return
		}
	}
}

// loadConfig 加载配置文件并分类
func (p *FunnelProcessor) loadConfig() error {
	// 读取配置文件
	data, err := ioutil.ReadFile(p.funnelConfigFilePath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	// 按行解析配置文件
	lines := strings.Split(string(data), "\n")

	// 创建新的配置映射
	newDeviceConfig := make(map[string]*FunnelConfig)
	newCreativeConfig := make(map[int32]*FunnelConfig)

	// 分类处理配置
	for _, line := range lines {
		line = strings.TrimSpace(line)
		// 跳过空行和标题行
		if line == "" || strings.HasPrefix(line, "id") {
			continue
		}

		funnelConfig, err := p.parseConfigLine(line)
		if err != nil {
			zaplog.Logger.Warn("Failed to parse config line ", zap.String("line", line), zap.Error(err))
			continue
		}

		// 根据类型分类存储
		switch funnelConfig.Type {
		case "did", "device":
			newDeviceConfig[funnelConfig.Value] = funnelConfig
		case "creative":
			// 将Value转换为int32
			if creativeId, err := strconv.ParseInt(funnelConfig.Value, 10, 32); err == nil {
				newCreativeConfig[int32(creativeId)] = funnelConfig
			} else {
				zaplog.Logger.Warn("Invalid creative ID", zap.String("id", funnelConfig.Value))
			}
		default:
			zaplog.Logger.Warn("Unknown config type", zap.String("type", funnelConfig.Type))
		}
	}

	// 更新全局配置（加锁保证线程安全）
	p.mu.Lock()
	globalDeviceConfig = newDeviceConfig
	globalCreativeConfig = newCreativeConfig
	p.mu.Unlock()

	zaplog.Logger.Info("Loaded configs",
		zap.Int("device", len(newDeviceConfig)),
		zap.Int("creative", len(newCreativeConfig)))

	return nil
}

// parseConfigLine 解析单行配置
func (p *FunnelProcessor) parseConfigLine(line string) (*FunnelConfig, error) {
	// 按空格分割字段
	fields := strings.Fields(line)
	if len(fields) != 4 {
		return nil, fmt.Errorf("invalid config line format, expected 4 fields but got %d", len(fields))
	}

	config := &FunnelConfig{}

	// 解析ID
	if id, err := strconv.ParseInt(fields[0], 10, 32); err == nil {
		config.ID = int32(id)
	} else {
		return nil, fmt.Errorf("invalid id field: %s", fields[0])
	}

	// 解析Type
	config.Type = fields[1]

	// 解析Value
	config.Value = fields[2]

	// 解析Expired
	if expired, err := strconv.ParseInt(fields[3], 10, 64); err == nil {
		config.Expired = expired
	} else {
		return nil, fmt.Errorf("invalid expired field: %s", fields[3])
	}

	// 检查是否过期
	currentTime := time.Now().Unix()
	if config.Expired < currentTime {
		zaplog.Logger.Warn("Config item has expired",
			zap.Int32("id", config.ID),
			zap.String("type", config.Type),
			zap.String("value", config.Value),
			zap.Int64("expired", config.Expired),
			zap.Int64("current_time", currentTime))
		return nil, fmt.Errorf("config item has expired")
	}

	return config, nil
}

// Stop 停止文件监控
func (p *FunnelProcessor) Stop() error {
	if p.watcher != nil {
		close(p.stopCh)
		return p.watcher.Close()
	}
	return nil
}
