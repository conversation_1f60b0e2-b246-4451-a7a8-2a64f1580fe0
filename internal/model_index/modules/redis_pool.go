package modules

import (
	"context"
	"rtb_model_server/conf"
	"rtb_model_server/internal/zaplog"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

type RedisPool struct {
	redisClient *redis.Client
	redisName   string
}

var redisPools = make(map[string]*RedisPool)

func GetRedisPool(name string) *RedisPool {
	return redisPools[name]
}

func NewRedisPool(redisConfig *conf.RedisPool, name string) (*RedisPool, error) {
	rp := &RedisPool{
		redisName: name,
	}
	opt := &redis.Options{
		Addr:      redisConfig.Addr,
		Password:  redisConfig.AuthInfo,
		DB:        redisConfig.Db,
		OnConnect: rp.OnConnect,
	}
	if redisConfig.DialTimeout > 0 {
		opt.DialTimeout = time.Duration(redisConfig.DialTimeout) * time.Millisecond
	}
	if redisConfig.ReadTimeout > 0 {
		opt.ReadTimeout = time.Duration(redisConfig.ReadTimeout) * time.Millisecond
	}
	if redisConfig.WriteTimeout > 0 {
		opt.WriteTimeout = time.Duration(redisConfig.WriteTimeout) * time.Millisecond
	}

	rp.redisClient = redis.NewClient(opt)
	redisPools[name] = rp
	return rp, rp.redisClient.Ping(context.Background()).Err()
}

func (p *RedisPool) GetClient() *redis.Client {
	return p.redisClient
}

func (p *RedisPool) OnConnect(ctx context.Context, cn *redis.Conn) error {
	zaplog.Logger.Debug("redis pool onconnect event", zap.String("name", p.redisName))
	return nil
}
