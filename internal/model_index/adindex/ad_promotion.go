package adindex

import (
	"fmt"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/internal/zaplog"

	"go.uber.org/zap"
)

// PromotionFilter 默认的Promotion过滤器
func (i *AdIndexManager) PromotionFilter(promotion IndexablePromotion) bool {
	// 示例过滤逻辑：过滤掉状态异常的promotion
	if promotion.RTBPromotion == nil {
		zaplog.Logger.Warn("promotion is nil, filtering out", zap.Int32("id", promotion.GetId()))
		return false
	}

	// 可以添加更多过滤条件，例如：
	// - 过滤掉已过期的promotion
	// - 过滤掉预算不足的promotion
	// - 过滤掉目标受众无效的promotion

	return true
}

// LoadPromotion 加载Promotion数据，使用默认过滤器
func (i *AdIndexManager) LoadPromotion() (int, error) {
	return i.LoadPromotionWithFilter(i.PromotionFilter)
}

// LoadPromotionWithFilter 加载Promotion数据，使用自定义过滤器
func (i *AdIndexManager) LoadPromotionWithFilter(filter FilterFunc[IndexablePromotion]) (int, error) {
	versionedPath := i.getVersionedFilePath(i.config.AdIndexPath.AdPromotion)
	return LoadAdIndexData(versionedPath, "promotion", func() IndexablePromotion {
		return IndexablePromotion{RTBPromotion: &rtb_adinfo_types.RTBPromotion{}}
	}, filter)
}

func (i *AdIndexManager) GetPromotion(promotionId int32) (*rtb_adinfo_types.RTBPromotion, error) {
	if data, ok := dictAdPromotion.Load(promotionId); ok {
		return data.(IndexablePromotion).RTBPromotion, nil
	}
	return nil, fmt.Errorf("promotion %d not found in index dict", promotionId)
}
