package adindex

import (
	"fmt"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/internal/zaplog"

	"go.uber.org/zap"
)

// SponsorFilter 默认的Sponsor过滤器
func (i *AdIndexManager) SponsorFilter(sponsor IndexableSponsor) bool {
	// 示例过滤逻辑：过滤掉状态异常的sponsor
	if sponsor.RTBSponsor == nil {
		zaplog.Logger.Warn("sponsor is nil, filtering out", zap.Int32("id", sponsor.GetId()))
		return false
	}

	// 可以添加更多过滤条件，例如：
	// - 过滤掉已禁用的sponsor
	// - 过滤掉信用不良的sponsor
	// - 过滤掉资质过期的sponsor

	return true
}

// LoadSponsor 加载Sponsor数据，使用默认过滤器
func (i *AdIndexManager) LoadSponsor() (int, error) {
	return i.LoadSponsorWithFilter(i.SponsorFilter)
}

// LoadSponsorWithFilter 加载Sponsor数据，使用自定义过滤器
func (i *AdIndexManager) LoadSponsorWithFilter(filter FilterFunc[IndexableSponsor]) (int, error) {
	versionedPath := i.getVersionedFilePath(i.config.AdIndexPath.AdSponsor)
	return LoadAdIndexData(versionedPath, "sponsor", func() IndexableSponsor {
		return IndexableSponsor{RTBSponsor: &rtb_adinfo_types.RTBSponsor{}}
	}, filter)
}

func (i *AdIndexManager) GetSponsor(sponsorId int32) (*rtb_adinfo_types.RTBSponsor, error) {
	if data, ok := dictAdSponsor.Load(sponsorId); ok {
		return data.(IndexableSponsor).RTBSponsor, nil
	}
	return nil, fmt.Errorf("sponsor %d not found in index dict", sponsorId)
}

func (i *AdIndexManager) GetAllSponsor() ([]*rtb_adinfo_types.RTBSponsor, error) {
	var sponsors []*rtb_adinfo_types.RTBSponsor
	dictAdSponsor.Range(func(key, value any) bool {
		sponsors = append(sponsors, value.(IndexableSponsor).RTBSponsor)
		return true
	})
	return sponsors, nil
}
