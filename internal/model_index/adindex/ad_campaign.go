package adindex

import (
	"fmt"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/internal/zaplog"

	"go.uber.org/zap"
)

// CampaignFilter 默认的Campaign过滤器
func (i *AdIndexManager) CampaignFilter(campaign IndexableCampaign) bool {
	// 检查campaign是否为空
	if campaign.RTBCampaign == nil {
		zaplog.Logger.Warn("campaign is nil, filtering out", zap.Int32("id", campaign.GetId()))
		return false
	}

	// 检查campaign ID是否有效
	if campaign.Id <= 0 {
		zaplog.Logger.Warn("campaign_id is filtered by campaign_id is less than zero",
			zap.Int32("campaign_id", campaign.Id))
		return false
	}

	// 检查sponsor_id是否有效
	if campaign.SponsorId <= 0 {
		zaplog.Logger.Warn("campaign_id is filtered by sponsor_id is less than zero",
			zap.Int32("campaign_id", campaign.Id),
			zap.Int32("sponsor_id", campaign.SponsorId))
		return false
	}

	// 检查cost_type是否有效
	if int32(campaign.CostType) <= 0 {
		zaplog.Logger.Warn("campaign_id is filtered by cost_type is less than zero",
			zap.Int32("campaign_id", campaign.Id),
			zap.Int32("cost_type", int32(campaign.CostType)))
		return false
	}

	// 检查bid_type是否有效
	if int32(campaign.BidType) <= 0 {
		zaplog.Logger.Warn("campaign_id is filtered by bid_type is less than zero",
			zap.Int32("campaign_id", campaign.Id),
			zap.Int32("bid_type", int32(campaign.BidType)))
		return false
	}
	// 存储预算
	dictCampaignBudget.Store(campaign.Id, campaign.ActualDailyBudget)
	// 检查actual_daily_budget是否有效
	if campaign.ActualDailyBudget <= 0 {
		zaplog.Logger.Warn("campaign_id is filtered by daily_budget allocate_type is less than zero",
			zap.Int32("campaign_id", campaign.Id),
			zap.Int64("daily_budget", campaign.ActualDailyBudget),
			zap.Int32("allocate_type", int32(campaign.AllocateType)))
		return false
	}

	// 检查sponsor是否存在
	sponsor, err := i.GetSponsor(campaign.SponsorId)
	if err != nil {
		zaplog.Logger.Warn("campaign_id sponsor_id is filtered by sponsor",
			zap.Int32("campaign_id", campaign.Id),
			zap.Int32("sponsor_id", campaign.SponsorId))
		return false
	}

	// 检查sponsor的exchange_audit_status
	if !i.checkExchangeAuditStatus(sponsor) {
		zaplog.Logger.Warn("campaign_id sponsor_id is filtered by exchange_audit_status",
			zap.Int32("campaign_id", campaign.Id),
			zap.Int32("sponsor_id", campaign.SponsorId))
		return false
	}

	// 检查各个层级的状态
	if campaign.PauseStatus != 0 || campaign.Status != 0 || campaign.SysPauseStatus != 0 {
		return false
	}

	return true
}
func (i *AdIndexManager) GetCampaignBudget(campaignId int32) (int64, bool) {
	if budget, ok := dictCampaignBudget.Load(campaignId); ok {
		return budget.(int64), true
	}
	return 0, false
}

// LoadCampaign 加载Campaign数据，使用默认过滤器
func (i *AdIndexManager) LoadCampaign() (int, error) {
	return i.LoadCampaignWithFilter(i.CampaignFilter)
}

// LoadCampaignWithFilter 加载Campaign数据，使用自定义过滤器
func (i *AdIndexManager) LoadCampaignWithFilter(filter FilterFunc[IndexableCampaign]) (int, error) {
	versionedPath := i.getVersionedFilePath(i.config.AdIndexPath.AdCampaign)
	return LoadAdIndexData(versionedPath, "campaign", func() IndexableCampaign {
		return IndexableCampaign{RTBCampaign: &rtb_adinfo_types.RTBCampaign{}}
	}, filter)
}

// checkExchangeAuditStatus 检查sponsor的exchange_audit_status
func (i *AdIndexManager) checkExchangeAuditStatus(sponsor *rtb_adinfo_types.RTBSponsor) bool {
	// 如果没有ExchangeAuditStatus信息，默认通过
	if sponsor.ExchangeAuditStatus == nil || len(sponsor.ExchangeAuditStatus) == 0 {
		return true
	}

	// 检查是否有任何exchange的审核状态通过
	// 这里可以根据具体业务逻辑调整，比如检查特定exchange的状态
	for _, auditInfo := range sponsor.ExchangeAuditStatus {
		if auditInfo != nil {
			// 这里可以根据ExchangeAuditInfo的具体字段来判断审核状态
			// 暂时返回true，具体逻辑需要根据ExchangeAuditInfo的结构来实现
			return true
		}
	}

	return true
}

func (i *AdIndexManager) GetCampaign(campaignId int32) (*rtb_adinfo_types.RTBCampaign, error) {
	if data, ok := dictAdCampaign.Load(campaignId); ok {
		return data.(IndexableCampaign).RTBCampaign, nil
	}
	return nil, fmt.Errorf("campaign %d not found in index dict", campaignId)
}
