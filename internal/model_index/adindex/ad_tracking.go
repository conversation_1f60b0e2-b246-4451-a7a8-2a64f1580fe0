package adindex

import (
	"fmt"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/internal/zaplog"

	"go.uber.org/zap"
)

// AdTrackingFilter 默认的AdTracking过滤器
func (i *AdIndexManager) AdTrackingFilter(tracking IndexableAdTracking) bool {
	// 示例过滤逻辑：过滤掉状态异常的tracking
	if tracking.AdTrackingInfo == nil {
		zaplog.Logger.Warn("ad tracking is nil, filtering out", zap.Int32("id", tracking.GetId()))
		return false
	}

	// 可以添加更多过滤条件，例如：
	// - 过滤掉无效的tracking URL
	// - 过滤掉已禁用的tracking配置
	// - 过滤掉过期的tracking信息

	return true
}

// LoadAdTracking 加载AdTracking数据，使用默认过滤器
func (i *AdIndexManager) LoadAdTracking() (int, error) {
	return i.LoadAdTrackingWithFilter(i.AdTrackingFilter)
}

// LoadAdTrackingWithFilter 加载AdTracking数据，使用自定义过滤器
func (i *AdIndexManager) LoadAdTrackingWithFilter(filter FilterFunc[IndexableAdTracking]) (int, error) {
	versionedPath := i.getVersionedFilePath(i.config.AdIndexPath.AdTracking)
	return LoadAdIndexData(versionedPath, "ad_tracking", func() IndexableAdTracking {
		return IndexableAdTracking{AdTrackingInfo: &rtb_adinfo_types.AdTrackingInfo{}}
	}, filter)
}

func (i *AdIndexManager) GetAdTracking(trackingId int32) (*rtb_adinfo_types.AdTrackingInfo, error) {
	if data, ok := dictAdTracking.Load(trackingId); ok {
		return data.(IndexableAdTracking).AdTrackingInfo, nil
	}
	return nil, fmt.Errorf("ad tracking %d not found in index dict", trackingId)
}
