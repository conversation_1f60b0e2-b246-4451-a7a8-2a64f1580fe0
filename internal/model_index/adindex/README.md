# AdIndex 索引更新架构

## 概述

AdIndex 是 RTB 模型服务器的核心广告索引管理模块，负责管理和维护广告相关的各种索引数据，包括广告主、活动、策略、创意、推广和追踪等信息。该模块采用基于版本控制的热更新机制，确保广告数据的实时性和一致性。

## 架构设计

### 核心组件

#### 1. AdIndexManager (ad_index_manage.go)
- **职责**: 索引管理器的核心控制器
- **功能**: 
  - 版本管理和文件监控
  - 数据加载和热更新
  - 索引状态管理
  - 文件过期检测

#### 2. 索引数据类型
- **AdCampaign** (ad_campaign.go): 广告活动索引
- **AdStrategy** (ad_strategy.go): 广告策略索引
- **AdCreative** (ad_creative.go): 广告创意索引
- **AdSponsor** (ad_sponsor.go): 广告主索引
- **AdPromotion** (ad_promotion.go): 推广信息索引
- **AdTracking** (ad_tracking.go): 追踪信息索引

### 数据存储结构

```go
// 全局索引存储 (使用 sync.Map 保证并发安全)
var (
    dictAdCampaign  = sync.Map{}  // 活动索引
    dictAdStrategy  = sync.Map{}  // 策略索引
    dictAdCreative  = sync.Map{}  // 创意索引
    dictAdSponsor   = sync.Map{}  // 广告主索引
    dictAdPromotion = sync.Map{}  // 推广索引
    dictAdTracking  = sync.Map{}  // 追踪索引
    dictResourceUsed = sync.Map{} // 资源包缓存
    dictCampaignBudget = sync.Map{} // 活动预算
    dictStrategyBudget = sync.Map{} // 策略预算
)
```

## 版本控制机制

### 版本文件格式
- **VERSION文件**: 包含时间戳格式的版本号 (如: `20250722_144206`)
- **数据文件**: 基础文件名 + 版本号后缀 (如: `ad_campaign.data.20250722_144206`)

### 版本更新流程
1. **文件监控**: 使用 `fsnotify` 监控 VERSION 文件变化
2. **版本验证**: 检查所有相关数据文件是否存在
3. **原子更新**: 确保所有索引数据同时更新
4. **状态管理**: 跟踪加载成功/失败状态

## 数据加载流程

### 1. 启动时加载
```go
func (m *ModelIndexManager) Start() error {
    // 1. 加载最新的广告索引数据
    err := m.adIndex.LoadAdIndexData()
    // 2. 启动文件监控
    err = m.adIndex.StartFileWatcher()
    // 3. 初始化其他组件...
}
```

### 2. 热更新流程
```
VERSION文件变化 → 文件监控器检测 → 触发重新加载 → 原子更新所有索引
```

### 3. 数据过滤机制
每种索引类型都有对应的过滤器，确保只加载有效数据：

- **Campaign过滤**: 检查ID、状态、预算、广告主等
- **Strategy过滤**: 检查状态、资源包使用情况
- **Creative过滤**: 检查关联关系、容器信息等
- **其他索引**: 基础有效性检查

## 文件过期检测

### 过期策略
- **时间基准**: 基于VERSION文件中的时间戳
- **过期时间**: 可配置，默认10分钟 (`AdIndexFileExpireMinutes`)
- **检测时机**: 每次请求处理前检查

### 过期处理
```go
func (i *AdIndexManager) GetAdIndexFileExpiredOrMissing() bool {
    // 1. 检查上次加载是否成功
    // 2. 验证VERSION文件存在性
    // 3. 检查所有数据文件完整性
    // 4. 计算时间差判断是否过期
}
```

## 并发安全设计

### 读写锁机制
- **sync.RWMutex**: 保护版本号和状态信息
- **sync.Map**: 索引数据的并发安全存储
- **原子操作**: 确保数据更新的一致性

### 无锁读取
- 索引查询操作无需加锁
- 利用 sync.Map 的并发特性
- 最小化锁竞争影响

## 性能优化

### 1. 增量更新
```go
// 记录旧数据，只更新变化的部分
oldEventMap := make(map[int32]bool)
indexMap.Range(func(key, value interface{}) bool {
    oldEventMap[indexable.GetId()] = false
    return true
})
```

### 2. 资源包优化
- 只缓存实际使用的资源包ID
- 避免加载全量资源包数据
- 减少内存占用

### 3. 批量处理
- 使用泛型函数 `LoadAdIndexData[T]` 统一处理逻辑
- 减少代码重复，提高维护性

## 监控和日志

### 关键指标
- 加载成功/失败状态
- 各类型索引数量统计
- 加载耗时监控
- 过滤数据统计

### 日志记录
```go
zaplog.Logger.Info("load ad index success",
    zap.String("version", i.currentVersion),
    zap.Int("campaignCount", campaignCount),
    zap.Int("strategyCount", strategyCount),
    // ... 其他统计信息
)
```

## 配置说明

### 文件路径配置
```yaml
ModelIndex:
  AdIndexPath:
    AdCampaign: data/adindex/ad_campaign.data
    AdStrategy: data/adindex/ad_strategy.data
    AdCreative: data/adindex/ad_creative.data
    AdSponsor: data/adindex/ad_sponsor.data
    AdPromotion: data/adindex/ad_promotion.data
    AdTracking: data/adindex/ad_tracking.data
  AdIndexFileExpireMinutes: 100  # 文件过期时间（分钟）
```

## 错误处理

### 容错机制
1. **文件缺失**: 返回错误，拒绝服务
2. **版本解析失败**: 标记为过期状态
3. **加载失败**: 保持上一次成功状态
4. **部分数据异常**: 通过过滤器排除

### 降级策略
- 文件过期时拒绝新请求
- 保持服务可用性优先
- 详细错误日志记录

## 使用示例

### 获取索引数据
```go
// 获取活动信息
campaign, err := adIndex.GetCampaign(campaignId)

// 获取创意信息
creative, err := adIndex.GetCreative(creativeId)

// 检查预算
budget, exists := adIndex.GetCampaignBudget(campaignId)
```

### 自定义过滤器
```go
// 使用自定义过滤器加载数据
count, err := adIndex.LoadCampaignWithFilter(func(campaign IndexableCampaign) bool {
    // 自定义过滤逻辑
    return campaign.Status == 0
})
```

## 总结

AdIndex 索引更新架构通过版本控制、热更新、并发安全等机制，为 RTB 系统提供了高性能、高可用的广告索引服务。其设计充分考虑了实时性、一致性和性能要求，是整个 RTB 模型服务器的重要基础组件。