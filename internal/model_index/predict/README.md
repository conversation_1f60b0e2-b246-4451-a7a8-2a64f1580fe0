# BidDecision 出价决策器

## 概述

本模块包含 RTB 模型服务器中的预测处理组件：

- `PredictProcessor`: 负责与预测服务通信，使用 Thrift 连接池管理连接
- `BidDecision`: 核心出价决策组件，负责根据预测结果和策略配置计算最优出价，支持多种出价类型（CPM、CPC、CPA）之间的智能转换

## PredictProcessor 连接池实现

### 功能特性

PredictProcessor 现在使用 Thrift 连接池来管理与预测服务的连接，提高性能和资源利用率：

- 自动创建和管理 Thrift 连接
- 连接复用，减少连接建立开销
- 连接健康检查和自动重连
- 线程安全的连接获取和释放

### 配置参数

在 `conf/rtb_model_server.yaml` 中的 `PredictServer` 部分配置连接池参数：

```yaml
PredictServer:
  Addr: ":3399"                # 预测服务地址
  ModelName: "default_model"    # 模型名称
  PoolSize: 10                  # 连接池最大连接数
  MaxIdleConns: 5               # 最大空闲连接数
  ConnTimeout: 5000             # 连接超时时间(毫秒)
```

#### 参数说明

- **PoolSize**: 连接池中最大连接数，默认值为 10
- **MaxIdleConns**: 初始化时创建的空闲连接数，也是保持的最大空闲连接数，默认值为 5
- **ConnTimeout**: 单个连接的超时时间，单位为毫秒，默认值为 5000ms

### PredictProcessor API

```go
// 创建 PredictProcessor 实例
func NewPredictProcessor() *PredictProcessor

// 执行预测请求
func (p *PredictProcessor) Predict(requestCtx *ctx.RequestContext, creativeIds []int32) (map[int32]PredictResult, error)

// 关闭连接池
func (p *PredictProcessor) Close()

// 获取连接池统计信息
func (p *PredictProcessor) GetPoolStats() (total, inUse, idle int)
```

### 使用示例

```go
// 创建处理器
processor := NewPredictProcessor()
defer processor.Close()

// 执行预测
results, err := processor.Predict(requestCtx, creativeIds)
if err != nil {
    log.Printf("prediction failed: %v", err)
    return
}

// 查看连接池状态
total, inUse, idle := processor.GetPoolStats()
log.Printf("Connection pool stats - Total: %d, InUse: %d, Idle: %d", total, inUse, idle)
```

## 核心结构

### BidDecision

```go
type BidDecision struct {
    // 出价决策器
}
```

出价决策器的主要职责：
- 处理多个创意的出价计算
- 选择出价最高的结果
- 支持不同出价类型之间的转换
- 验证策略与媒体出价类型的兼容性

### BidResult

```go
type BidResult struct {
    CreativeId         int32                    // 创意ID
    MatchedAdMatchType int64                    // 匹配上的广告位ID
    BidPrice           int64                    // 盟元(百万分之一元)
    BidType            rtb_adinfo_types.BidType // 1系统优化出价,2人工出价
    CostType           enums.CostType           // 成本类型
    MediaBidType       int                      // 1表示cpm，2表示cpc，4表示cpa, 默认1，0情况同1为cpm
    MediaCostType      int                      // 由UI根据RTBStrategy.media_cost_type填充,默认值0表示by cpm，1表示by cpc
}
```

### PredictResult

```go
type PredictResult struct {
    CreativeId int32   // 创意ID
    Score      float32 // 综合得分
    PreCtr     float32 // 预估点击率
    PreCvr     float32 // 预估转化率
    Ctr        float32 // 历史点击率
    Cvr        float32 // 历史转化率
}
```

## 主要方法

### Decision

```go
func (b *BidDecision) Decision(requestCtx *ctx.RequestContext, predictResults map[int32]PredictResult) (*BidResult, error)
```

**功能**：主要决策方法，从所有可能的出价中选择最高出价

**流程**：
1. 遍历所有预测结果
2. 为每个创意调用 `fillingBid` 计算出价
3. 收集所有有效出价结果
4. 返回出价最高的结果

**错误处理**：
- 如果某个创意出价失败，继续处理其他创意
- 如果没有任何有效出价，返回错误

### fillingBid

```go
func (b *BidDecision) fillingBid(requestCtx *ctx.RequestContext, predictResult *PredictResult) ([]*BidResult, error)
```

**功能**：为单个创意计算出价结果

**流程**：
1. 获取创意和策略信息
2. 匹配广告位类型
3. 计算出价价格
4. 设置媒体出价类型和成本类型
5. 返回出价结果列表

### getBidPrice

```go
func (b *BidDecision) getBidPrice(strategy *rtb_adinfo_types.RTBStrategy, req *rtb_types.RTBRequestInfo, predictResult *PredictResult) int64
```

**功能**：核心出价计算方法

**流程**：
1. 检查策略与媒体出价类型的兼容性
2. 根据媒体出价类型调用相应的计算方法
3. 返回最终出价（盟元单位）

## 出价类型支持

### 媒体出价类型（MediaBidType）

- `1`: CPM（千次展示成本）
- `2`: CPC（每次点击成本）
- `4`: CPA（每次行动成本）
- `0`: 默认为 CPM

### 策略成本类型（CostType）

- `CT_CPM`: CPM 策略
- `CT_CPC`: CPC 策略
- `CT_CPA`: CPA 策略

### 兼容性检查

策略的 `media_bid_type` 使用位运算方式：
- `1`: 支持 CPM
- `2`: 支持 CPC
- `4`: 支持 CPA
- `3` (1+2): 支持 CPM、CPC
- `5` (1+4): 支持 CPM、CPA
- `6` (2+4): 支持 CPC、CPA
- `7` (1+2+4): 支持全部类型

## 出价计算算法

### CPM 出价计算

| 策略类型 | 计算公式 | 说明 |
|---------|---------|------|
| CPM | `basePrice` | 直接返回基础价格 |
| CPC | `basePrice × PreCtr` | 基础价格乘以预估点击率 |
| CPA | `basePrice × PreCtr × PreCvr` | 基础价格乘以预估点击率和转化率 |

### CPC 出价计算

| 策略类型 | 计算公式 | 说明 |
|---------|---------|------|
| CPM | `basePrice ÷ PreCtr` | 基础价格除以预估点击率 |
| CPC | `basePrice` | 直接返回基础价格 |
| CPA | `basePrice × PreCvr` | 基础价格乘以预估转化率 |

### CPA 出价计算

| 策略类型 | 计算公式 | 说明 |
|---------|---------|------|
| CPM | `basePrice ÷ (PreCtr × PreCvr)` | 基础价格除以预估点击率和转化率的乘积 |
| CPC | `basePrice ÷ PreCvr` | 基础价格除以预估转化率 |
| CPA | `basePrice` | 直接返回基础价格 |

## 安全检查

1. **预估值验证**：确保 `PreCtr` 和 `PreCvr` 大于 0
2. **基础价格验证**：确保 `basePrice` 大于 0
3. **兼容性验证**：检查策略是否支持媒体要求的出价类型
4. **除零保护**：在除法运算前检查分母是否为 0

## 使用示例

```go
// 创建出价决策器
bidDecision := &BidDecision{}

// 执行出价决策
result, err := bidDecision.Decision(requestCtx, predictResults)
if err != nil {
    // 处理错误
    return nil, err
}

// 使用出价结果
fmt.Printf("最高出价: %d 盟元, 创意ID: %d\n", result.BidPrice, result.CreativeId)
```

## 注意事项

1. **货币单位**：所有价格以盟元（百万分之一元）为单位
2. **精度处理**：浮点数计算后转换为 int64，可能存在精度损失
3. **性能考虑**：目前仅支持单个广告位匹配，找到第一个匹配即跳出
4. **错误容忍**：单个创意出价失败不会影响其他创意的处理

## 依赖关系

- `rtb_adinfo_types`: RTB 广告信息类型定义
- `rtb_types`: RTB 请求类型定义
- `enums`: 枚举类型定义
- `context`: 请求上下文
- `lib`: 工具库函数

## 扩展性

该模块设计具有良好的扩展性：
- 可以轻松添加新的出价类型
- 支持新的成本类型
- 可以扩展出价计算算法
- 支持更复杂的兼容性检查逻辑