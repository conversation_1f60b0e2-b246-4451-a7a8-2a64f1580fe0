package model_index

import (
	"math/rand"
	"rtb_model_server/common/domob_thrift/rtb_model_server"
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/conf"
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/lib"
	"rtb_model_server/internal/model_index/adindex"
	"rtb_model_server/internal/model_index/budget"
	"rtb_model_server/internal/model_index/filters"
	"rtb_model_server/internal/model_index/modules"
	"rtb_model_server/internal/model_index/predict"
	"rtb_model_server/internal/zaplog"
	"time"

	"github.com/pkg/errors"
)

type ModelIndexManager struct {
	config        *conf.ModelIndexConfig
	adIndex       *adindex.AdIndexManager
	predict       *predict.PredictProcessor
	budgetControl *budget.BudgetProcessor
	decision      *predict.BidDecision
	render        *AdsRender

	deviceDmpTargetRedisPool *modules.RedisPool
	frequencyRedisPool       *modules.RedisPool
	resourceTargetProcessor  *modules.ResourceTargetProcessor
	frequencyProcessor       *modules.FrequencyProcessor
	funnelProcessor          *modules.FunnelProcessor
}

func NewModelIndexManager() *ModelIndexManager {
	return &ModelIndexManager{
		config:        &conf.GlobalConfig.ModelIndex,
		adIndex:       adindex.NewAdIndex(),
		predict:       predict.NewPredictProcessor(),
		decision:      predict.NewBidDecision(),
		budgetControl: budget.GetBudgetProcessor(),
		render:        NewAdsRender(),
	}
}

func (m *ModelIndexManager) Start() error {
	// 加载最新的广告索引数据
	err := m.adIndex.LoadAdIndexData()
	if err != nil {
		return errors.Wrap(err, "failed to load ad index data")
	}
	// 启动广告索引文件监控
	if err = m.adIndex.StartFileWatcher(); err != nil {
		return errors.Wrap(err, "failed to start file watcher")
	}

	// 初始化Redis链接池
	if err = m.initRedisPool(); err != nil {
		return errors.Wrap(err, "failed to init redis pool")
	}
	//初始化过滤器定向处理器
	if err = m.initFilterProcessor(); err != nil {
		return errors.Wrap(err, "failed to init filter processor")
	}

	// 初始化漏斗处理器
	if err = m.initFunnelProcessor(); err != nil {
		return errors.Wrap(err, "failed to init funnel processor")
	}

	return err
}

func (m *ModelIndexManager) PreFilter(requestCtx *ctx.RequestContext) []int32 {
	t1 := time.Now()
	defer func() {
		requestCtx.AddTimeCost("filter", time.Since(t1).Microseconds())
	}()

	// 检查文件是否过期
	if m.adIndex.GetAdIndexFileExpiredOrMissing() {
		zaplog.Logger.Warn("index files are expired, rejecting request")
		// 返回空的创意列表，表示文件过期
		requestCtx.RecallCreatives = []int32{}
		return []int32{}
	}

	// 执行广告索引预过滤，获取到所有通过的
	filtersProcessor := filters.NewFilterProcessor()
	filtersProcessor.SetupDefaultFilters()
	filterResult := filtersProcessor.ProcessAllAds(requestCtx)
	recallCreative := []int32{}
	for _, r := range filterResult {
		if r.FilterType == filters.FilterTypeNone {
			recallCreative = append(recallCreative, r.Cid)
		} else {
			// 关闭一些比如admatchtype过滤原因的日志，避免无用日志量太多
			if filters.FunnelLogWithFilterType(r.FilterType) {
				requestCtx.CreativeFilterMap[r.Cid] = int32(r.FilterType)
			}
		}
	}
	requestCtx.RecallCreatives = recallCreative
	return recallCreative
}

// CreateContext 创建广告请求上下文
// 该context生命周期是请求开始到响应返回
func (m *ModelIndexManager) CreateContext(req *rtb_types.RTBBidRequest, resp *rtb_model_server.RTBModelServerResponse) *ctx.RequestContext {
	funnelId := m.funnelProcessor.DeviceInFunnelConfig(lib.GetDidFromDevice(req.Device))
	requestCtx := &ctx.RequestContext{
		TimeCost:          make(map[string]int64),
		TimeStart:         time.Now(),
		BidRequest:        req,
		AdIndex:           m.adIndex,
		BidResponse:       resp,
		CreativeFilterMap: map[int32]int32{},

		// 初始化链接池
		DeviceDmpTargetRedisPool: m.deviceDmpTargetRedisPool,
		FrequencyRedisPool:       m.frequencyRedisPool,
		ResourceTargetProcessor:  m.resourceTargetProcessor,
		FrequencyProcessor:       m.frequencyProcessor,

		EnableFunnelLog: funnelId > 0,
		// 将启动的配置进行加载，开启设备漏斗日志
		EnableDeviceFunnelLogFid: funnelId,
	}
	if rand.Intn(10000) < int(conf.GlobalConfig.FunnelConfig.FunnelOpenRatio) {
		// 这个ID如果是-1表示由随机Log开启的
		requestCtx.EnableDeviceFunnelLogFid = -1
		requestCtx.EnableFunnelLog = true
	}
	return requestCtx
}

func (m *ModelIndexManager) Predict(requestCtx *ctx.RequestContext, budgetControlResult map[int32]*budget.BudgetControlResult) (map[int32]predict.PredictResult, error) {
	t1 := time.Now()
	defer func() {
		requestCtx.AddTimeCost("predict", time.Since(t1).Microseconds())
	}()
	creativeIds := []int32{}
	for creative := range budgetControlResult {
		creativeIds = append(creativeIds, creative)
	}
	if len(creativeIds) > 20 {
		creativeIds = creativeIds[:10]
	}
	return m.predict.Predict(requestCtx, creativeIds)

}

// 预算控制模块
// returns map[creative]ratio errors
func (m *ModelIndexManager) BudgetControl(requestCtx *ctx.RequestContext, preRecallCreative []int32) (map[int32]*budget.BudgetControlResult, error) {
	results, err := m.budgetControl.Process(requestCtx, preRecallCreative)
	if err != nil {
		return nil, err
	}
	newResults := map[int32]*budget.BudgetControlResult{}
	for creative, result := range results {
		if result.Allowed {
			newResults[creative] = result
		}
	}
	return newResults, nil
}

// 决策层
func (m *ModelIndexManager) BidDecision(requestCtx *ctx.RequestContext, predictResults map[int32]predict.PredictResult) (*predict.BidResult, error) {
	t1 := time.Now()
	defer func() {
		requestCtx.AddTimeCost("decision", time.Since(t1).Microseconds())
	}()
	// 执行广告排序
	bidResults, err := m.decision.Sort(requestCtx, predictResults)
	if err != nil {
		return nil, err
	}
	// 对排序的结果进行返回
	return m.decision.Decision(requestCtx, bidResults)
}

func (m *ModelIndexManager) Render(requestCtx *ctx.RequestContext, bidResult *predict.BidResult) (*rtb_model_server.RTBModelServerResponse, error) {
	t1 := time.Now()
	defer func() {
		requestCtx.AddTimeCost("render", time.Since(t1).Microseconds())
	}()
	err := m.render.Render(requestCtx, bidResult)
	return requestCtx.BidResponse, err
}

func (m *ModelIndexManager) initRedisPool() error {
	// 初始化DMP定向Redis
	redisPool1, err := modules.NewRedisPool(&conf.GlobalConfig.DmpTargetRedisPool, "dmp_target_redis")
	if err != nil {
		return errors.Wrap(err, "failed to create dmp target redis pool")
	}
	m.deviceDmpTargetRedisPool = redisPool1
	// 初始化频控的Redis
	redisPool2, err := modules.NewRedisPool(&conf.GlobalConfig.FrequencyRedisPool, "frequency_redis")
	if err != nil {
		return errors.Wrap(err, "failed to create frequency redis pool")
	}
	m.frequencyRedisPool = redisPool2
	return nil
}
func (m *ModelIndexManager) initFilterProcessor() error {
	var err error
	// 初始化资源包过滤器，仅初始化一次
	m.resourceTargetProcessor, err = modules.NewResourceTargetProcessor(m.adIndex.GetAllResourceTarget)
	if err != nil {
		return errors.Wrap(err, "failed to create resource target processor")
	}
	// 初始化频控获取
	m.frequencyProcessor = modules.NewFrequencyProcessor(m.frequencyRedisPool)
	return nil
}
func (m *ModelIndexManager) initFunnelProcessor() error {
	// 初始化频控获取
	var err error
	m.funnelProcessor, err = modules.NewFunnelProcessor()
	return err
}

func (m *ModelIndexManager) Stop() {
	// 关闭文件监控
	m.adIndex.StopFileWatcher()
}
