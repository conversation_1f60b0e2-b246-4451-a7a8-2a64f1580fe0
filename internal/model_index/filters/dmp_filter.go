package filters

import (
	"context"
	"crypto/md5"
	"fmt"
	"strconv"
	"strings"
	"time"

	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/zaplog"

	"go.uber.org/zap"
)

// DMPTargetFilter 人群定向过滤器
type DMPTargetFilter struct {
	*BaseFilter
}

// NewDMPTargetFilter 创建人群定向过滤器
func NewDMPTargetFilter(ctx *ctx.RequestContext) *DMPTargetFilter {
	dmpTargetFilter := &DMPTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
	if ctx.DeviceDmpTarget == nil {
		t1 := time.Now()
		// 获取设备ID（OAID MD5）
		deviceId := dmpTargetFilter.getDeviceId(ctx)
		if deviceId == "" {
			zaplog.Logger.Debug("device id is empty, skip DMP target filter")
		}
		// 获取设备所属的设备组
		ctx.DeviceDmpTarget = dmpTargetFilter.getDeviceGroups(deviceId)
		ctx.AddTimeCost("dmp", time.Since(t1).Microseconds())
		zaplog.Logger.Debug("device dmp target", zap.String("device_id", deviceId), zap.Any("device_groups", ctx.DeviceDmpTarget))
	}
	return dmpTargetFilter
}

// Filter 执行人群定向过滤
func (f *DMPTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	// 获取当前策略
	strategy := ctx.CurrentFilterStrategy
	if strategy == nil {
		zaplog.Logger.Debug("strategy is nil, skip DMP target filter")
		return FilterTypeNone
	}
	// 检查是否配置了设备组定向
	if len(strategy.DeviceGroupWhiteTarget) == 0 && len(strategy.DeviceGroupBlackTarget) == 0 {
		// 没有配置设备组定向，直接通过
		return FilterTypeNone
	}
	deviceGroups := ctx.DeviceDmpTarget
	// 检查黑名单
	if len(strategy.DeviceGroupBlackTarget) > 0 {
		if f.isInBlacklist(deviceGroups, strategy.DeviceGroupBlackTarget, strategy.DeviceGroupBlackRule) {
			zaplog.Logger.Debug("device filtered by black target",
				zap.String("device_id", f.getDeviceId(ctx)),
				zap.Any("device_groups", deviceGroups),
				zap.Any("black_target", strategy.DeviceGroupBlackTarget))
			return FilterTypeDMPTarget
		}
	}

	// 检查白名单
	if len(strategy.DeviceGroupWhiteTarget) > 0 {
		if !f.isInWhitelist(deviceGroups, strategy.DeviceGroupWhiteTarget, strategy.DeviceGroupWhiteRule) {
			zaplog.Logger.Debug("device filtered by white target",
				zap.String("device_id", f.getDeviceId(ctx)),
				zap.Any("device_groups", deviceGroups),
				zap.Any("white_target", strategy.DeviceGroupWhiteTarget))
			return FilterTypeDMPTarget
		}
	}

	return FilterTypeNone
}

// getDeviceId 获取设备ID（OAID MD5）
func (f *DMPTargetFilter) getDeviceId(ctx *ctx.RequestContext) string {
	if ctx.BidRequest == nil || ctx.BidRequest.Device == nil {
		return ""
	}

	// 优先使用OAID
	if ctx.BidRequest.Device.Oaidmd5 != "" {
		return strings.ToUpper(ctx.BidRequest.Device.Oaidmd5)
	}

	// 如果没有OAID，可以考虑使用其他设备标识
	if ctx.BidRequest.Device.Idfamd5 != "" {
		return strings.ToUpper(ctx.BidRequest.Device.Idfamd5)
	}

	if ctx.BidRequest.Device.Gaid != "" {
		hash := md5.Sum([]byte(ctx.BidRequest.Device.Gaid))
		return strings.ToUpper(fmt.Sprintf("%x", hash))
	}

	return ""
}

// getDeviceGroups 从Redis获取设备所属的设备组
func (f *DMPTargetFilter) getDeviceGroups(deviceId string) []int32 {

	// 从Redis查询
	val, err := f.ctx.DeviceDmpTargetRedisPool.GetClient().Get(context.Background(), deviceId).Result()
	if err != nil {
		return []int32{}
	}

	// 解析设备组ID列表（格式："123,456,789"）
	groups := []int32{}
	if val != "" {
		groupStrs := strings.Split(val, ",")
		for _, groupStr := range groupStrs {
			groupStr = strings.TrimSpace(groupStr)
			if groupStr != "" {
				if groupId, err := strconv.ParseInt(groupStr, 10, 32); err == nil {
					groups = append(groups, int32(groupId))
				}
			}
		}
	}
	return groups
}

// isInBlacklist 检查设备是否在黑名单中
func (f *DMPTargetFilter) isInBlacklist(deviceGroups []int32, blackTarget []int32, rule int32) bool {
	if len(blackTarget) == 0 {
		return false
	}

	// 创建黑名单映射
	blackMap := make(map[int32]bool)
	for _, groupId := range blackTarget {
		blackMap[groupId] = true
	}

	// 检查设备组是否在黑名单中
	matchCount := 0
	for _, groupId := range deviceGroups {
		if blackMap[groupId] {
			matchCount++
		}
	}

	if rule == 1 {
		// 交集：设备必须同时属于所有黑名单组才被过滤
		return matchCount == len(blackTarget)
	} else {
		// 并集（默认）：设备属于任一黑名单组就被过滤
		return matchCount > 0
	}
}

// isInWhitelist 检查设备是否在白名单中
func (f *DMPTargetFilter) isInWhitelist(deviceGroups []int32, whiteTarget []int32, rule int32) bool {
	if len(whiteTarget) == 0 {
		return true
	}

	// 创建白名单映射
	whiteMap := make(map[int32]bool)
	for _, groupId := range whiteTarget {
		whiteMap[groupId] = true
	}

	// 检查设备组是否在白名单中
	matchCount := 0
	for _, groupId := range deviceGroups {
		if whiteMap[groupId] {
			matchCount++
		}
	}

	if rule == 1 {
		// 交集：设备必须同时属于所有白名单组才通过
		return matchCount == len(whiteTarget)
	} else {
		// 并集（默认）：设备属于任一白名单组就通过
		return matchCount > 0
	}
}

// ExchangeDmpTargetFilter 交易所人群定向过滤器
type ExchangeDmpTargetFilter struct {
}

// NewExchangeDmpTargetFilter 创建交易所人群定向过滤器
func NewExchangeDmpTargetFilter(ctx *ctx.RequestContext) *ExchangeDmpTargetFilter {
	// 这里需要从配置中获取Redis连接信息
	return &ExchangeDmpTargetFilter{}
}

// Filter 执行交易所人群定向过滤
func (f *ExchangeDmpTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	// FIXME 这里实现有问题，目前推广组里有交易所人群ID无法定向和出价，需要单独实现
	strategy := ctx.CurrentFilterStrategy
	if len(strategy.ExchangeDmpWhiteTarget) == 0 && len(strategy.ExchangeDmpBlackTarget) == 0 {
		return FilterTypeNone
	}

	return FilterTypeExchangeDmpTarget
}
