# RTB 广告过滤器系统

## 概述

这是一个基于工厂模式和策略模式设计的广告过滤器系统，用于在RTB（Real-Time Bidding）场景中对广告进行多维度过滤。

## 设计模式

### 1. 策略模式（Strategy Pattern）
- **Filters接口**: 定义了过滤器的统一接口
- **具体过滤器**: 实现了不同的过滤策略（如频控、定向、预算等）

### 2. 工厂模式（Factory Pattern）
- **FilterFactory接口**: 定义了过滤器创建的接口
- **DefaultFilterFactory**: 根据过滤器类型创建对应的过滤器实例

### 3. 管理器模式（Manager Pattern）
- **FilterManager**: 管理过滤器链的执行顺序和逻辑

## 架构组件

### 核心接口
```go
type Filters interface {
    Filter(ctx *RequestContext) FilterType
}
```

### 过滤器类型
过滤器按照层级分为以下几类：

#### Campaign层级
- `FilterTypeDeliveryTime`: 投放时间过滤
- `FilterTypeBudgetReason`: 预算过滤

#### Strategy层级
- `FilterTypeExchangeTarget`: 交易所ID过滤
- `FilterTypeInstalledApps`: 已安装应用过滤
- `FilterTypeCountryTarget`: 国家定向
- `FilterTypeGenderTarget`: 性别定向
- `FilterTypeAgeTarget`: 年龄定向
- 等等...

#### Creative层级
- `FilterTypeAdMatchTypeTarget`: 广告位匹配类型
- `FilterTypeAbility`: 请求能力要求
- `FilterTypeCreativeDimTarget`: 创意尺寸定向

#### 流量相关
- `FilterTypeAdClkFrequency`: 点击频控
- `FilterTypeAdImpFrequency`: 曝光频控
- `FilterTypeForbiddenSponsor`: RTA过滤

## 使用方法

### 1. 基本使用
```go
// 创建过滤器工厂
factory := NewDefaultFilterFactory()

// 创建过滤器管理器
filterManager := NewFilterManager(factory)

// 添加过滤器
filterManager.AddFilters(
    FilterTypeDeliveryTime,
    FilterTypeBudgetReason,
    FilterTypeCountryTarget,
)

// 执行过滤
filteredAds := filterManager.FilterAds(requestCtx, ads)
```

### 2. 使用默认过滤器链
```go
defaultFilters := GetDefaultFilterChain()
filterManager.AddFilters(defaultFilters...)
```

### 3. 动态添加过滤器
```go
filterManager.AddFilter(FilterTypeForbiddenSponsor)
```

## 扩展指南

### 添加新的过滤器

1. **定义新的过滤器类型**
   在 `filter_interface.go` 中添加新的 `FilterType` 常量

2. **实现过滤器结构体**
   ```go
   type NewFilter struct {
       *BaseFilter
   }
   
   func NewNewFilter(ctx *RequestContext) *NewFilter {
       return &NewFilter{
           BaseFilter: NewBaseFilter(ctx),
       }
   }
   
   func (f *NewFilter) Filter(ctx *RequestContext) FilterType {
       // 实现过滤逻辑
       return FilterTypeNone
   }
   ```

3. **在工厂中注册**
   在 `DefaultFilterFactory.CreateFilter` 方法中添加新的case

### 自定义过滤器工厂

如果需要特殊的过滤器创建逻辑，可以实现自己的工厂：

```go
type CustomFilterFactory struct{}

func (f *CustomFilterFactory) CreateFilter(filterType FilterType, ctx *RequestContext) Filters {
    // 自定义创建逻辑
}
```

## 性能考虑

1. **过滤器顺序**: 将最容易过滤掉广告的过滤器放在前面，减少后续过滤器的执行
2. **缓存机制**: 在具体过滤器实现中可以添加缓存机制
3. **并发处理**: 对于独立的过滤器，可以考虑并发执行

## 文件结构

```
filters/
├── filter_interface.go      # 核心接口和类型定义
├── filter_processor.go      # 过滤器的主入口
├── base_filter.go          # 基础过滤器
├── filter_factory.go       # 过滤器工厂
├── filter_manager.go       # 过滤器管理器
├── campaign_filters.go     # Campaign层级过滤器
├── device_filters.go       # 设备相关过滤器
├── user_filters.go         # 用户定向过滤器
├── creative_filters.go     # 创意相关过滤器
├── ad_frequency.go         # 频控过滤器
├── ad_target.go           # 定向过滤器
├── example_usage.go       # 使用示例
└── README.md              # 说明文档
```

## 注意事项

1. 所有过滤器返回 `FilterTypeNone` 表示通过过滤，返回其他类型表示被过滤
2. 过滤器的执行是短路的，任何一个过滤器返回非None就会被过滤掉
3. 过滤器应该是无状态的，所有状态信息都通过RequestContext传递
4. 在实现具体过滤器时，需要处理好异常情况，避免panic