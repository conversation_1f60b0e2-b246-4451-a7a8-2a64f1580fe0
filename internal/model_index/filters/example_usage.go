package filters

import (
	ctx "rtb_model_server/internal/context"
)

// FilterExample 过滤器使用示例
type FilterExample struct {
	filterManager *FilterManager
}

// NewFilterExample 创建过滤器使用示例
func NewFilterExample() *FilterExample {
	// 创建过滤器工厂
	factory := NewDefaultFilterFactory()

	// 创建过滤器管理器
	filterManager := NewFilterManager(factory)

	return &FilterExample{
		filterManager: filterManager,
	}
}

// SetupDefaultFilters 设置默认过滤器链
func (fe *FilterExample) SetupDefaultFilters() {
	// 使用默认过滤器链
	defaultFilters := GetDefaultFilterChain()
	fe.filterManager.AddFilters(defaultFilters...)
}

// SetupCustomFilters 设置自定义过滤器链
func (fe *FilterExample) SetupCustomFilters() {
	// 自定义过滤器链，只使用部分过滤器
	customFilters := []FilterType{
		// 基础过滤
		FilterTypeDeliveryTime,
		FilterTypeBudgetReason,

		// 定向过滤
		FilterTypeCountryTarget,
		FilterTypeGenderTarget,
		FilterTypeAgeTarget,

		// 频控过滤
		FilterTypeAdClkFrequency,
		FilterTypeAdImpFrequency,
	}

	fe.filterManager.AddFilters(customFilters...)
}

// ProcessAds 处理广告列表的示例
func (fe *FilterExample) ProcessAds(requestCtx *ctx.RequestContext, ads []int32) []FilterResult {
	// 执行过滤
	filteredAds := fe.filterManager.FilterAds(requestCtx, ads)

	// 可以在这里添加其他处理逻辑，如排序、限制数量等

	return filteredAds
}

// AddDynamicFilter 动态添加过滤器的示例
func (fe *FilterExample) AddDynamicFilter(filterType FilterType) {
	fe.filterManager.AddFilter(filterType)
}

// 使用示例函数
func ExampleUsage() {
	// 创建示例实例
	example := NewFilterExample()

	// 设置过滤器链
	example.SetupDefaultFilters()

	// 或者使用自定义过滤器链
	// example.SetupCustomFilters()

	// 动态添加特定过滤器
	example.AddDynamicFilter(FilterTypeForbiddenSponsor)

	// 在实际使用中，你会从请求上下文和广告索引中获取这些数据
	// requestCtx := &ctx.RequestContext{...}
	// ads := []*rtb_model_server.RTBModelServerAdInfo{...}
	//
	// filteredAds := example.ProcessAds(requestCtx, ads)
}
