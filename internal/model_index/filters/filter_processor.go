package filters

import (
	ctx "rtb_model_server/internal/context"
)

// 过滤器的主入口

// FilterProcessor

type FilterProcessor struct {
	filterManager *FilterManager
}

// NewFilterProcessor 创建过滤器使用示例
func NewFilterProcessor() *FilterProcessor {
	// 创建过滤器工厂
	factory := NewDefaultFilterFactory()

	// 创建过滤器管理器
	filterManager := NewFilterManager(factory)

	return &FilterProcessor{
		filterManager: filterManager,
	}
}

// SetupDefaultFilters 设置默认过滤器链
func (fe *FilterProcessor) SetupDefaultFilters() {
	// 使用默认过滤器链
	defaultFilters := GetDefaultFilterChain()
	fe.filterManager.AddFilters(defaultFilters...)
}

// ProcessAds 处理广告列表
func (fe *FilterProcessor) ProcessAllAds(requestCtx *ctx.RequestContext) []FilterResult {
	creativeIds := requestCtx.AdIndex.GetAllCreativeId()
	// 执行过滤
	filteredAds := fe.filterManager.FilterAds(requestCtx, creativeIds)

	return filteredAds
}
