package filters

import (
	"testing"
)

// TestMatchDeviceTarget 测试设备定向匹配逻辑
func TestMatchDeviceTarget(t *testing.T) {
	filter := &DeviceTargetFilter{}

	// 测试用例
	tests := []struct {
		name          string
		deviceId      int32
		deviceTargets []int32
		expected      bool
	}{
		{
			name:          "直接匹配具体设备ID",
			deviceId:      533000001,
			deviceTargets: []int32{533000001, 534000000},
			expected:      true,
		},
		{
			name:          "匹配品牌ID - 华为品牌下的机型",
			deviceId:      533000001,
			deviceTargets: []int32{533000000}, // 华为品牌ID
			expected:      true,
		},
		{
			name:          "匹配品牌ID - 另一个华为机型",
			deviceId:      533000999,
			deviceTargets: []int32{533000000}, // 华为品牌ID
			expected:      true,
		},
		{
			name:          "不匹配 - 不同品牌",
			deviceId:      533000001, // 华为机型
			deviceTargets: []int32{534000000}, // 其他品牌ID
			expected:      false,
		},
		{
			name:          "不匹配 - 具体机型不在列表中",
			deviceId:      533000001,
			deviceTargets: []int32{533000002, 534000001},
			expected:      false,
		},
		{
			name:          "复合匹配 - 包含品牌和具体机型",
			deviceId:      533000001,
			deviceTargets: []int32{534000000, 533000000, 535000001},
			expected:      true,
		},
		{
			name:          "边界测试 - 品牌ID边界",
			deviceId:      533999999, // 华为品牌下最大机型ID
			deviceTargets: []int32{533000000}, // 华为品牌ID
			expected:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := filter.matchDeviceTarget(tt.deviceId, tt.deviceTargets)
			if result != tt.expected {
				t.Errorf("matchDeviceTarget() = %v, expected %v", result, tt.expected)
				t.Errorf("deviceId: %d, deviceTargets: %v", tt.deviceId, tt.deviceTargets)
			}
		})
	}
}

// TestBrandIdCalculation 测试品牌ID计算逻辑
func TestBrandIdCalculation(t *testing.T) {
	tests := []struct {
		deviceId int32
		expected int32
	}{
		{533000001, 533000000}, // 华为机型 -> 华为品牌
		{533000999, 533000000}, // 华为机型 -> 华为品牌
		{533999999, 533000000}, // 华为机型 -> 华为品牌
		{534000001, 534000000}, // 其他品牌机型 -> 其他品牌
		{1000000, 1000000},     // 品牌1机型 -> 品牌1
		{1000001, 1000000},     // 品牌1机型 -> 品牌1
	}

	for _, tt := range tests {
			brandId := (tt.deviceId / 1000000) * 1000000
			if brandId != tt.expected {
				t.Errorf("Brand ID calculation for %d = %d, expected %d", tt.deviceId, brandId, tt.expected)
			}
	}
}