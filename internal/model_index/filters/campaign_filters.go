package filters

import (
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/lib"
	"time"
)

// DeliveryTimeFilter 投放时间过滤器
type DeliveryTimeFilter struct {
	*BaseFilter
}

// NewDeliveryTimeFilter 创建投放时间过滤器
func NewDeliveryTimeFilter(ctx *ctx.RequestContext) *DeliveryTimeFilter {
	return &DeliveryTimeFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行投放时间过滤
func (f *DeliveryTimeFilter) Filter(ctx *ctx.RequestContext) FilterType {
	// 1. 获取当前时间
	campaign := ctx.CurrentFilterCampaign
	strategy := ctx.CurrentFilterStrategy
	currentTime := time.Now()
	currentTs := currentTime.Unix()

	// 检查广告计划的开始和结束时间
	if campaign.BeginTime > 0 && currentTs < campaign.BeginTime {
		return FilterTypeDeliveryTime
	}
	if campaign.EndTime > 0 && currentTs > campaign.EndTime {
		return FilterTypeDeliveryTime
	}

	// 2. 检查分小时定向 (hour_target)
	// 优先检查策略级别的设置，如果没有则检查计划级别
	hourTarget := strategy.HourTarget
	// 如果设置了分小时定向，检查当前小时是否在允许范围内
	if len(hourTarget) > 0 {
		currentHour := currentTime.Hour() // 0-23
		allowed := false
		if lib.InSlice[int32](hourTarget, int32(currentHour)) {
			allowed = true
		}
		if !allowed {
			return FilterTypeDeliveryTime
		}
	}

	// 3. 检查按周几定向 (weekday_target)
	// 优先检查策略级别的设置，如果没有则检查计划级别
	weekdayTarget := strategy.WeekdayTarget
	// 如果设置了按周几定向，检查当前星期几是否在允许范围内
	if len(weekdayTarget) > 0 {
		// Go的Weekday: Sunday=0, Monday=1, ..., Saturday=6
		// 接口定义: 1=周一, 2=周二, ..., 7=周日
		currentWeekday := int(currentTime.Weekday())
		if currentWeekday == 0 { // Sunday
			currentWeekday = 7 // 转换为接口定义的周日=7
		}

		allowed := false
		if lib.InSlice[int32](weekdayTarget, int32(currentWeekday)) {
			allowed = true
		}
		if !allowed {
			return FilterTypeDeliveryTime
		}
	}

	return FilterTypeNone
}

// BudgetReasonFilter 预算过滤器
type BudgetReasonFilter struct {
	*BaseFilter
}

// NewBudgetReasonFilter 创建预算过滤器
func NewBudgetReasonFilter(ctx *ctx.RequestContext) *BudgetReasonFilter {
	return &BudgetReasonFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行预算过滤
func (f *BudgetReasonFilter) Filter(ctx *ctx.RequestContext) FilterType {
	campaign := ctx.CurrentFilterCampaign
	strategy := ctx.CurrentFilterStrategy

	if campaign.DailyBudgetOverTime > 0 || campaign.TotalBudgetOverTime > 0 {
		return FilterTypeBudgetReason
	}
	if strategy.DailyBudgetOverTime > 0 {
		return FilterTypeBudgetReason
	}
	// FIXME: 这里还需要对预算进行控制

	return FilterTypeNone
}

// NoneFilter 无过滤器（默认过滤器）
type NoneFilter struct {
	*BaseFilter
}

// NewNoneFilter 创建无过滤器
func NewNoneFilter(ctx *ctx.RequestContext) *NoneFilter {
	return &NoneFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行无过滤（直接通过）
func (f *NoneFilter) Filter(ctx *ctx.RequestContext) FilterType {
	return FilterTypeNone
}
