package filters

import (
	"fmt"
	ctx "rtb_model_server/internal/context"
	"time"
)

// 频控文档：https://bluefocus.feishu.cn/wiki/EDAywKVXsiP61jkcMJ3can0gnyg

// AdClkFrequencyFilter 点击频控过滤器
type AdClkFrequencyFilter struct {
	*BaseFilter
}

// NewAdClkFrequencyFilter 创建点击频控过滤器
func NewAdClkFrequencyFilter(ctx *ctx.RequestContext) *AdClkFrequencyFilter {
	return &AdClkFrequencyFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行点击频控过滤
func (f *AdClkFrequencyFilter) Filter(ctx *ctx.RequestContext) FilterType {
	sponsorId := ctx.CurrentFilterSponsor.Id
	freqConfigs := ctx.FrequencyProcessor.GetFreqConfigs()
	sponsorFreqConfig, ok := freqConfigs[sponsorId]
	//如果不存在该广告主点击频控配置，则直接返回
	if !ok {
		return FilterTypeNone
	}
	// 加载点击频控数据
	// 结构是 map[string]int， key为sponsor:date value为次数，需要根据sponsorFreqConfig的FreqDays和ClkFreqTarget次数共同判定
	data, err := f.loadClkFreaData(ctx)
	if err != nil {
		return FilterTypeNone
	}
	// 检查点击频控
	if f.checkFrequencyLimit(data, sponsorFreqConfig.FreqDays, sponsorFreqConfig.ClkFreqTarget, sponsorId) {
		return FilterTypeAdClkFrequency
	}
	return FilterTypeNone
}

func (f *AdClkFrequencyFilter) loadClkFreaData(requestCtx *ctx.RequestContext) (map[string]int, error) {
	if requestCtx.ClkFrequencyData == nil {
		t1 := time.Now()
		defer func() {
			requestCtx.AddTimeCost("clk_freq", time.Since(t1).Microseconds())
		}()
		device := requestCtx.BidRequest.Device
		if device != nil {
			// 初始化点击频控和曝光频控
			clkFreqData, err := requestCtx.FrequencyProcessor.GetClkFrequencyData(device.Oaidmd5, device.Gaid, device.Idfamd5)
			if err != nil {
				return nil, err
			}
			requestCtx.ClkFrequencyData = clkFreqData
		} else {
			return nil, fmt.Errorf("device is nil")
		}
	}
	return requestCtx.ClkFrequencyData, nil
}

func (f *AdImpFrequencyFilter) loadImpFreaData(requestCtx *ctx.RequestContext) (map[string]int, error) {
	if requestCtx.ImpFrequencyData == nil {
		t1 := time.Now()
		defer func() {
			requestCtx.AddTimeCost("imp_freq", time.Since(t1).Microseconds())
		}()
		device := requestCtx.BidRequest.Device
		if device != nil {
			// 初始化点击频控和曝光频控
			impFreqData, err := requestCtx.FrequencyProcessor.GetImpFrequencyData(device.Oaidmd5, device.Gaid, device.Idfamd5)
			if err != nil {
				return nil, err
			}
			requestCtx.ImpFrequencyData = impFreqData
		} else {
			return nil, fmt.Errorf("device is nil")
		}
	}
	return requestCtx.ImpFrequencyData, nil
}

// AdImpFrequencyFilter 曝光频控过滤器
type AdImpFrequencyFilter struct {
	*BaseFilter
}

// NewAdImpFrequencyFilter 创建曝光频控过滤器
func NewAdImpFrequencyFilter(ctx *ctx.RequestContext) *AdImpFrequencyFilter {
	return &AdImpFrequencyFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行曝光频控过滤
func (f *AdImpFrequencyFilter) Filter(ctx *ctx.RequestContext) FilterType {
	sponsorId := ctx.CurrentFilterSponsor.Id
	freqConfigs := ctx.FrequencyProcessor.GetFreqConfigs()
	sponsorFreqConfig, ok := freqConfigs[sponsorId]
	//如果不存在该广告主曝光频控配置，则直接返回
	if !ok {
		return FilterTypeNone
	}
	// 加载曝光频控数据
	// 结构是 map[string]int， key为sponsor:date value为次数，需要根据sponsorFreqConfig的FreqDays和ImpFreqTarget次数共同判定
	data, err := f.loadImpFreaData(ctx)
	if err != nil {
		return FilterTypeNone
	}

	// 检查曝光频控
	if f.checkFrequencyLimit(data, sponsorFreqConfig.FreqDays, sponsorFreqConfig.ImpFreqTarget, sponsorId) {
		return FilterTypeAdImpFrequency
	}
	return FilterTypeNone
}

// checkFrequencyLimit 检查频控限制
// data: 频控数据，key为sponsor:date，value为次数
// freqDays: 频控天数
// freqTarget: 频控目标次数
// sponsorId: 广告主ID
func (f *AdClkFrequencyFilter) checkFrequencyLimit(data map[string]int, freqDays, freqTarget int, sponsorId int32) bool {
	if data == nil || len(data) == 0 {
		return false
	}
	if freqTarget <= 0 || freqDays <= 0 {
		return false
	}

	// 计算需要检查的日期范围
	now := time.Now()
	totalCount := 0

	// 检查过去freqDays天的数据
	for i := 0; i < freqDays; i++ {
		date := now.AddDate(0, 0, -i).Format("20060102")
		key := fmt.Sprintf("%d:%s", sponsorId, date)
		if count, exists := data[key]; exists {
			totalCount += count
		}
	}
	// 如果总次数超过或等于目标次数，则触发频控
	return totalCount >= freqTarget
}

// checkFrequencyLimit 检查频控限制（曝光频控版本）
func (f *AdImpFrequencyFilter) checkFrequencyLimit(data map[string]int, freqDays, freqTarget int, sponsorId int32) bool {
	if freqTarget <= 0 || freqDays <= 0 {
		return false
	}

	// 计算需要检查的日期范围
	now := time.Now()
	totalCount := 0

	// 检查过去freqDays天的数据
	for i := 0; i < freqDays; i++ {
		date := now.AddDate(0, 0, -i).Format("20060102")
		key := fmt.Sprintf("%d:%s", sponsorId, date)
		if count, exists := data[key]; exists {
			totalCount += count
		}
	}

	// 如果总次数超过或等于目标次数，则触发频控
	return totalCount >= freqTarget
}
