# Ad Index 过滤器使用指南

## 概述

本系统为每种广告数据类型提供了可自定义的过滤器功能，允许在数据加载时过滤掉异常或不符合条件的数据。

## 支持的数据类型

- **Campaign** (广告活动)
- **Strategy** (投放策略)
- **Creative** (广告创意)
- **Sponsor** (广告主)
- **Promotion** (推广活动)
- **AdTracking** (广告追踪)

## 基本使用

### 1. 使用默认过滤器

每种数据类型都提供了默认的过滤器，会自动过滤掉明显异常的数据：

```go
// 使用默认过滤器加载数据
count, err := adIndex.LoadCampaign()
count, err := adIndex.LoadStrategy()
count, err := adIndex.LoadCreative()
// ... 其他类型
```

### 2. 使用自定义过滤器

可以为每种数据类型提供自定义的过滤器：

```go
// 自定义Campaign过滤器
customCampaignFilter := func(campaign IndexableCampaign) bool {
    if campaign.RTBCampaign == nil {
        return false
    }
    
    // 只加载状态为活跃的campaign
    if campaign.RTBCampaign.Status != nil && *campaign.RTBCampaign.Status != 1 {
        return false
    }
    
    // 只加载未过期的campaign
    if campaign.RTBCampaign.EndTime != nil {
        endTime := time.Unix(*campaign.RTBCampaign.EndTime, 0)
        if endTime.Before(time.Now()) {
            return false
        }
    }
    
    return true
}

// 使用自定义过滤器加载数据
count, err := adIndex.LoadCampaignWithFilter(customCampaignFilter)
```

## 高级用法

### 1. 过滤器链

可以组合多个过滤器：

```go
// 创建过滤器链
chain := NewFilterChain(
    CampaignFilter,           // 默认过滤器
    CombinedCampaignFilter,   // 组合过滤器
    customCampaignFilter,     // 自定义过滤器
)

// 使用过滤器链
count, err := adIndex.LoadCampaignWithFilter(chain.Apply)
```

### 2. 条件过滤器

根据不同条件使用不同的过滤器：

```go
var filter FilterFunc[IndexableCampaign]
if isStrictMode {
    filter = StrictCampaignFilter
} else {
    filter = CampaignFilter
}

count, err := adIndex.LoadCampaignWithFilter(filter)
```

## 过滤器示例

### Campaign 过滤器示例

```go
func CustomCampaignFilter(campaign IndexableCampaign) bool {
    if campaign.RTBCampaign == nil {
        return false
    }
    
    // 过滤条件示例：
    // 1. 状态必须为活跃
    // 2. 必须有有效的开始和结束时间
    // 3. 预算必须大于0
    
    return true
}
```

### Creative 过滤器示例

```go
func CustomCreativeFilter(creative IndexableCreative) bool {
    if creative.RTBCreative == nil {
        return false
    }
    
    // 过滤条件示例：
    // 1. 必须有素材URL
    // 2. 审核状态必须通过
    // 3. 素材格式必须支持
    
    return true
}
```

## 日志和监控

系统会自动记录过滤统计信息：

```
INFO: data filtered during loading
  indexType: campaign
  totalRead: 1000
  filtered: 50
  loaded: 950
```

## 性能考虑

1. **过滤器应该尽可能高效**：避免在过滤器中进行复杂的计算或网络请求
2. **早期过滤**：将最可能过滤掉数据的条件放在前面
3. **日志级别**：在过滤器中使用适当的日志级别，避免过多的日志输出

## 最佳实践

1. **渐进式过滤**：从宽松的过滤条件开始，逐步收紧
2. **测试过滤器**：在生产环境使用前，充分测试过滤器的效果
3. **监控过滤率**：定期检查过滤率，确保不会过度过滤有效数据
4. **文档化**：为自定义过滤器编写清晰的文档说明

## 故障排除

### 数据加载量异常减少

1. 检查过滤器逻辑是否过于严格
2. 查看过滤日志，了解具体的过滤原因
3. 临时使用更宽松的过滤器进行对比

### 性能问题

1. 检查过滤器是否包含耗时操作
2. 优化过滤条件的顺序
3. 考虑使用更简单的过滤逻辑