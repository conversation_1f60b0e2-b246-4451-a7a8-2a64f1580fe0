package filters

import (
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/common/domob_thrift/rtb_types"
	ctx "rtb_model_server/internal/context"
	"testing"
)

func TestTagIdFilter_Filter(t *testing.T) {
	type testCase struct {
		name                string
		tagIdType           int32
		tagIds              []string
		bidAdpIds           []string
		bidAdMatchTypes     []int64
		creativeAdMatchType int64
		expected            FilterType
	}
	tests := []testCase{
		{
			name:                "TagIdType不是1或2，返回FilterTypeNone",
			tagIdType:           3,
			tagIds:              []string{"tag1", "tag2"},
			bidAdpIds:           []string{"tag1"},
			bidAdMatchTypes:     []int64{1, 2},
			creativeAdMatchType: 1,
			expected:            FilterTypeNone,
		},
		{
			name:                "TagIdType为1（相等），AdpId在目标列表中，返回FilterTypeNone",
			tagIdType:           1,
			tagIds:              []string{"tag1", "tag2"},
			bidAdpIds:           []string{"tag1"},
			bidAdMatchTypes:     []int64{1, 2},
			creativeAdMatchType: 1,
			expected:            FilterTypeNone,
		},
		{
			name:                "TagIdType为1（相等），AdpId不在目标列表中，返回FilterTypeTagIdTarget",
			tagIdType:           1,
			tagIds:              []string{"tag1", "tag2"},
			bidAdpIds:           []string{"tag3"},
			bidAdMatchTypes:     []int64{1, 2},
			creativeAdMatchType: 1,
			expected:            FilterTypeTagIdTarget,
		},
		{
			name:                "TagIdType为2（不相等），AdpId不在目标列表中，返回FilterTypeNone",
			tagIdType:           2,
			tagIds:              []string{"tag1", "tag2"},
			bidAdpIds:           []string{"tag3"},
			bidAdMatchTypes:     []int64{1, 2},
			creativeAdMatchType: 1,
			expected:            FilterTypeNone,
		},
		{
			name:                "TagIdType为2（不相等），AdpId在目标列表中，返回FilterTypeTagIdTarget",
			tagIdType:           2,
			tagIds:              []string{"tag1", "tag2"},
			bidAdpIds:           []string{"tag1"},
			bidAdMatchTypes:     []int64{1, 2},
			creativeAdMatchType: 1,
			expected:            FilterTypeTagIdTarget,
		},
		{
			name:                "AdMatchType不匹配，跳过该bid，返回FilterTypeTagIdTarget",
			tagIdType:           1,
			tagIds:              []string{"tag1", "tag2"},
			bidAdpIds:           []string{"tag1"},
			bidAdMatchTypes:     []int64{3, 4}, // 与创意的AdMatchType不匹配
			creativeAdMatchType: 1,
			expected:            FilterTypeTagIdTarget,
		},
		{
			name:                "多个bid，其中一个匹配条件，返回FilterTypeNone",
			tagIdType:           1,
			tagIds:              []string{"tag1", "tag2"},
			bidAdpIds:           []string{"tag3", "tag1"}, // 第二个bid匹配
			bidAdMatchTypes:     []int64{1, 2},
			creativeAdMatchType: 1,
			expected:            FilterTypeNone,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试上下文
			requestCtx := createTestRequestContext(tt.tagIdType, tt.tagIds, tt.bidAdpIds, tt.bidAdMatchTypes, tt.creativeAdMatchType)

			// 创建过滤器
			filter := NewTagIdFilter(requestCtx)

			// 执行过滤
			result := filter.Filter(requestCtx)

			// 验证结果
			if result != tt.expected {
				t.Errorf("Filter() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

// createTestRequestContext 创建测试用的RequestContext
func createTestRequestContext(tagIdType int32, tagIds []string, bidAdpIds []string, bidAdMatchTypes []int64, creativeAdMatchType int64) *ctx.RequestContext {
	// 创建BidRequest
	bidList := make([]*rtb_types.RTBRequestResponseInfo, len(bidAdpIds))
	for i, adpId := range bidAdpIds {
		bidList[i] = &rtb_types.RTBRequestResponseInfo{
			Request: &rtb_types.RTBRequestInfo{
				AdpId:        adpId,
				AdMatchTypes: bidAdMatchTypes,
			},
		}
	}

	bidRequest := &rtb_types.RTBBidRequest{
		BidList: bidList,
	}

	// 创建Strategy
	strategy := &rtb_adinfo_types.RTBStrategy{
		TagidType:   tagIdType,
		TagidTarget: tagIds,
	}

	// 创建Creative
	creative := &rtb_adinfo_types.RTBCreative{
		AdMatchType: creativeAdMatchType,
		Containers: map[int64]*rtb_adinfo_types.RTBContainer{
			creativeAdMatchType: &rtb_adinfo_types.RTBContainer{
				AdMatchType: creativeAdMatchType,
			},
		},
	}

	// 创建RequestContext
	return &ctx.RequestContext{
		BidRequest:            bidRequest,
		CurrentFilterStrategy: strategy,
		CurrentFilterCreative: creative,
	}
}

// TestNewTagIdFilter 测试构造函数
func TestNewTagIdFilter(t *testing.T) {
	requestCtx := &ctx.RequestContext{}
	filter := NewTagIdFilter(requestCtx)

	if filter == nil {
		t.Error("NewTagIdFilter() returned nil")
	}

	if filter.BaseFilter == nil {
		t.Error("BaseFilter is nil")
	}

	if filter.GetContext() != requestCtx {
		t.Error("GetContext() returned wrong context")
	}
}

// TestTagIdFilter_EdgeCases 测试边界情况
func TestTagIdFilter_EdgeCases(t *testing.T) {
	t.Run("空的TagIds列表", func(t *testing.T) {
		requestCtx := createTestRequestContext(1, []string{}, []string{"tag1"}, []int64{1}, 1)
		filter := NewTagIdFilter(requestCtx)
		result := filter.Filter(requestCtx)
		// 空的TagIds列表，AdpId不在列表中，应该返回FilterTypeTagIdTarget
		if result != FilterTypeTagIdTarget {
			t.Errorf("Expected FilterTypeTagIdTarget, got %v", result)
		}
	})

	t.Run("空的BidList", func(t *testing.T) {
		requestCtx := &ctx.RequestContext{
			BidRequest: &rtb_types.RTBBidRequest{
				BidList: []*rtb_types.RTBRequestResponseInfo{},
			},
			CurrentFilterStrategy: &rtb_adinfo_types.RTBStrategy{
				TagidType:   1,
				TagidTarget: []string{"tag1"},
			},
			CurrentFilterCreative: &rtb_adinfo_types.RTBCreative{
				AdMatchType: 1,
			},
		}
		filter := NewTagIdFilter(requestCtx)
		result := filter.Filter(requestCtx)
		// 空的BidList，没有匹配的bid，应该返回FilterTypeTagIdTarget
		if result != FilterTypeTagIdTarget {
			t.Errorf("Expected FilterTypeTagIdTarget, got %v", result)
		}
	})

	t.Run("AdMatchType为0的情况", func(t *testing.T) {
		requestCtx := &ctx.RequestContext{
			BidRequest: &rtb_types.RTBBidRequest{
				BidList: []*rtb_types.RTBRequestResponseInfo{
					{
						Request: &rtb_types.RTBRequestInfo{
							AdpId:        "tag1",
							AdMatchTypes: []int64{1},
						},
					},
				},
			},
			CurrentFilterStrategy: &rtb_adinfo_types.RTBStrategy{
				TagidType:   1,
				TagidTarget: []string{"tag1"},
			},
			CurrentFilterCreative: &rtb_adinfo_types.RTBCreative{
				AdMatchType: 0, // AdMatchType为0，不匹配bid的AdMatchTypes
			},
		}
		filter := NewTagIdFilter(requestCtx)
		result := filter.Filter(requestCtx)
		// AdMatchType为0，不匹配bid的AdMatchTypes，应该返回FilterTypeTagIdTarget
		if result != FilterTypeTagIdTarget {
			t.Errorf("Expected FilterTypeTagIdTarget, got %v", result)
		}
	})
}
