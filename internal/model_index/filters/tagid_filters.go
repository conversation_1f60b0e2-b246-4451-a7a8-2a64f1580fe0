package filters

import (
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/lib"
)

// InstalledAppsFilter 已安装应用过滤器
type TagIdFilter struct {
	*BaseFilter
}

// NewTagIdFilter 创建广告位过滤器
func NewTagIdFilter(ctx *ctx.RequestContext) *TagIdFilter {
	return &TagIdFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行广告位过滤
func (f *TagIdFilter) Filter(ctx *ctx.RequestContext) FilterType {
	strategy := ctx.CurrentFilterStrategy
	creative := ctx.CurrentFilterCreative
	var creativeAdMatchTypes []int64
	for _, container := range creative.Containers {
		creativeAdMatchTypes = append(creativeAdMatchTypes, container.AdMatchType)
	}
	tagIdType := strategy.TagidType
	const (
		TagIdTypeEqual    = 1
		TagIdTypeNotEqual = 2
	)
	// 如果tagIdType不是1或2，直接返回
	if tagIdType != TagIdTypeEqual && tagIdType != TagIdTypeNotEqual {
		return FilterTypeNone
	}
	tagIds := strategy.TagidTarget
	for _, bid := range ctx.BidRequest.BidList {
		// 如果创意的admatchtype不在bid的admatchtype中，寻找下一个BidList
		if !lib.SameElementInSlice[int64](bid.Request.AdMatchTypes, creativeAdMatchTypes) {
			continue
		}
		if tagIdType == TagIdTypeEqual {
			if lib.InSlice[string](tagIds, bid.Request.AdpId) {
				return FilterTypeNone
			}
		}
		if tagIdType == TagIdTypeNotEqual {
			if !lib.InSlice[string](tagIds, bid.Request.AdpId) {
				return FilterTypeNone
			}
		}
	}
	return FilterTypeTagIdTarget
}
