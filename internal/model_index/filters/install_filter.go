package filters

import (
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/lib"
)

// InstalledAppsFilter 已安装应用过滤器
type InstalledAppsFilter struct {
	*BaseFilter
}

// NewInstalledAppsFilter 创建已安装应用过滤器
func NewInstalledAppsFilter(ctx *ctx.RequestContext) *InstalledAppsFilter {
	return &InstalledAppsFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行已安装应用过滤
func (f *InstalledAppsFilter) Filter(ctx *ctx.RequestContext) FilterType {
	strategy := ctx.CurrentFilterStrategy
	packageName := ctx.CurrentFilterPromotion.PackageName

	installApps := ctx.BidRequest.Device.InstallledApps
	switch strategy.InstalledUserFilter {
	case 1:
		// 排除已安装用户，在里面的话就返回屏蔽
		if lib.InSlice[string](installApps, packageName) {
			return FilterTypeInstalledApps
		}

	case 2:
		// 定向已安装用户，不在里面返回屏蔽
		if !lib.InSlice[string](installApps, packageName) {
			return FilterTypeInstalledApps
		}
	}
	// 除此之外就是可以定向
	return FilterTypeNone
}
