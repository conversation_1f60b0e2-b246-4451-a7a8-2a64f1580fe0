package filters

import (
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/lib"
)

// ModelTargetFilter 手机型号定向过滤器
type DeviceTargetFilter struct {
	*BaseFilter
}

// NewModelTargetFilter 创建手机型号定向过滤器
func NewDeviceTargetFilter(ctx *ctx.RequestContext) *DeviceTargetFilter {
	return &DeviceTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行手机型号定向过滤
func (f *DeviceTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	strategy := ctx.CurrentFilterStrategy
	deviceId := ctx.BidRequest.Device.DmDeviceId

	// 如果没有设置设备定向条件，直接通过
	if len(strategy.DeviceTarget) == 0 {
		return FilterTypeNone
	}

	// 执行设备定向匹配
	if f.matchDeviceTarget(deviceId, strategy.DeviceTarget) {
		return FilterTypeNone
	}

	return FilterTypeDeviceTarget
}

// matchDeviceTarget 执行设备定向匹配逻辑
// deviceId: 流量中的设备ID (DmDeviceId)
// deviceTargets: 定向中的设备ID列表
// 匹配规则：
// 1. 如果定向中包含具体的设备ID，直接匹配
// 2. 如果定向中包含品牌ID（末尾6位为000000），则该品牌下的所有机型都匹配
func (f *DeviceTargetFilter) matchDeviceTarget(deviceId int32, deviceTargets []int32) bool {
	// 计算设备所属的品牌ID（将设备ID的后6位置为0）
	brandId := (deviceId / 1000000) * 1000000

	// 遍历定向列表进行匹配
	for _, targetId := range deviceTargets {
		// 直接匹配设备ID
		if targetId == deviceId {
			return true
		}

		// 匹配品牌ID（如果定向ID的后6位是000000，表示品牌定向）
		if targetId%1000000 == 0 && targetId == brandId {
			return true
		}
	}

	return false
}

// OSVersionTargetFilter 系统版本定向过滤器
type OSVersionTargetFilter struct {
	*BaseFilter
}

// NewOSVersionTargetFilter 创建系统版本定向过滤器
func NewOSVersionTargetFilter(ctx *ctx.RequestContext) *OSVersionTargetFilter {
	return &OSVersionTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行系统版本定向过滤
func (f *OSVersionTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	strategy := ctx.CurrentFilterStrategy
	device := ctx.BidRequest.Device

	// 如果没有设置操作系统版本定向条件，直接通过
	if len(strategy.MinOsvTarget) == 0 && len(strategy.MaxOsvTarget) == 0 {
		return FilterTypeNone
	}

	// 获取设备的平台类型和操作系统版本ID
	devicePlatform := device.DmPlatform
	deviceOsId := device.DmOsId

	// 检查最低版本限制
	if len(strategy.MinOsvTarget) > 0 {
		// 如果流量和定向的设备类型匹配，需要看版本号是否符合要求
		if minOsv, exists := strategy.MinOsvTarget[devicePlatform]; exists {
			// 如果设备的操作系统版本低于最低要求版本，过滤掉
			if deviceOsId < minOsv {
				return FilterTypeOSVersionTarget
			}
		} else {
			// 如果流量和定向的设备类型不匹配，则直接返回错误
			return FilterTypeOSVersionTarget
		}
	}

	// 检查最高版本限制
	if len(strategy.MaxOsvTarget) > 0 {
		// 如果流量和定向的设备类型匹配，需要看版本号是否符合要求
		if maxOsv, exists := strategy.MaxOsvTarget[devicePlatform]; exists {
			// 如果设备的操作系统版本高于最高要求版本，过滤掉
			if deviceOsId > maxOsv {
				return FilterTypeOSVersionTarget
			}
		} else {
			// 如果流量和定向的设备类型不匹配，则直接返回错误
			return FilterTypeOSVersionTarget
		}
	}

	// 通过所有版本检查
	return FilterTypeNone
}

// NetworkTypeTargetFilter 网络类型定向过滤器
type NetworkTypeTargetFilter struct {
	*BaseFilter
}

// NewNetworkTypeTargetFilter 创建网络类型定向过滤器
func NewNetworkTypeTargetFilter(ctx *ctx.RequestContext) *NetworkTypeTargetFilter {
	return &NetworkTypeTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行网络类型定向过滤
func (f *NetworkTypeTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	strategy := ctx.CurrentFilterStrategy
	device := ctx.BidRequest.Device
	// 如果定向不为空，且流量能够正常解析该字段的话
	if len(strategy.AccessTarget) > 0 && strategy.AccessTarget[0] > 1 {
		if !lib.InSlice[int32](strategy.AccessTarget, device.DmAccesstypeId) {
			return FilterTypeNetworkTypeTarget
		}
	}
	return FilterTypeNone
}

// CarrierTargetFilter 运营商定向过滤器
type CarrierTargetFilter struct {
	*BaseFilter
}

// NewCarrierTargetFilter 创建运营商定向过滤器
func NewCarrierTargetFilter(ctx *ctx.RequestContext) *CarrierTargetFilter {
	return &CarrierTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行运营商定向过滤
func (f *CarrierTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	strategy := ctx.CurrentFilterStrategy
	device := ctx.BidRequest.Device
	// 如果定向不为空，且流量能够正常解析该字段的话
	if len(strategy.CarrierTarget) > 0 && strategy.CarrierTarget[0] != 0 {
		if !lib.InSlice[int32](strategy.CarrierTarget, device.DmCarrierId) {
			return FilterTypeCarrierTarget
		}
	}
	return FilterTypeNone
}
