package filters

import (
	"errors"
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/zaplog"

	"go.uber.org/zap"
)

// FilterManager 过滤器管理器
type FilterManager struct {
	factory FilterFactory
	filters []FilterType
}

// NewFilterManager 创建过滤器管理器
func NewFilterManager(factory FilterFactory) *FilterManager {
	return &FilterManager{
		factory: factory,
		filters: make([]FilterType, 0),
	}
}

// AddFilter 添加过滤器类型
func (fm *FilterManager) AddFilter(filterType FilterType) {
	fm.filters = append(fm.filters, filterType)
}

// AddFilters 批量添加过滤器类型
func (fm *FilterManager) AddFilters(filterTypes ...FilterType) {
	fm.filters = append(fm.filters, filterTypes...)
}

// FilterAds 对广告列表进行过滤
func (fm *FilterManager) FilterAds(ctx *ctx.RequestContext, creativeIds []int32) []FilterResult {
	if len(fm.filters) == 0 {
		return []FilterResult{}
	}

	filteredAds := make([]FilterResult, 0)

	for _, creativeId := range creativeIds {
		filterType := fm.shouldKeepAd(ctx, creativeId)
		fr := FilterResult{
			FilterType: filterType,
			Cid:        creativeId,
		}
		filteredAds = append(filteredAds, fr)
	}

	return filteredAds
}

// shouldKeepAd 判断广告是否应该保留
func (fm *FilterManager) shouldKeepAd(ctx *ctx.RequestContext, creativeId int32) FilterType {
	// 创建广告特定的上下文
	err := fm.updateAdContext(ctx, creativeId)
	if err != nil {
		zaplog.Logger.Warn("creative has invalid adindex data", zap.Int32("creative_id", creativeId), zap.Error(err))
		return FilterTypeCreateContextException
	}

	// 依次执行所有过滤器
	for _, filterType := range fm.filters {
		filter := fm.factory.CreateFilter(filterType, ctx)
		result := filter.Filter(ctx)
		// 如果任何一个过滤器返回非None类型，说明被过滤掉
		if result != FilterTypeNone {
			return result
		}
	}
	return FilterTypeNone
}

// createAdContext 为特定广告创建上下文
func (fm *FilterManager) updateAdContext(ctx *ctx.RequestContext, creativeId int32) error {
	// 这里可以根据需要扩展上下文，添加当前处理的广告信息
	// 目前直接返回原始上下文
	creative, err := ctx.AdIndex.GetCreative(creativeId)
	if err != nil {
		return err
	}
	strategy, err := ctx.AdIndex.GetStrategy(creative.StrategyId)
	if err != nil {
		return err
	}
	campaign, err := ctx.AdIndex.GetCampaign(creative.CampaignId)
	if err != nil {
		return err
	}
	sponsor, err := ctx.AdIndex.GetSponsor(campaign.SponsorId)
	if err != nil {
		return err
	}
	if len(creative.AdTrackingIds) == 0 {
		return errors.New("filter out by invalid ad-tracking ids")
	}

	tracking, err := ctx.AdIndex.GetAdTracking(creative.AdTrackingIds[0])
	if err != nil {
		return err
	}
	promotion, err := ctx.AdIndex.GetPromotion(campaign.PromotionId)
	if err != nil {
		return err
	}

	ctx.CurrentFilterCampaign = campaign
	ctx.CurrentFilterStrategy = strategy
	ctx.CurrentFilterCreative = creative
	ctx.CurrentFilterPromotion = promotion
	ctx.CurrentFilterSponsor = sponsor
	ctx.CurrentFilterTracking = tracking

	return nil
}

// GetDefaultFilterChain 获取默认的过滤器链
func GetDefaultFilterChain() []FilterType {
	return []FilterType{
		// Creative层级过滤
		// 这里先做创意级别的过滤，这样就只需要先拿创意ID去匹配了
		FilterTypeAdMatchTypeTarget,
		FilterTypeAbility,
		FilterTypeCreativeDimTarget,
		FilterTypeVideoDurationTarget,
		FilterTypeAuditStatus,

		// Strategy层级过滤
		FilterTypeExchangeTarget,
		FilterTypeInstalledApps,
		FilterTypeResourceTarget,
		FilterTypeDMPTarget,
		FilterTypeCountryTarget,
		FilterTypeCityTarget,
		FilterTypeDeviceTarget,
		FilterTypeOSVersionTarget,
		FilterTypeNetworkTypeTarget,
		FilterTypeCarrierTarget,
		FilterTypeExchangeDmpTarget,
		FilterTypeGenderTarget,
		FilterTypeAgeTarget,
		FilterTypeInterestTarget,
		FilterTypeBidTypeTarget,
		FilterTypeTagIdTarget,

		// Campaign层级过滤
		FilterTypeDeliveryTime,
		FilterTypeBudgetReason,

		// 流量相关过滤
		FilterTypeAdClkFrequency,
		FilterTypeAdImpFrequency,
		FilterTypeForbiddenSponsor,
	}
}
