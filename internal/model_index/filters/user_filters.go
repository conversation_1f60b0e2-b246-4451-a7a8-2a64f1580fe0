package filters

import (
	ctx "rtb_model_server/internal/context"
)

// GenderTargetFilter 性别定向过滤器
type GenderTargetFilter struct {
	*BaseFilter
}

// NewGenderTargetFilter 创建性别定向过滤器
func NewGenderTargetFilter(ctx *ctx.RequestContext) *GenderTargetFilter {
	return &GenderTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行性别定向过滤
func (f *GenderTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	// TODO: 实现性别定向过滤逻辑
	strategy := ctx.CurrentFilterStrategy

	// FIXME，暂时不支持定向，等新的编码确认后再做支持
	if len(strategy.UserTagListTarget) > 0 && len(strategy.UserTagListTarget[0]) > 0 {
		return FilterTypeGenderTarget
	}
	return FilterTypeNone
}

// AgeTargetFilter 年龄定向过滤器
type AgeTargetFilter struct {
	*BaseFilter
}

// NewAgeTargetFilter 创建年龄定向过滤器
func NewAgeTargetFilter(ctx *ctx.RequestContext) *AgeTargetFilter {
	return &AgeTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行年龄定向过滤
func (f *AgeTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	// TODO: 实现年龄定向过滤逻辑
	strategy := ctx.CurrentFilterStrategy

	// FIXME，暂时不支持定向，等新的编码确认后再做支持
	if len(strategy.UserTagListTarget) > 0 && len(strategy.UserTagListTarget[0]) > 0 {
		return FilterTypeAgeTarget
	}
	return FilterTypeNone
}

// InterestTargetFilter 兴趣定向过滤器
type InterestTargetFilter struct {
	*BaseFilter
}

// NewInterestTargetFilter 创建兴趣定向过滤器
func NewInterestTargetFilter(ctx *ctx.RequestContext) *InterestTargetFilter {
	return &InterestTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行兴趣定向过滤
func (f *InterestTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	// TODO: 实现兴趣定向过滤逻辑
	strategy := ctx.CurrentFilterStrategy

	// FIXME，暂时不支持定向，等新的编码确认后再做支持
	if len(strategy.UserTagListTarget) > 0 && len(strategy.UserTagListTarget[0]) > 0 {
		return FilterTypeInterestTarget
	}
	return FilterTypeNone
}
