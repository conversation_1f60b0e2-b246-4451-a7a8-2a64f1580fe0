package filters

import (
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/lib"
	"strings"
)

// ForbiddenSponsorFilter RTA过滤器
type ForbiddenSponsorFilter struct {
	*BaseFilter
}

// NewForbiddenSponsorFilter 创建RTA过滤器
func NewForbiddenSponsorFilter(ctx *ctx.RequestContext) *ForbiddenSponsorFilter {
	return &ForbiddenSponsorFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行RTA过滤
func (f *ForbiddenSponsorFilter) Filter(ctx *ctx.RequestContext) FilterType {
	// 1. 获取当前广告的sponsor信息
	req := ctx.BidRequest
	sponsor := ctx.CurrentFilterSponsor
	if req == nil || sponsor == nil {
		return FilterTypeFilterException
	}
	// 2. 检查是否在禁投名单中
	if lib.InSlice[int32](req.ForbiddenSponsorIds, sponsor.Id) {
		return FilterTypeForbiddenSponsor
	}
	// 3. 返回相应的过滤结果
	return FilterTypeNone
}

// ExchangeTargetFilter 交易所ID过滤器
type ExchangeTargetFilter struct {
	*BaseFilter
}

// NewExchangeTargetFilter 创建交易所ID过滤器
func NewExchangeTargetFilter(ctx *ctx.RequestContext) *ExchangeTargetFilter {
	return &ExchangeTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行交易所ID过滤
func (f *ExchangeTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	strategy := ctx.CurrentFilterStrategy
	req := ctx.BidRequest
	if strategy == nil || req == nil {
		return FilterTypeFilterException
	}

	// 检查是否在目标交易所ID列表中

	if !lib.InSlice[int32](strategy.ExchangeTarget, req.ExchangeId) {
		return FilterTypeExchangeTarget
	}
	return FilterTypeNone
}

// CountryTargetFilter 国家定向过滤器
type CountryTargetFilter struct {
	*BaseFilter
}

// NewCountryTargetFilter 创建国家定向过滤器
func NewCountryTargetFilter(ctx *ctx.RequestContext) *CountryTargetFilter {
	return &CountryTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行国家定向过滤
func (f *CountryTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	strategy := ctx.CurrentFilterStrategy
	req := ctx.BidRequest
	if strategy == nil || req == nil || req.Device == nil {
		return FilterTypeFilterException
	}
	// 如果广告没有国家码定向，则认为都可以投放
	if len(strategy.CountryCodeTarget) == 0 {
		return FilterTypeNone
	}
	// 如果广告里有国家码定向

	//当请求中不含国家码
	if strings.TrimSpace(req.Device.CountryCode) == "" {
		return FilterTypeExchangeTarget
	} else {
		// 检查是否在目标交易所ID列表中
		if !lib.InSlice[string](strategy.CountryCodeTarget, req.Device.CountryCode) {
			return FilterTypeExchangeTarget
		}
	}

	return FilterTypeNone
}

//FilterTypeBidTypeTarget

type BidTypeTargetFilter struct {
	*BaseFilter
}

func NewBidTypeTargetFilter(ctx *ctx.RequestContext) *BidTypeTargetFilter {
	return &BidTypeTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 媒体出价类型过滤
func (f *BidTypeTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	req := ctx.BidRequest
	strategy := ctx.CurrentFilterStrategy
	if strategy == nil || req == nil {
		return FilterTypeFilterException
	}
	// 出价类型有一个支持就算支持
	for _, bid := range req.BidList {
		if f.isMediaBidTypeSupported(strategy.MediaBidType, bid.Request.SupportMediaBidType) {
			return FilterTypeNone
		}
	}
	// 如果广告里有出价类型定向
	return FilterTypeBidTypeTarget
}
func (b *BidTypeTargetFilter) isMediaBidTypeSupported(strategyMediaBidType, requestMediaBidType int32) bool {
	// 策略的media_bid_type是位运算方式：1支持CPM，2支持CPC，4支持CPA
	// 3(1+2)支持CPM、CPC，5(1+4)支持CPM、CPA，6(2+4)支持CPC、CPA，7(1+2+4)支持全部
	return (strategyMediaBidType & requestMediaBidType) != 0
}
