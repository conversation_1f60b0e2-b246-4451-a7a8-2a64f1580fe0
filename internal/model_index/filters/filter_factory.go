package filters

import (
	ctx "rtb_model_server/internal/context"
)

// FilterFactory 过滤器工厂接口
type FilterFactory interface {
	CreateFilter(filterType FilterType, ctx *ctx.RequestContext) Filters
}

// DefaultFilterFactory 默认过滤器工厂实现
type DefaultFilterFactory struct{}

// NewDefaultFilterFactory 创建默认过滤器工厂
func NewDefaultFilterFactory() FilterFactory {
	return &DefaultFilterFactory{}
}

// CreateFilter 根据过滤类型创建对应的过滤器
func (f *DefaultFilterFactory) CreateFilter(filterType FilterType, ctx *ctx.RequestContext) Filters {
	switch filterType {
	case FilterTypeDeliveryTime:
		return NewDeliveryTimeFilter(ctx)
	case FilterTypeBudgetReason:
		return NewBudgetReasonFilter(ctx)
	case FilterTypeExchangeTarget:
		return NewExchangeTargetFilter(ctx)
	case FilterTypeInstalledApps:
		return NewInstalledAppsFilter(ctx)
	case FilterTypeResourceTarget:
		return NewResourceTargetFilter(ctx)
	case FilterTypeDMPTarget:
		return NewDMPTargetFilter(ctx)
	case FilterTypeCountryTarget:
		return NewCountryTargetFilter(ctx)
	case FilterTypeCityTarget:
		return NewCityTargetFilter(ctx)
	case FilterTypeDeviceTarget:
		return NewDeviceTargetFilter(ctx)
	case FilterTypeOSVersionTarget:
		return NewOSVersionTargetFilter(ctx)
	case FilterTypeNetworkTypeTarget:
		return NewNetworkTypeTargetFilter(ctx)
	case FilterTypeCarrierTarget:
		return NewCarrierTargetFilter(ctx)
	case FilterTypeExchangeDmpTarget:
		return NewExchangeDmpTargetFilter(ctx)
	case FilterTypeGenderTarget:
		return NewGenderTargetFilter(ctx)
	case FilterTypeAgeTarget:
		return NewAgeTargetFilter(ctx)
	case FilterTypeInterestTarget:
		return NewInterestTargetFilter(ctx)
	case FilterTypeAdMatchTypeTarget:
		return NewAdMatchTypeTargetFilter(ctx)
	case FilterTypeAbility:
		return NewAbilityFilter(ctx)
	case FilterTypeCreativeDimTarget:
		return NewCreativeDimTargetFilter(ctx)
	case FilterTypeVideoDurationTarget:
		return NewVideoDurationTargetFilter(ctx)
	case FilterTypeAdClkFrequency:
		return NewAdClkFrequencyFilter(ctx)
	case FilterTypeAdImpFrequency:
		return NewAdImpFrequencyFilter(ctx)
	case FilterTypeForbiddenSponsor:
		return NewForbiddenSponsorFilter(ctx)
	case FilterTypeAuditStatus:
		return NewAuditStatusFilter(ctx)
	case FilterTypeBidTypeTarget:
		return NewBidTypeTargetFilter(ctx)
	default:
		return NewNoneFilter(ctx)
	}
}
