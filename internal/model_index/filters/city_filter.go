package filters

import (
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/lib"
)

// CityTargetFilter 城市定向过滤器
type CityTargetFilter struct {
	*BaseFilter
}

// NewCityTargetFilter 创建城市定向过滤器
func NewCityTargetFilter(ctx *ctx.RequestContext) *CityTargetFilter {
	return &CityTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行城市定向过滤
func (f *CityTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	strategy := ctx.CurrentFilterStrategy
	userGeoCity := ctx.BidRequest.Device.GeoCity
	if len(strategy.GeoCityTarget) > 0 {
		// 先判断用户是否有城市信息
		if userGeoCity == 0 {
			return FilterTypeCityTarget

		} else if lib.InSlice[int64](strategy.GeoCityTarget, int64(userGeoCity)) {
			// 用户城市在定向城市列表中，直接通过
			return FilterTypeNone
		} else {
			// 用户城市不在定向城市列表中，过滤
			return FilterTypeCityTarget
		}
	}
	return FilterTypeNone
}
