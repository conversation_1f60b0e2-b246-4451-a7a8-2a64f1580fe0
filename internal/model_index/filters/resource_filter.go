package filters

import (
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/lib"
)

// ResourceTargetFilter 资源包过滤器
type ResourceTargetFilter struct {
	*BaseFilter
}

// NewResourceTargetFilter 创建资源包过滤器
func NewResourceTargetFilter(ctx *ctx.RequestContext) *ResourceTargetFilter {
	return &ResourceTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行资源包过滤
func (f *ResourceTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	req := ctx.BidRequest
	if req.App == nil || req.App.DmMediaId == 0 || len(req.BidList) == 0 {
		return FilterTypeNone
	}
	if ctx.ResourceTargetWhiteList == nil || ctx.ResourceTargetBlackList == nil {
		ctx.ResourceTargetWhiteList = make([]int32, 0)
		ctx.ResourceTargetBlackList = make([]int32, 0)
		for _, admt := range req.BidList[0].Request.AdMatchTypes {
			rWhite, rBlack := ctx.ResourceTargetProcessor.FilterByTargets(int64(req.App.DmMediaId), req.AdxExchangeId, req.ExchangeId, admt)
			//将流量命中的资源包加入白名单
			ctx.ResourceTargetWhiteList = lib.MergeSlice[int32](ctx.ResourceTargetWhiteList, rWhite)
			ctx.ResourceTargetBlackList = lib.MergeSlice[int32](ctx.ResourceTargetBlackList, rBlack)
		}
	}
	//检查广告请求是否命中资源包黑名单
	stratgy := ctx.CurrentFilterStrategy

	// 如果推广组没有设定资源包定向，则直接返回，不进行过滤
	if stratgy.ResourceTarget == nil || len(stratgy.ResourceTarget) == 0 {
		return FilterTypeNone
	}
	// 如果存在黑名单中
	if lib.SameElementInSlice(ctx.ResourceTargetBlackList, stratgy.ResourceTarget) {
		return FilterTypeResourceTarget
	}
	// 如果不存在白名单中
	if !lib.SameElementInSlice(ctx.ResourceTargetWhiteList, stratgy.ResourceTarget) {
		return FilterTypeResourceTarget
	}
	return FilterTypeNone
}
