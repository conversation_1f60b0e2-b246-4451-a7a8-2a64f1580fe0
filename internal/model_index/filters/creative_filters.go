package filters

import (
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/lib"
)

// AdMatchTypeTargetFilter 广告位匹配类型过滤器
type AdMatchTypeTargetFilter struct {
	*BaseFilter
}

// NewAdMatchTypeTargetFilter 创建广告位匹配类型过滤器
func NewAdMatchTypeTargetFilter(ctx *ctx.RequestContext) *AdMatchTypeTargetFilter {
	return &AdMatchTypeTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行广告位匹配类型过滤
func (f *AdMatchTypeTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	creative := ctx.CurrentFilterCreative

	var allowBid = false
	if ctx.BidRequest == nil || ctx.BidRequest.BidList == nil {
		return FilterTypeFilterException
	}

	for _, container := range creative.Containers {
		for _, bid := range ctx.BidRequest.BidList {
			if lib.InSlice[int64](bid.Request.AdMatchTypes, container.AdMatchType) {
				allowBid = true
			}
		}
	}
	if allowBid {
		return FilterTypeNone
	}
	return FilterTypeAdMatchTypeTarget
}

// AbilityFilter 请求能力要求过滤器
type AbilityFilter struct {
	*BaseFilter
}

// NewAbilityFilter 创建请求能力要求过滤器
func NewAbilityFilter(ctx *ctx.RequestContext) *AbilityFilter {
	return &AbilityFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行请求能力要求过滤
func (f *AbilityFilter) Filter(ctx *ctx.RequestContext) FilterType {

	creative := ctx.CurrentFilterCreative

	// 流量要求的能力，去看创意是否有
	reqRequestCapabilitys := ctx.BidRequest.RequiredCapabilities
	creativeCapabilitys := creative.Capabilities
	for cap, req := range reqRequestCapabilitys {
		if req {
			if _, ok := creativeCapabilitys[cap]; !ok {
				return FilterTypeAbility
			}
		}
	}

	// 创意要求的能力，去看流量里是否有
	creativeRequestCapabilitys := creative.RequiredCapabilities
	reqCapabilitys := ctx.BidRequest.Capabilities

	for cap, req := range creativeRequestCapabilitys {
		if req {
			if _, ok := reqCapabilitys[cap]; !ok {
				return FilterTypeAbility
			}
		}
	}

	return FilterTypeNone
}

// CreativeDimTargetFilter 创意尺寸定向过滤器
type CreativeDimTargetFilter struct {
	*BaseFilter
}

// NewCreativeDimTargetFilter 创建创意尺寸定向过滤器
func NewCreativeDimTargetFilter(ctx *ctx.RequestContext) *CreativeDimTargetFilter {
	return &CreativeDimTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行创意尺寸定向过滤
func (f *CreativeDimTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	// TODO: 实现创意尺寸定向过滤逻辑
	// 1. 获取请求的广告位尺寸要求
	// 2. 检查创意尺寸是否匹配
	// 3. 返回相应的过滤结果
	return FilterTypeNone
}

// VideoDurationTargetFilter 视频时长定向过滤器
type VideoDurationTargetFilter struct {
	*BaseFilter
}

// NewVideoDurationTargetFilter 创建视频时长定向过滤器
func NewVideoDurationTargetFilter(ctx *ctx.RequestContext) *VideoDurationTargetFilter {
	return &VideoDurationTargetFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行视频时长定向过滤
func (f *VideoDurationTargetFilter) Filter(ctx *ctx.RequestContext) FilterType {
	if len(ctx.BidRequest.BidList) == 0 {
		return FilterTypeFilterException
	}
	bidRequest := ctx.BidRequest.BidList[0]
	creative := ctx.CurrentFilterCreative
	// Load的时候创意Container做过检验了，可以直接用
	container := creative.Containers[0]
	if bidRequest.Request.VideoMinduration > 0 {
		if container.VideoDuration < bidRequest.Request.VideoMinduration {
			return FilterTypeVideoDurationTarget
		}
	}
	if bidRequest.Request.VideoMaxduration > 0 {
		if container.VideoDuration > bidRequest.Request.VideoMaxduration {
			return FilterTypeVideoDurationTarget
		}
	}
	return FilterTypeNone
}

type AuditStatusFilter struct {
	*BaseFilter
}

// NewAuditStatusFilter 创建审核状态过滤器
func NewAuditStatusFilter(ctx *ctx.RequestContext) *AuditStatusFilter {
	return &AuditStatusFilter{
		BaseFilter: NewBaseFilter(ctx),
	}
}

// Filter 执行审核状态过滤
func (f *AuditStatusFilter) Filter(ctx *ctx.RequestContext) FilterType {
	creative := ctx.CurrentFilterCreative
	exchangeId := ctx.BidRequest.ExchangeId

	// 目前快手有审核，ADX没有审核，没有审核记录的则默认审核通过
	for _, auditStatus := range creative.ExchangeAuditStatus {
		if auditStatus.ExchangeId == exchangeId {
			if !lib.InSlice([]rtb_adinfo_types.ExchangeAuditStatus{rtb_adinfo_types.ExchangeAuditStatus_EAS_APPROVED, rtb_adinfo_types.ExchangeAuditStatus_EAS_NEED_NO_AUDIT}, auditStatus.AuditStatus) {
				return FilterTypeAuditStatus
			}
		}
	}
	return FilterTypeNone
}
