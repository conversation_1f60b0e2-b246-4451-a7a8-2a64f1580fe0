package zaplog

import (
	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var Logger *zap.Logger
var FunnelLogger *zap.Logger

type Option struct {
	//服务名称
	ServiceName string
	// 日志文件路径，默认 os.TempDir()
	LogPath string
	// 设置日志级别
	// debug 可以打印出 info debug warn
	// info  级别可以打印 warn info
	// warn  只能打印 warn
	// debug->info->warn->error
	LogLevel string
	// 每个日志文件保存大小（MB），默认 100M
	MaxSize int
	// 保留7天，默认不限
	MaxAgeDays int
	// 保留30个备份，默认不限
	MaxBackups int
	// 是否压缩，默认不压缩
	Compress bool
}

func InitLogger(option Option) {
	Logger = initLogger(option)
}

func InitFunnelLogger(option Option) {
	FunnelLogger = initLogger(option)
}

func initLogger(option Option) *zap.Logger {
	// 日志分割
	hook := lumberjack.Logger{
		Filename:   option.LogPath,    // 日志文件路径，默认 os.TempDir()
		MaxSize:    option.MaxSize,    // 每个日志文件保存10M，默认 100M
		MaxBackups: option.MaxBackups, // 保留30个备份，默认不限
		MaxAge:     option.MaxAgeDays, // 保留7天，默认不限
		LocalTime:  true,
		Compress:   option.Compress, // 是否压缩，默认不压缩
	}
	write := zapcore.AddSync(&hook)
	// 设置日志级别
	// debug 可以打印出 info debug warn
	// info  级别可以打印 warn info
	// warn  只能打印 warn
	// debug->info->warn->error
	var level zapcore.Level
	switch option.LogLevel {
	case "debug":
		level = zap.DebugLevel
	case "info":
		level = zap.InfoLevel
	case "error":
		level = zap.ErrorLevel
	default:
		level = zap.InfoLevel
	}
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "linenum",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,  // 小写编码器
		EncodeTime:     zapcore.ISO8601TimeEncoder,     // ISO8601 UTC 时间格式
		EncodeDuration: zapcore.SecondsDurationEncoder, //
		EncodeCaller:   zapcore.FullCallerEncoder,      // 全路径编码器
		EncodeName:     zapcore.FullNameEncoder,
	}
	// 设置日志级别
	atomicLevel := zap.NewAtomicLevel()
	atomicLevel.SetLevel(level)
	core := zapcore.NewCore(
		// zapcore.NewConsoleEncoder(encoderConfig),
		zapcore.NewJSONEncoder(encoderConfig),
		// zapcore.NewMultiWriteSyncer(zapcore.AddSync(os.Stdout), zapcore.AddSync(&write)), // 打印到控制台和文件
		write,
		level,
	)
	// 开启开发模式，堆栈跟踪
	//caller := zap.AddCaller()
	// 开启文件及行号
	//development := zap.Development()
	// 设置初始化字段,如：添加一个服务器名称
	//filed := zap.Fields(zap.String("serviceName", option.ServiceName))
	// 构造日志
	return zap.New(core)
}
