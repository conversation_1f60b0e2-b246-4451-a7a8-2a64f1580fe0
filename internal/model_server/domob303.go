package model_server

import (
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/conf"
)

//Standard base service

// ATTENTION 这里方法不做实现，dm303已经不再使用了

// Returns a descriptive name of the service
func (s ModelServerHandler) GetName() (r string, err error) {
	return conf.GlobalConfig.Server.Name, nil
}

// Returns the version of the service
func (s ModelServerHandler) GetVersion() (r string, err error) {
	return "", nil
}

// Gets the status of this service
func (s ModelServerHandler) GetStatus() (r dm303.DmStatus, err error) {
	return dm303.DmStatus(0), nil
}

// User friendly description of status, such as why the service is in
// the dead or warning state, or what is being started or stopped.
func (s ModelServerHandler) GetStatusDetails() (r string, err error) {
	return "", nil
}

// Gets the counters for this service
func (s ModelServerHandler) GetCounters() (r map[string]int64, err error) {
	return nil, nil
}

// Gets the map of counters for this service
func (s ModelServerHandler) GetMapCounters() (r map[string]map[string]int64, err error) {
	return nil, nil
}

// Gets the value of a single counter
//
// Parameters:
//   - Key
func (s ModelServerHandler) GetCounter(key string) (r int64, err error) {
	return 0, nil
}

// Sets an option
//
// Parameters:
//   - Key
//   - Value
func (s ModelServerHandler) SetOption(key string, value string) (err error) {
	return nil
}

// Gets an option
//
// Parameters:
//   - Key
func (s ModelServerHandler) GetOption(key string) (r string, err error) {
	return "", nil
}

// Gets all options
func (s ModelServerHandler) GetOptions() (r map[string]string, err error) {
	return nil, nil
}

// Returns a CPU profile over the given time interval (client and server
// must agree on the profile format).
//
// Parameters:
//   - ProfileDurationInSec
func (s ModelServerHandler) GetCpuProfile(profileDurationInSec int32) (r string, err error) {
	return "", nil
}

// Returns the unix time that the server has been running since
func (s ModelServerHandler) AliveSince() (r int64, err error) {
	return 0, nil
}

// Tell the server to reload its configuration, reopen log files, etc
func (s ModelServerHandler) Reinitialize() (err error) {
	return nil
}

// Suggest a shutdown to the server
func (s ModelServerHandler) Shutdown() (err error) {
	return nil
}
