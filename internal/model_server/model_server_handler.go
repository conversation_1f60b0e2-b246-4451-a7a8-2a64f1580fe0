package model_server

import (
	"fmt"
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/internal/zaplog"
	"time"

	"rtb_model_server/common/domob_thrift/rtb_model_server"

	up "rtb_model_server/internal/user_profile"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type ModelServerHandler struct {
}

// Parameters:
//   - BidRequest
//   - AdMap
func (s ModelServerHandler) GetAdBid(bid_request *rtb_types.RTBBidRequest, ad_map map[rtb_types.AdPlacementId][]*rtb_model_server.RTBModelServerAdInfo) (r *rtb_model_server.RTBModelServerResponse, err error) {
	return nil, nil
}

// Parameters:
//   - BidRequest
func (s ModelServerHandler) GetAd(req *rtb_types.RTBBidRequest) (r *rtb_model_server.RTBModelServerResponse, err error) {
	t1 := time.Now()
	resp := &rtb_model_server.RTBModelServerResponse{}
	gModelIndexManager := GetGlobalModelIndexManger()
	runtimeCtx := gModelIndexManager.CreateContext(req, resp)

	defer func() {
		runtimeCtx.AddTimeCost("total", time.Since(t1).Microseconds())
		zaplog.Logger.Info("request", zap.String("ctx", runtimeCtx.String()))
		if runtimeCtx.EnableFunnelLog {
			zaplog.FunnelLogger.Info("funnel log", zap.Any("data", runtimeCtx.FullLog()))
		}
	}()

	// 执行用户画像填充

	err = up.GetUserProfileProcessor().Process(runtimeCtx)
	runtimeCtx.AddTimeCost("up", time.Since(t1).Microseconds())
	if err != nil {
		return nil, errors.Wrap(err, "fill user profile error")
	}

	fmt.Println(runtimeCtx.BidRequest.Device)

	// 执行广告索引预过滤
	preRecallCreatives := gModelIndexManager.PreFilter(runtimeCtx)

	// 执行预算控制
	budgetControlResult, err := gModelIndexManager.BudgetControl(runtimeCtx, preRecallCreatives)
	if err != nil {
		return nil, errors.Wrap(err, "budget control error")
	}

	// 执行预估
	predictResults, err := gModelIndexManager.Predict(runtimeCtx, budgetControlResult)
	if err != nil {
		return nil, errors.Wrap(err, "predict error")
	}

	// 执行广告决策
	cidInfo, err := gModelIndexManager.BidDecision(runtimeCtx, predictResults)
	if err != nil {
		return nil, errors.Wrap(err, "bid decision error")
	}

	// 对于决策后的结果进行日志打印
	zaplog.Logger.Debug("BidDecision", zap.Any("BidResult", cidInfo))

	// 执行广告渲染
	resp, err = gModelIndexManager.Render(runtimeCtx, cidInfo)
	if err != nil {
		return nil, errors.Wrap(err, "render error")
	}
	zaplog.Logger.Debug("BidDecision", zap.Any("BidResult", cidInfo))
	return resp, nil
}

// Parameters:
//   - BidRequest
func (s ModelServerHandler) GetAdForDebug(bid_request *rtb_types.RTBBidRequest) (r *rtb_model_server.RTBModelServerResponseForDebug, err error) {
	return nil, nil
}
