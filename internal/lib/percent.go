package lib

import (
	"crypto/md5"
	"math/big"
)

// Hash takes a string and returns a big number.
func Hash(input string) *big.Int {
	data := md5.Sum([]byte(input))
	return new(big.Int).SetBytes(data[:])
}

// IsInPercentage takes a string and a percentage (between 0 and 100), returns true if the hash of the string is within the specified percentage.
func IsInPercentage(s string, percentage int64) bool {
	hash := Hash(s)
	mod := new(big.Int).Mod(hash, big.NewInt(100))
	return mod.Int64() < percentage
}
