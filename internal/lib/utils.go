package lib

import (
	"crypto/md5"
	"fmt"
	"io"
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/internal/zaplog"
	"runtime"
	"sort"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

func GenMd5(str string) string {
	w := md5.New()
	io.WriteString(w, str)
	md5str := fmt.Sprintf("%x", w.Sum(nil))
	return md5str
}

// 对map按key进行升序排序
func sortMap(m map[string]string) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	values := make([]string, len(keys))
	for i, k := range keys {
		values[i] = m[k]
	}

	return values
}

// 错误日志
func ErrorLog(isWarning bool, msg string, fields ...zap.Field) {
	if isWarning {
		// prometheus监控

	}
	// 记录日志
	zaplog.Logger.Error(msg, fields...)
}

// panic详细信息日志
func PanicLogDetail() {
	// 获取堆栈信息
	stackBuf := make([]byte, 4096)
	stackLen := runtime.Stack(stackBuf, false)
	stackTrace := strings.TrimRight(string(stackBuf[:stackLen]), "\n")

	// 替换/t字符串
	stackTrace = strings.Replace(stackTrace, "\t", " ", -1)
	split := strings.Split(stackTrace, "\n")
	for i, s := range split {
		// 输出每一行panic信息到日志
		ErrorLog(true, "panic stackTrace Info", zap.Any("stackTrace"+strconv.Itoa(i), s))
	}
}

func InSlice[T comparable](slice []T, val T) bool {
	for _, item := range slice {
		if item == val {
			return true
		}
	}
	return false
}

func SameElementInSlice[T comparable](slice1 []T, slice2 []T) bool {
	for _, item := range slice2 {
		if InSlice(slice1, item) {
			return true
		}
	}
	return false
}

func MergeSlice[T comparable](slice1 []T, slice2 []T) []T {
	for _, item := range slice2 {
		if !InSlice(slice1, item) {
			slice1 = append(slice1, item)
		}
	}
	return slice1
}

func GetDidFromDevice(device *rtb_types.RTBDeviceInfo) string {
	if device.Oaidmd5 != "" {
		return device.Oaidmd5
	}
	if device.Idfamd5 != "" {
		return device.Idfamd5
	}
	if device.Gaid != "" {
		return device.Gaid
	}
	return ""
}
