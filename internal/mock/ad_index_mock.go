package mock

import (
	"fmt"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
)

// MockAdIndex 模拟广告索引，用于测试
type MockAdIndex struct {
	creatives map[int32]*rtb_adinfo_types.RTBCreative
	strategies map[int32]*rtb_adinfo_types.RTBStrategy
	campaigns map[int32]*rtb_adinfo_types.RTBCampaign
	campaignBudgets map[int32]int64
	strategyBudgets map[int32]int64
}

// NewMockAdIndex 创建模拟广告索引
func NewMockAdIndex() *MockAdIndex {
	return &MockAdIndex{
		creatives: make(map[int32]*rtb_adinfo_types.RTBCreative),
		strategies: make(map[int32]*rtb_adinfo_types.RTBStrategy),
		campaigns: make(map[int32]*rtb_adinfo_types.RTBCampaign),
		campaignBudgets: make(map[int32]int64),
		strategyBudgets: make(map[int32]int64),
	}
}

// GetCreative 获取创意信息
func (m *MockAdIndex) GetCreative(creativeId int32) (*rtb_adinfo_types.RTBCreative, error) {
	if creative, exists := m.creatives[creativeId]; exists {
		return creative, nil
	}
	
	// 返回默认的模拟创意
	return &rtb_adinfo_types.RTBCreative{
		Id:          creativeId,
		StrategyId:  creativeId + 1000, // 策略ID = 创意ID + 1000
		CampaignId:  creativeId + 2000, // 活动ID = 创意ID + 2000
		AdMatchType: 1,                 // 默认广告匹配类型
		Status:      0,                 // 正常状态
	}, nil
}

// GetStrategy 获取策略信息
func (m *MockAdIndex) GetStrategy(strategyId int32) (*rtb_adinfo_types.RTBStrategy, error) {
	if strategy, exists := m.strategies[strategyId]; exists {
		return strategy, nil
	}
	
	// 返回默认的模拟策略
	return &rtb_adinfo_types.RTBStrategy{
		Id:         strategyId,
		CampaignId: strategyId + 1000, // 活动ID = 策略ID + 1000
		CostType:   1,                 // 默认计费类型：CPM
		Status:     0,                 // 正常状态
	}, nil
}

// GetCampaign 获取活动信息
func (m *MockAdIndex) GetCampaign(campaignId int32) (*rtb_adinfo_types.RTBCampaign, error) {
	if campaign, exists := m.campaigns[campaignId]; exists {
		return campaign, nil
	}
	
	// 返回默认的模拟活动
	return &rtb_adinfo_types.RTBCampaign{
		Id:                campaignId,
		SponsorId:         campaignId + 100, // 广告主ID = 活动ID + 100
		ActualDailyBudget: 100000,           // 默认日预算：1000元
		Status:            0,                // 正常状态
	}, nil
}

// GetCampaignBudget 获取活动预算
func (m *MockAdIndex) GetCampaignBudget(campaignId int32) (int64, bool) {
	if budget, exists := m.campaignBudgets[campaignId]; exists {
		return budget, true
	}
	// 返回默认预算
	return 100000, true // 默认1000元
}

// GetStrategyBudget 获取策略预算
func (m *MockAdIndex) GetStrategyBudget(strategyId int32) (int64, bool) {
	if budget, exists := m.strategyBudgets[strategyId]; exists {
		return budget, true
	}
	// 返回默认预算
	return 50000, false // 默认500元
}

// SetCreative 设置创意信息（用于测试）
func (m *MockAdIndex) SetCreative(creativeId int32, creative *rtb_adinfo_types.RTBCreative) {
	m.creatives[creativeId] = creative
}

// SetStrategy 设置策略信息（用于测试）
func (m *MockAdIndex) SetStrategy(strategyId int32, strategy *rtb_adinfo_types.RTBStrategy) {
	m.strategies[strategyId] = strategy
}

// SetCampaign 设置活动信息（用于测试）
func (m *MockAdIndex) SetCampaign(campaignId int32, campaign *rtb_adinfo_types.RTBCampaign) {
	m.campaigns[campaignId] = campaign
}

// SetCampaignBudget 设置活动预算（用于测试）
func (m *MockAdIndex) SetCampaignBudget(campaignId int32, budget int64) {
	m.campaignBudgets[campaignId] = budget
}

// SetStrategyBudget 设置策略预算（用于测试）
func (m *MockAdIndex) SetStrategyBudget(strategyId int32, budget int64) {
	m.strategyBudgets[strategyId] = budget
}

// CreateTestData 创建测试数据
func (m *MockAdIndex) CreateTestData() {
	// 创建CPC广告测试数据
	m.SetCreative(1001, &rtb_adinfo_types.RTBCreative{
		Id:          1001,
		StrategyId:  2001,
		CampaignId:  3001,
		AdMatchType: 1, // 普通广告
		Status:      0,
	})
	m.SetStrategy(2001, &rtb_adinfo_types.RTBStrategy{
		Id:         2001,
		CampaignId: 3001,
		CostType:   2, // CPC
		Status:     0,
	})
	m.SetCampaign(3001, &rtb_adinfo_types.RTBCampaign{
		Id:                3001,
		SponsorId:         4001,
		ActualDailyBudget: 200000, // 2000元
		Status:            0,
	})
	m.SetStrategyBudget(2001, 100000) // 1000元
	m.SetCampaignBudget(3001, 200000) // 2000元

	// 创建唤醒类广告测试数据
	m.SetCreative(1002, &rtb_adinfo_types.RTBCreative{
		Id:          1002,
		StrategyId:  2002,
		CampaignId:  3002,
		AdMatchType: 8, // 唤醒类广告
		Status:      0,
	})
	m.SetStrategy(2002, &rtb_adinfo_types.RTBStrategy{
		Id:         2002,
		CampaignId: 3002,
		CostType:   1, // CPM
		Status:     0,
	})
	m.SetCampaign(3002, &rtb_adinfo_types.RTBCampaign{
		Id:                3002,
		SponsorId:         4002,
		ActualDailyBudget: 150000, // 1500元
		Status:            0,
	})
	m.SetStrategyBudget(2002, 75000)  // 750元
	m.SetCampaignBudget(3002, 150000) // 1500元

	// 创建普通广告测试数据
	m.SetCreative(1003, &rtb_adinfo_types.RTBCreative{
		Id:          1003,
		StrategyId:  2003,
		CampaignId:  3003,
		AdMatchType: 1, // 普通广告
		Status:      0,
	})
	m.SetStrategy(2003, &rtb_adinfo_types.RTBStrategy{
		Id:         2003,
		CampaignId: 3003,
		CostType:   1, // CPM
		Status:     0,
	})
	m.SetCampaign(3003, &rtb_adinfo_types.RTBCampaign{
		Id:                3003,
		SponsorId:         4003,
		ActualDailyBudget: 80000, // 800元
		Status:            0,
	})
	m.SetStrategyBudget(2003, 40000) // 400元
	m.SetCampaignBudget(3003, 80000) // 800元
}

// GetSponsor 获取广告主信息（模拟实现）
func (m *MockAdIndex) GetSponsor(sponsorId int32) (*rtb_adinfo_types.RTBSponsor, error) {
	return &rtb_adinfo_types.RTBSponsor{
		Id:     sponsorId,
		Status: 0, // 正常状态
	}, nil
}

// GetPromotion 获取推广信息（模拟实现）
func (m *MockAdIndex) GetPromotion(promotionId int32) (*rtb_adinfo_types.RTBPromotion, error) {
	return nil, fmt.Errorf("promotion %d not found", promotionId)
}

// GetAdTracking 获取追踪信息（模拟实现）
func (m *MockAdIndex) GetAdTracking(trackingId int32) (*rtb_adinfo_types.AdTrackingInfo, error) {
	return nil, fmt.Errorf("ad tracking %d not found", trackingId)
}