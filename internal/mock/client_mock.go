package mock

import (
	"encoding/json"
	"fmt"
	"log"
	"rtb_model_server/common/domob_thrift/rtb_model_server"
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/conf"

	"git.apache.org/thrift.git/lib/go/thrift"
	"github.com/google/uuid"
)

func MockRequest() {
	// 创建传输层和协议层
	transport, err := thrift.NewTSocket(fmt.Sprintf(":%d", conf.GlobalConfig.Server.Port))
	if err != nil {
		log.Fatalf("Error creating socket: %v", err)
	}

	transportFactory := thrift.NewTBufferedTransportFactory(8192)
	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()

	// 创建客户端
	clientTransport := transportFactory.GetTransport(transport)
	defer clientTransport.Close()

	if err := clientTransport.Open(); err != nil {
		log.Fatalf("Error opening connection: %v", err)
	}

	client := rtb_model_server.NewRTBModelServerClientFactory(clientTransport, protocolFactory)
	req := buildBidReuqest()
	bReq, _ := json.Marshal(req)
	log.Printf("req: %s\n", string(bReq))
	result, err2 := client.GetAd(req)
	if err2 != nil {
		log.Fatalf("Error calling service: %v", err2)
	}
	bResp, _ := json.Marshal(result)
	log.Printf("resp: %s\n", string(bResp))
}

func buildBidReuqest() *rtb_types.RTBBidRequest {
	return &rtb_types.RTBBidRequest{
		ReqId:    uuid.New().String(),
		SearchId: 12346,
		UiName:   "mock-ui",

		ExchangeId: 132,
		Device: &rtb_types.RTBDeviceInfo{
			Ip:         "************",
			DmPlatform: 1,
			Oaidmd5:    "7815696ecbf1c96e6894b779456d3301",
			InstallledApps: []string{
				"com.eg.android.AlipayGphone",
				//"com.taotao.taobao",
			},
			UserAgent: "Mozilla/5.0 (Linux; Android 11; Samsung SM-G998B)",
		},
		App: &rtb_types.RTBAppInfo{
			DmMediaId: 12313,
			AppBundle: "com.eg.android",
		},
		BidList: []*rtb_types.RTBRequestResponseInfo{
			{
				Request: &rtb_types.RTBRequestInfo{

					SearchImpId:         123467,
					ImpId:               "xxxxxxx-imp01",
					AdMatchTypes:        []int64{1320000000020},
					SupportMediaBidType: 2,
				},
			},
		},
		RequiredCapabilities: map[int32]bool{1002: true},
		Capabilities:         map[int32]bool{2001: true},
	}
}
