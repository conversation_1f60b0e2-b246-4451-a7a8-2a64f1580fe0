package context

import (
	"encoding/json"
)

// 此处定义漏斗的结构
type FunnelData struct {
	FunnelId      int32           `json:"fid"`             // 命中的漏斗ID
	Date          int32           `json:"date"`            // 日期
	Hour          int32           `json:"hour"`            // 小时
	RequestId     string          `json:"rid"`             // 请求ID
	SearchId      int64           `json:"sid"`             // 会话ID
	UiName        string          `json:"ui"`              // 用户标识
	Timing        *FunnelTiming   `json:"timing"`          // 时间统计
	ExchangeId    int32           `json:"exchange_id"`     // 交易所ID
	AdxExchangeId int32           `json:"adx_exchange_id"` // ADX交易所ID
	DmMediaId     int32           `json:"media_id"`        // 媒体ID
	AppBundle     string          `json:"pkg"`             // 应用包名
	Idfa          string          `json:"idfa"`            // iOS广告标识符
	Idfamd5       string          `json:"ifamd5"`          // iOS广告标识符MD5
	Oaidmd5       string          `json:"oaidmd5"`         // Android开放广告标识符MD5
	Gaid          string          `json:"gaid"`            // Google广告ID
	Platform      int32           `json:"platform"`        // 平台类型
	Os            int32           `json:"os"`              // 操作系统
	Access        int32           `json:"access"`          // 接入方式
	City          int32           `json:"city"`            // 城市
	Carrier       int32           `json:"carrier"`         // 运营商
	Request       *FunnelRequest  `json:"req"`             // 请求信息
	Response      *FunnelResponse `json:"resp"`            // 响应信息
	RuntimeData   *RuntimeData    `json:"runtime"`         // 运行时数据
}

type RuntimeData struct {
	// 创意过滤原因
	CreativeFilterMap map[int32]int32 `json:"creative_filter"`
	// 定向召回的创意ID
	RecallCreatives []int32 `json:"recall_creatives"`
	//此次请求命中的DMP列表
	DeviceDmpTarget []int32 `json:"device_dmp_target"`
	//此次请求命中的资源包黑白名单
	ResourceTargetWhiteList []int32 `json:"resource_target_white_list"`
	ResourceTargetBlackList []int32 `json:"resource_target_black_list"`
	// 该设备拉起的点击频控数据
	ClkFrequencyData map[string]int `json:"clk_frequency_data"`
	// 该设备拉起的曝光频控数据
	ImpFrequencyData map[string]int `json:"imp_frequency_data"`
}

// 时间统计信息
type FunnelTiming struct {
	Total         int `json:"tot"`      // 总耗时
	UserProfile   int `json:"up"`       // 用户画像耗时
	Filter        int `json:"filter"`   // 过滤耗时
	Dmp           int `json:"dmp"`      // DMP耗时
	ClkFrequency  int `json:"clk_freq"` // 频控耗时
	ImpFrequency  int `json:"imp_freq"` // 频控耗时
	PredictServer int `json:"predict"`  // 预测耗时
	Sort          int `json:"sort"`     // 排序耗时
	Render        int `json:"render"`   // 渲染耗时
}

// 请求信息
type FunnelRequest struct {
	ReqAds []*FunnelRequestAd `json:"req_ads"` // 请求广告列表
}

// 请求广告信息
type FunnelRequestAd struct {
	Idx          int     `json:"idx"`            // 广告位索引
	ImpId        string  `json:"imp_id"`         // 曝光ID
	AdMatchType  []int64 `json:"adm"`            // 广告素材ID列表
	BidFloor     int64   `json:"bidf"`           // 竞价标识
	CpcBidFloor  int64   `json:"cpc_bidf"`       // CPC竞价标识
	MediaBidType int32   `json:"media_bid_type"` // 媒体竞价类型
	DealFloor    int64   `json:"dealf"`          // 交易标识
	DealId       string  `json:"deal_id"`        // 交易ID
}

// 响应信息
type FunnelResponse struct {
	RespAds []*FunnelResponseAd `json:"resp_ads"` // 响应广告列表
}

// 响应广告信息
type FunnelResponseAd struct {
	Idx   int                   `json:"idx"`    // 广告位索引
	AdNum int                   `json:"ad_num"` // 广告数量
	ImpId int                   `json:"imp_id"` // 曝光ID
	Ads   *FunnelResponseAdInfo `json:"ads"`    // 广告详情
}

// 响应广告详细信息
type FunnelResponseAdInfo struct {
	Index    int32 `json:"idx"`       // idx
	Cid      int32 `json:"cid"`       // 创意ID
	Sid      int32 `json:"sid"`       // 策略ID
	Pid      int32 `json:"pid"`       // 计划ID
	Uid      int32 `json:"uid"`       // 用户ID
	Bid      int64 `json:"bid"`       // 出价
	Ctr      int64 `json:"ctr"`       // 点击率
	Funneld  int32 `json:"funneld"`   // 漏斗配置ID
	Atr      int64 `json:"atr"`       // 转化率
	Adm      int64 `json:"adm"`       // 广告素材ID
	BidType  int32 `json:"bid_type"`  // 出价类型
	FinPrice int64 `json:"fin_price"` // 最终价格
}

func NewFunnelData(ctx *RequestContext) *FunnelData {
	if ctx == nil || ctx.BidRequest == nil {
		return &FunnelData{}
	}

	req := ctx.BidRequest
	t1 := ctx.TimeStart
	funnelData := &FunnelData{
		FunnelId:      ctx.EnableDeviceFunnelLogFid,
		Date:          int32(t1.Year())*10000 + int32(t1.Month())*100 + int32(t1.Day()),
		Hour:          int32(t1.Hour()),
		RequestId:     req.ReqId,
		SearchId:      req.SearchId,
		UiName:        req.UiName,
		ExchangeId:    req.ExchangeId,
		AdxExchangeId: req.AdxExchangeId,
		Os:            req.Device.DmOsId,
		Timing:        &FunnelTiming{},
		Request:       &FunnelRequest{},
		Response:      &FunnelResponse{},
		RuntimeData:   &RuntimeData{},
	}

	// 从App信息中获取字段
	if req.App != nil {
		funnelData.DmMediaId = req.App.DmMediaId
		funnelData.AppBundle = req.App.AppBundle
	}

	// 从Device信息中获取字段
	if req.Device != nil {
		funnelData.Idfa = req.Device.Idfa
		funnelData.Idfamd5 = req.Device.Idfamd5
		funnelData.Oaidmd5 = req.Device.Oaidmd5
		funnelData.Gaid = req.Device.Gaid
		funnelData.Platform = req.Device.DmPlatform
		funnelData.Os = req.Device.DmOsId
		funnelData.Access = req.Device.DmAccesstypeId
		funnelData.City = req.Device.GeoCity
		funnelData.Carrier = req.Device.DmCarrierId
	}

	// 填充请求广告信息
	if req.BidList != nil {
		funnelData.Request.ReqAds = make([]*FunnelRequestAd, len(req.BidList))
		for i, bidInfo := range req.BidList {
			if bidInfo.Request != nil {
				funnelData.Request.ReqAds[i] = &FunnelRequestAd{
					Idx:          i,
					ImpId:        bidInfo.Request.ImpId,
					AdMatchType:  bidInfo.Request.AdMatchTypes,
					BidFloor:     bidInfo.Request.Bidfloor,
					CpcBidFloor:  bidInfo.Request.CpcBidfloor,
					MediaBidType: bidInfo.Request.SupportMediaBidType,
					DealFloor:    bidInfo.Request.Dealfloor,
					DealId:       bidInfo.Request.DealId,
				}
			} else {
				funnelData.Request.ReqAds[i] = &FunnelRequestAd{Idx: i}
			}
		}
	}

	// 填充RuntimeData
	funnelData.FillRuntimeData(ctx)
	funnelData.FillResponse(ctx)
	funnelData.FillTiming(ctx)

	return funnelData
}

// convertInt64SliceToIntSlice 将int64切片转换为int切片
func convertInt64SliceToIntSlice(int64Slice []int64) []int {
	if int64Slice == nil {
		return nil
	}
	intSlice := make([]int, len(int64Slice))
	for i, v := range int64Slice {
		intSlice[i] = int(v)
	}
	return intSlice
}

// FillRuntimeData 填充运行时数据
func (d *FunnelData) FillRuntimeData(ctx *RequestContext) {
	if d == nil || d.RuntimeData == nil || ctx == nil {
		return
	}

	// 初始化map
	d.RuntimeData.CreativeFilterMap = make(map[int32]int32)
	d.RuntimeData.ClkFrequencyData = make(map[string]int)
	d.RuntimeData.ImpFrequencyData = make(map[string]int)

	// 从RequestContext中获取数据
	if ctx.CreativeFilterMap != nil {
		for k, v := range ctx.CreativeFilterMap {
			d.RuntimeData.CreativeFilterMap[k] = v
		}
	}

	if ctx.RecallCreatives != nil {
		d.RuntimeData.RecallCreatives = make([]int32, len(ctx.RecallCreatives))
		copy(d.RuntimeData.RecallCreatives, ctx.RecallCreatives)
	}

	if ctx.DeviceDmpTarget != nil {
		d.RuntimeData.DeviceDmpTarget = make([]int32, len(ctx.DeviceDmpTarget))
		copy(d.RuntimeData.DeviceDmpTarget, ctx.DeviceDmpTarget)
	}

	if ctx.ResourceTargetWhiteList != nil {
		d.RuntimeData.ResourceTargetWhiteList = make([]int32, len(ctx.ResourceTargetWhiteList))
		copy(d.RuntimeData.ResourceTargetWhiteList, ctx.ResourceTargetWhiteList)
	}

	if ctx.ResourceTargetBlackList != nil {
		d.RuntimeData.ResourceTargetBlackList = make([]int32, len(ctx.ResourceTargetBlackList))
		copy(d.RuntimeData.ResourceTargetBlackList, ctx.ResourceTargetBlackList)
	}

	if ctx.ClkFrequencyData != nil {
		for k, v := range ctx.ClkFrequencyData {
			d.RuntimeData.ClkFrequencyData[k] = v
		}
	}

	if ctx.ImpFrequencyData != nil {
		for k, v := range ctx.ImpFrequencyData {
			d.RuntimeData.ImpFrequencyData[k] = v
		}
	}
}

// FillResponse 填充响应数据
func (d *FunnelData) FillResponse(ctx *RequestContext) {
	if d == nil || d.Response == nil || ctx == nil || ctx.BidResponse == nil {
		return
	}

	response := ctx.BidResponse
	if response.ResponseList == nil {
		return
	}

	// 初始化响应广告列表
	d.Response.RespAds = make([]*FunnelResponseAd, 0)

	// 遍历响应中的广告信息
	for idx, responseInfo := range response.ResponseList {
		if responseInfo == nil || responseInfo.AdList == nil {
			continue
		}

		respAd := &FunnelResponseAd{
			Idx:   idx,
			AdNum: len(responseInfo.AdList),
			ImpId: int(responseInfo.SearchImpId),
		}

		// 填充第一个广告的详情（如果存在）
		if len(responseInfo.AdList) > 0 {
			adInfo := responseInfo.AdList[0]
			respAd.Ads = &FunnelResponseAdInfo{
				Index:    int32(idx),
				Cid:      adInfo.CreativeId,
				Sid:      adInfo.StrategyId,
				Pid:      adInfo.CampaignId, // 使用CampaignId作为计划ID
				Uid:      adInfo.SponsorId,  // 使用SponsorId作为用户ID
				Bid:      adInfo.Bid,
				Ctr:      int64(adInfo.Ctr),
				Atr:      int64(adInfo.Atr),
				Adm:      adInfo.AdMatchType,
				BidType:  0, // RTBModelServerAdInfo中没有BidType字段，设为0
				FinPrice: adInfo.FinPrice,
			}
		} else {
			// 如果没有广告信息，创建空的广告详情
			respAd.Ads = &FunnelResponseAdInfo{
				Index: int32(idx),
			}
		}

		d.Response.RespAds = append(d.Response.RespAds, respAd)
	}
}

// FillTiming 填充时间统计数据
func (d *FunnelData) FillTiming(ctx *RequestContext) {
	if d == nil || d.Timing == nil || ctx == nil {
		return
	}

	// 从RequestContext的TimeCost中获取时间统计
	if ctx.TimeCost != nil {
		if total, ok := ctx.TimeCost["total"]; ok {
			d.Timing.Total = int(total)
		}
		if filter, ok := ctx.TimeCost["filter"]; ok {
			d.Timing.Filter = int(filter)
		}
		if dmp, ok := ctx.TimeCost["dmp"]; ok {
			d.Timing.Dmp = int(dmp)
		}
		if freq, ok := ctx.TimeCost["clk_freq"]; ok {
			d.Timing.ClkFrequency = int(freq)
		}
		if freq, ok := ctx.TimeCost["imp_freq"]; ok {
			d.Timing.ImpFrequency = int(freq)
		}
		if predict, ok := ctx.TimeCost["predict"]; ok {
			d.Timing.PredictServer = int(predict)
		}
		if sort, ok := ctx.TimeCost["sort"]; ok {
			d.Timing.Sort = int(sort)
		}
		if render, ok := ctx.TimeCost["render"]; ok {
			d.Timing.Render = int(render)
		}
		if up, ok := ctx.TimeCost["up"]; ok {
			d.Timing.UserProfile = int(up)
		}
	}
}

// ToJson 转为Json字符串
func (d *FunnelData) ToJson() string {
	if d == nil {
		return "{\"error\":\"funnel data is nil\"}"
	}

	jsonBytes, err := json.Marshal(d)
	if err != nil {
		return "{\"error\":\"funnel data marshal error\"}"
	}
	return string(jsonBytes)
}
