package main

import (
	"flag"
	"log"
	"os"
	"rtb_model_server/conf"
	"rtb_model_server/internal/mock"
	"rtb_model_server/internal/model_server"
	"rtb_model_server/internal/zaplog"
	"strings"
)

var __version__ = "__BUILD_VERSION__"

func main() {
	configFile := flag.String("c", "conf/rtb_model_server.yaml", "default conf/rtb_frequency_control.yaml")
	pv := flag.Bool("v", false, "print version")
	client := flag.Bool("client", false, "client test")
	predict := flag.Bool("predict", false, "predict test server")
	flag.Parse()
	log.Println("Using config file: " + *configFile)
	err := conf.ConfigInit(*configFile)
	if err != nil {
		log.Fatal("config init error", err)
	}
	log.Println("Current version: " + *&__version__)
	if *pv {
		log.Println("current run version:" + __version__)
		os.Exit(0)
	}
	zaplog.InitLogger(getLogOption())
	zaplog.InitFunnelLogger(getFunnelLogOption())

	// 测试请求
	if *client {
		mock.MockRequest()
		return
	}
	if *predict {
		s := mock.PredictServer{}
		s.Start()
		return
	}

	// 启动model-server
	ms := model_server.NewModelServer()
	err = ms.Start()
	if err != nil {
		log.Fatal("model-server start error ", err)
	}
}

func getLogOption() zaplog.Option {
	config := conf.GlobalConfig.LogConfig
	hostName, _ := os.Hostname()
	return zaplog.Option{
		ServiceName: string(conf.GlobalConfig.Server.Name),
		LogPath:     strings.Replace(config.LogPath, "__POD__", hostName, -1),
		LogLevel:    config.LogLevel,
		MaxSize:     config.MaxSize,
		MaxAgeDays:  config.MaxAgeDays,
		MaxBackups:  config.MaxBackups,
		Compress:    config.Compress,
	}
}

func getFunnelLogOption() zaplog.Option {
	config := conf.GlobalConfig.FunnelLogConfig
	hostName, _ := os.Hostname()
	return zaplog.Option{
		ServiceName: string(conf.GlobalConfig.Server.Name),
		LogPath:     strings.Replace(config.LogPath, "__POD__", hostName, -1),
		LogLevel:    config.LogLevel,
		MaxSize:     config.MaxSize,
		MaxAgeDays:  config.MaxAgeDays,
		MaxBackups:  config.MaxBackups,
		Compress:    config.Compress,
	}
}
