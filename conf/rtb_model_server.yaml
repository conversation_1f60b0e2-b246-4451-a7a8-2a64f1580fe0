Server:
  Name: rtb_model_server
  Port: 3398

PredictServer:
  Addr: "127.0.0.1:3399"  # 明确指定本地地址，避免DNS解析
  ModelName: "default_model"
  PoolSize: 10        # 连接池最大连接数
  MaxIdleConns: 5     # 最大空闲连接数
  ConnTimeout: 1000   # 连接超时时间(毫秒) - 降低到1秒

LogConfig:
  LogPath: log/rtb_model_sever.log
  LogLevel: debug
  MaxAgeDays: 7
  MaxSize: 100
  MaxBackups: 100
  Compress: false

FunnelLogConfig:
  LogPath: log/rtb_funnel.__POD__
  LogLevel: info
  MaxAgeDays: 7
  MaxSize: 100
  MaxBackups: 100
  Compress: false

FunnelConfig:
  # 设备级别漏斗文件
  ConfigFilePath: data/resource/funnel.data
  # 设备级别全量漏斗日志文件刷新时间
  Interval: 300
  # 漏斗日志开启比例，单位万分之一，范围是0-10000
  FunnelOpenRatio: 10000



ModelIndex:
  AdIndexPath:
    AdCampaign: data/adindex/ad_campaign.data
    AdStrategy: data/adindex/ad_strategy.data
    AdCreative: data/adindex/ad_creative.data
    AdSponsor: data/adindex/ad_sponsor.data
    AdPromotion: data/adindex/ad_promotion.data
    AdTracking: data/adindex/ad_tracking.data
  ResourceTargetPath: data/resource/resource-target.data
  IP2RegionDbPath: data/ip2region/ip2region.xdb
  DeviceMappingPath: data/device/device_mapping.txt
  AdIndexFileExpireMinutes: 1000  # 广告索引文件过期时间（分钟）

BudgetConfig:
  StatsFilePath: data/budget/stats.data
  SpeedStopRate: 0.01
  SmallBudgetThreshold: 1000000000
  BudgetThresholdRatio: 0.01
  MaxBidPerMinute: 1000000
  DecelerationFactor: 0.01
  DefaultMediaPrice: 1000000000
  StatsReloadInterval: 300

FrequencyConfig:
  LoopInterval: 300
  FrequencyKey: FC
  FrequencyKeyCount: 2
  ClkFrequencyKeyPrefix: clk
  ImpFrequencyKeyPrefix: imp

DmpTargetRedisPool:
  Addr: "localhost:6379"
  Db: 0
  AuthInfo: ""
  PoolSize: 10
  DialTimeout: 3000
  ReadTimeout: 3000
  WriteTimeout: 3000

FrequencyRedisPool:
  Addr: "localhost:6379"
  Db: 0
  AuthInfo: ""
  PoolSize: 10
  DialTimeout: 3000
  ReadTimeout: 3000
  WriteTimeout: 3000
